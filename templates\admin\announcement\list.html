{% extends "base.html" %}

{% block title %}Manage Announcements - Footprints{% endblock %}

{% block content %}
<div class="container">
  <div class="row align-items-center mb-4">
    <div class="col-md-6 mb-3 mb-md-0">
      <h1 class="display-6 fw-bold">
        <span class="position-relative">
          Announcement Management
          <span class="position-absolute start-0 bottom-0"
            style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
        </span>
      </h1>
    </div>
    <div class="col-md-6">
      <div class="d-flex flex-column flex-sm-row gap-2 justify-content-md-end">
        <a href="{{ url_for('announcement.get_announcements') }}"
          class="btn btn-outline-dark rounded-pill px-4 py-2 d-flex align-items-center justify-content-center">
          <i class="bi bi-eye me-2"></i> View Announcements
        </a>
        <button type="button"
          class="btn btn-dark rounded-pill px-4 py-2 d-flex align-items-center justify-content-center"
          id="createAnnouncementBtn">
          <i class="bi bi-plus me-2"></i> Add announcement
        </button>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold mb-0">Total ({{ total_count|default(0) }})</h5>
  </div>

  {% if announcements %}
  <div class="table-responsive">
    <table class="table align-middle table-hover">
      <thead>
        <tr>
          <th style="width: 50px;">No.</th>
          <th>Title</th>
          <th>Content</th>
          <th style="width: 120px;">Author</th>
          <th style="width: 120px;">Created</th>
          <th style="width: 120px;">Updated</th>
          <th style="width: 150px;">Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for announcement in announcements %}
        <tr>
          <td class="text-center">{{ (page - 1) * 10 + loop.index }}</td>
          <td class="text-truncate" style="max-width: 200px;">{{ announcement.title }}</td>
          <td>
            {% if announcement.content %}
            <div class="small text-truncate" style="max-width: 300px;">{{ announcement.content }}</div>
            {% else %}
            <span class="text-muted">-</span>
            {% endif %}
          </td>
          <td class="text-truncate" style="max-width: 120px;">{{ announcement.username }}</td>
          <td class="text-nowrap">{{ announcement.created_at.strftime('%Y-%m-%d') }}</td>
          <td class="text-nowrap">{{ announcement.updated_at.strftime('%Y-%m-%d') if announcement.updated_at else
            'Never' }}</td>
          <td>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-sm btn-outline-dark edit-announcement-btn"
                data-id="{{ announcement.id }}" data-title="{{ announcement.title }}"
                data-content="{{ announcement.content }}">Edit</button>
              <button type="button" class="btn btn-sm btn-outline-dark delete-announcement-btn"
                data-id="{{ announcement.id }}" data-title="{{ announcement.title }}">Delete</button>
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>

  <!-- Pagination styled like My Journeys page -->
  <nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center"
      style="--bs-pagination-active-bg: #000; --bs-pagination-active-border-color: #000; --bs-pagination-color: #000; --bs-pagination-hover-color: #000; --bs-pagination-focus-color: #000;">
      {% if page > 1 %}
      <li class="page-item">
        <a class="page-link border-0" href="{{ url_for('announcement.get_all_announcements', page=page-1) }}">«</a>
      </li>
      {% endif %}

      {% for p in range(1, total_pages + 1) %}
      {% if p == page %}
      <li class="page-item active">
        <span class="page-link border-0">{{ p }}</span>
      </li>
      {% else %}
      <li class="page-item">
        <a class="page-link border-0" href="{{ url_for('announcement.get_all_announcements', page=p) }}">{{ p }}</a>
      </li>
      {% endif %}
      {% endfor %}

      {% if page < total_pages %} <li class="page-item">
        <a class="page-link border-0" href="{{ url_for('announcement.get_all_announcements', page=page+1) }}">»</a>
        </li>
        {% endif %}
    </ul>
  </nav>
  {% else %}
  <div class="alert alert-info">
    <p>No announcements have been created yet.</p>
    <button type="button" class="btn btn-primary" id="createFirstAnnouncementBtn">Create Your First
      Announcement</button>
  </div>
  {% endif %}
</div>

<!-- Hidden form for DELETE request -->
<form id="deleteAnnouncementForm" method="POST" style="display: none;">
  <input type="hidden" name="_method" value="DELETE">
</form>

{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Create Announcement Modal
    const createAnnouncementBtn = document.getElementById('createAnnouncementBtn');
    const createFirstAnnouncementBtn = document.getElementById('createFirstAnnouncementBtn');

    function showCreateAnnouncementModal() {
      const form = `
        <form id="createAnnouncementForm" method="POST" action="{{ url_for('announcement.create_announcement') }}" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" required>
            <div class="invalid-feedback">Please provide a title.</div>
          </div>
          <div class="mb-3">
            <label for="content" class="form-label">Content</label>
            <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
            <div class="invalid-feedback">Please provide content for the announcement.</div>
          </div>
        </form>
      `;

      showModal('Create New Announcement', form, {
        actionText: 'Create',
        onAction: () => {
          const form = document.getElementById('createAnnouncementForm');
          form.classList.add('was-validated');

          if (!form.checkValidity()) {
            // Focus the first invalid field instead of showing browser popup
            const firstInvalidField = form.querySelector(':invalid');
            if (firstInvalidField) {
              firstInvalidField.focus();
            }
            return false; // Prevent modal from closing
          }

          form.submit();
          return true; // Allow modal to close
        }
      });
    }

    if (createAnnouncementBtn) {
      createAnnouncementBtn.addEventListener('click', showCreateAnnouncementModal);
    }
    if (createFirstAnnouncementBtn) {
      createFirstAnnouncementBtn.addEventListener('click', showCreateAnnouncementModal);
    }

    // Edit Announcement Modal
    const editButtons = document.querySelectorAll('.edit-announcement-btn');
    editButtons.forEach(button => {
      button.addEventListener('click', function () {
        const id = this.getAttribute('data-id');
        const title = this.getAttribute('data-title');
        const content = this.getAttribute('data-content');

        const form = `
          <form id="editAnnouncementForm" method="POST" action="/announcement/${id}/edit">
            <div class="mb-3">
              <label for="title" class="form-label">Title</label>
              <input type="text" class="form-control" id="title" name="title" value="${title}" required>
            </div>
            <div class="mb-3">
              <label for="content" class="form-label">Content</label>
              <textarea class="form-control" id="content" name="content" rows="5" required>${content}</textarea>
            </div>
          </form>
        `;

        showModal('Edit Announcement', form, {
          actionText: 'Update',
          onAction: () => {
            const form = document.getElementById('editAnnouncementForm');
            form.submit();
          }
        });
      });
    });

    // Delete Announcement Modal
    const deleteButtons = document.querySelectorAll('.delete-announcement-btn');
    deleteButtons.forEach(button => {
      button.addEventListener('click', function () {
        const id = this.getAttribute('data-id');
        const title = this.getAttribute('data-title');

        showModal('Delete Announcement',
          `<p>Are you sure you want to delete this announcement?</p>`,
          {
            actionText: 'Delete',
            onAction: () => {
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = `/announcement/${id}/delete`;
              document.body.appendChild(form);
              form.submit();
            }
          }
        );
      });
    });
  });
</script>
{% endblock %}