from data.journey_data import (
    create_journey,
    get_journey,
    get_private_journeys,
    get_public_journeys,
    get_public_journey_count,
    get_hidden_journeys,
    update_journey,
    update_journey_status,
    update_journey_hidden_status,
    delete_journey,
    search_my_journeys,
    search_journeys,
    search_journeys_by_location,
    count_journey_events,
)

# Test create_journey
def test_create_journey(app, mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=None)
    result = create_journey(1, "New Journey", "Journey Description", "2025-01-01")
    assert result == None
    mock_execute_query.assert_called_once_with(
    """
    INSERT INTO journeys
    (user_id, title, description, start_date, is_public)
    VALUES (%s, %s, %s, %s, %s)
    """,
    (1, "New Journey", "Journey Description", "2025-01-01", False)
)


# Test get_journey
def test_get_journey(app, mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'id': 1, 'title': 'Test Journey'})
    result = get_journey(1)
    assert result == {'id': 1, 'title': 'Test Journey'}
    mock_execute_query.assert_called_once_with(
    """
    SELECT j.*, u.username, u.profile_image,
           (SELECT e.photo FROM events e 
            WHERE e.journey_id = j.id AND e.photo IS NOT NULL AND e.photo != '' 
            ORDER BY e.created_at ASC LIMIT 1) as event_photo
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.id = %s
    """,
    (1,),
    fetch_one=True
)



# Test get_private_journeys
def test_get_private_journeys(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[{'id': 1, 'title': 'Test Journey'}])
    result = get_private_journeys(1)
    assert result == [{'id': 1, 'title': 'Test Journey'}]
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.profile_image, 
           (SELECT e.photo FROM events e WHERE e.journey_id = j.id AND e.photo IS NOT NULL AND e.photo != '' ORDER BY e.created_at ASC LIMIT 1) AS event_photo,
           (SELECT COUNT(*) FROM events e WHERE e.journey_id = j.id) AS event_count
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.user_id = %s
    ORDER BY j.start_date DESC
    """,
        (1,),
        fetch_all=True 
    )


# Test get_public_journeys
def test_get_public_journeys(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[{'id': 1, 'title': 'Public Journey'}])
    result = get_public_journeys(10, 0)
    assert result == [{'id': 1, 'title': 'Public Journey'}]
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           (SELECT e.photo FROM events e 
            WHERE e.journey_id = j.id AND e.photo IS NOT NULL AND e.photo != '' 
            ORDER BY e.created_at ASC LIMIT 1) as event_photo
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.is_public = TRUE AND j.is_hidden = FALSE
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (10, 0),
        fetch_all=True 
    )


# Test update_journey
def test_update_journey(app, mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    result = update_journey(1, title="Updated Journey", description="Updated Description")
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET title = %s, description = %s
    WHERE id = %s
    """,
        ['Updated Journey', 'Updated Description', 1]
    )


# Test update_journey_status
def test_update_journey_status(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    result = update_journey_status(1, True)
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET is_public = %s
    WHERE id = %s
    """,
        (True, 1)
    )


# Test delete_journey
def test_delete_journey(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    result = delete_journey(1)
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    DELETE FROM journeys
    WHERE id = %s
    """,
        (1,)
    )


# Test search_my_journeys
def test_search_my_journeys(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[{'id': 1, 'title': 'Test Journey'}])
    result = search_my_journeys(1, "Test")
    assert result == [{'id': 1, 'title': 'Test Journey'}]
    mock_execute_query.assert_called_once_with(
        """
    SELECT DISTINCT j.*, 
           u.username, 
           u.first_name, 
           u.last_name,
           (SELECT e.photo 
            FROM events e 
            WHERE e.journey_id = j.id 
              AND e.photo IS NOT NULL 
              AND e.photo != '' 
            ORDER BY e.created_at ASC LIMIT 1) AS event_photo,
           (SELECT COUNT(*) 
            FROM events e 
            WHERE e.journey_id = j.id) AS event_count
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    LEFT JOIN events e ON j.id = e.journey_id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE j.user_id = %s
      AND j.is_hidden = FALSE
      AND (
        j.title LIKE %s OR 
        j.description LIKE %s OR
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
      )
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (1, '%Test%', '%Test%', '%Test%', 50, 0, ),
        fetch_all=True,
    )

# Test get_public_journey_count
def test_get_public_journey_count(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 5})
    result = get_public_journey_count()
    assert result == 5
    mock_execute_query.assert_called_once_with(
        """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE is_public = TRUE AND is_hidden = FALSE
    """,
        fetch_one=True
    )


# Test get_hidden_journeys
def test_get_hidden_journeys(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[{'id': 1, 'title': 'Hidden Journey'}])
    result = get_hidden_journeys()
    assert result == [{'id': 1, 'title': 'Hidden Journey'}]
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.is_hidden = TRUE
    ORDER BY j.user_id, j.updated_at DESC
    """,
        fetch_all=True
    )


# Test count_journey_events
def test_count_journey_events(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 3})
    result = count_journey_events(1)
    assert result == 3
    mock_execute_query.assert_called_once_with(
        """
    SELECT COUNT(*) as count
    FROM events
    WHERE journey_id = %s
    """,
        (1,),
        fetch_one=True
    )


# Test update_journey_hidden_status
def test_update_journey_hidden_status(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    result = update_journey_hidden_status(1, True)
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET is_hidden = %s
    WHERE id = %s
    """,
        (True, 1)
    )


# Test search_journeys
def test_search_journeys(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[{'id': 1, 'title': 'Test Journey'}])
    result = search_journeys("Test")
    assert result == [{'id': 1, 'title': 'Test Journey'}]
    mock_execute_query.assert_called_once_with(
       """
    SELECT DISTINCT j.*, u.username, u.first_name, u.last_name,  u.profile_image,
           (SELECT e.photo FROM events e 
            WHERE e.journey_id = j.id AND e.photo IS NOT NULL AND e.photo != '' 
            ORDER BY e.created_at ASC LIMIT 1) as event_photo
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    LEFT JOIN events e ON j.id = e.journey_id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE j.is_public = TRUE AND j.is_hidden = FALSE
      AND (
        j.title LIKE %s OR 
        j.description LIKE %s OR
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
      )
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        ('%Test%', '%Test%', '%Test%', 50, 0),
        fetch_all=True
    )



# Test search_journeys_by_location
def test_search_journeys_by_location(mocker):
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[{'id': 1, 'title': 'Journey Near Beach'}])
    result = search_journeys_by_location("Beach")
    assert result == [{'id': 1, 'title': 'Journey Near Beach'}]
    mock_execute_query.assert_called_once_with(
       """
    SELECT DISTINCT j.*, u.username, u.first_name, u.last_name
    FROM journeys j
    JOIN events e ON j.id = e.journey_id
    JOIN users u ON j.user_id = u.id
    WHERE j.is_public = TRUE AND j.is_hidden = FALSE
      AND e.location_id = %s
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        ('Beach', 50, 0),
        fetch_all=True
    )
