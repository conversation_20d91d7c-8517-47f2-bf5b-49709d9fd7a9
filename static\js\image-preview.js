/**
 * Image Preview Utility
 * Provides centralized image preview functionality for file uploads
 * 
 * Dependencies: file-validation.js, Bootstrap 5
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

class ImagePreview {
    constructor(options = {}) {
        this.options = {
            // Container selectors
            containerSelector: '#imagePreviewContainer',
            gridSelector: '#previewGrid',
            feedbackSelector: '#imagesFeedback',
            
            // Preview settings
            maxFiles: 10,
            allowMultiple: true,
            showFileInfo: true,
            showRemoveButton: true,
            showClearAll: true,
            
            // Styling
            cardClass: 'preview-card',
            errorClass: 'preview-error',
            imageHeight: '120px',
            
            // Grid layout
            colClass: 'col-6 col-md-4',
            
            // Callbacks
            onFileSelect: null,
            onFileRemove: null,
            onValidationError: null,
            onClear: null,
            
            ...options
        };
        
        this.files = [];
        this.container = null;
        this.grid = null;
        this.input = null;
        
        this.init();
    }
    
    init() {
        // Initialize file validation if not already done
        if (window.FileValidation && typeof window.FileValidation.init === 'function') {
            window.FileValidation.init().catch(console.error);
        }
    }
    
    /**
     * Setup preview for a file input
     * @param {HTMLInputElement} input - The file input element
     * @param {Object} options - Override options for this instance
     */
    setupInput(input, options = {}) {
        if (!input || input.type !== 'file') {
            console.error('ImagePreview: Invalid file input provided');
            return;
        }
        
        // Merge options
        this.options = { ...this.options, ...options };
        this.input = input;
        
        // Get containers
        this.container = document.querySelector(this.options.containerSelector);
        this.grid = document.querySelector(this.options.gridSelector);
        
        if (!this.container || !this.grid) {
            console.error('ImagePreview: Preview containers not found');
            return;
        }
        
        // Setup event listener
        input.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Setup premium detection
        this.isPremium = input.getAttribute('data-premium') === 'true';
        this.options.maxFiles = this.isPremium ? this.options.maxFiles : 1;
        
        return this;
    }
    
    /**
     * Handle file selection
     * @param {Event} event - Change event from file input
     */
    handleFileSelect(event) {
        const files = Array.from(event.target.files || []);
        
        // Clear previous state
        this.clearValidation();
        
        if (files.length === 0) {
            this.hidePreview();
            return;
        }
        
        // Validate file count
        if (files.length > this.options.maxFiles) {
            this.showValidationError(
                `You can only upload ${this.options.maxFiles} image${this.options.maxFiles > 1 ? 's' : ''} at a time.`
            );
            this.hidePreview();
            return;
        }
        
        // Process files
        this.files = files;
        this.generatePreviews(files);
        
        // Callback
        if (this.options.onFileSelect) {
            this.options.onFileSelect(files);
        }
    }
    
    /**
     * Generate preview cards for files
     * @param {Array} files - Array of File objects
     */
    generatePreviews(files) {
        // Clear previous previews
        this.grid.innerHTML = '';
        
        // Show container
        this.showPreview();
        
        // Process each file
        files.forEach((file, index) => {
            this.createPreviewCard(file, index);
        });
    }
    
    /**
     * Create a preview card for a single file
     * @param {File} file - The file to preview
     * @param {number} index - File index
     */
    createPreviewCard(file, index) {
        // Validate file
        const validation = this.validateFile(file);
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const card = this.createCardElement(file, index, validation, e.target.result);
            this.grid.appendChild(card);
        };
        
        reader.readAsDataURL(file);
    }
    
    /**
     * Create card DOM element
     * @param {File} file - The file
     * @param {number} index - File index
     * @param {Object} validation - Validation result
     * @param {string} dataUrl - Base64 data URL
     * @returns {HTMLElement} Card element
     */
    createCardElement(file, index, validation, dataUrl) {
        const col = document.createElement('div');
        col.className = this.options.colClass;
        
        const cardClass = validation.valid ? 
            this.options.cardClass : 
            `${this.options.cardClass} ${this.options.errorClass}`;
        
        const removeButton = this.options.showRemoveButton ? 
            `<button type="button" class="preview-remove" onclick="window.imagePreviewInstance.removeFile(${index})" title="Remove image">
                <i class="bi bi-x"></i>
            </button>` : '';
        
        const fileInfo = this.options.showFileInfo ? 
            `<div class="preview-info">
                <p class="preview-filename" title="${file.name}">${file.name}</p>
                <p class="preview-filesize">${(file.size / (1024 * 1024)).toFixed(2)} MB</p>
                ${!validation.valid ? `<p class="error-message">${validation.message}</p>` : ''}
            </div>` : '';
        
        col.innerHTML = `
            <div class="${cardClass}">
                <img src="${dataUrl}" class="preview-image" alt="Preview ${index + 1}" style="height: ${this.options.imageHeight}; object-fit: cover;">
                ${removeButton}
                ${fileInfo}
            </div>
        `;
        
        return col;
    }
    
    /**
     * Validate a single file
     * @param {File} file - File to validate
     * @returns {Object} Validation result
     */
    validateFile(file) {
        // Use centralized validation if available
        if (window.FileValidation && typeof window.FileValidation.validateFile === 'function') {
            return window.FileValidation.validateFile(file);
        }
        
        // Fallback validation
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        const maxSize = 5242880; // 5MB
        
        if (!allowedTypes.includes(file.type)) {
            return { valid: false, message: 'Invalid file type' };
        }
        
        if (file.size > maxSize) {
            return { valid: false, message: 'File too large (max 5MB)' };
        }
        
        return { valid: true, message: '' };
    }
    
    /**
     * Remove a file by index
     * @param {number} index - Index of file to remove
     */
    removeFile(index) {
        if (!this.input || !this.input.files) return;
        
        // Create new FileList without the removed file
        const dt = new DataTransfer();
        Array.from(this.input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        // Update input
        this.input.files = dt.files;
        this.files = Array.from(dt.files);
        
        // Refresh preview
        if (this.files.length === 0) {
            this.hidePreview();
        } else {
            this.generatePreviews(this.files);
        }
        
        // Callback
        if (this.options.onFileRemove) {
            this.options.onFileRemove(index, this.files);
        }
    }
    
    /**
     * Clear all files
     */
    clearAll() {
        if (this.input) {
            this.input.value = '';
            this.input.files = new DataTransfer().files;
        }
        
        this.files = [];
        this.hidePreview();
        this.clearValidation();
        
        // Callback
        if (this.options.onClear) {
            this.options.onClear();
        }
    }
    
    /**
     * Show preview container
     */
    showPreview() {
        if (this.container) {
            this.container.style.display = 'block';
        }
    }
    
    /**
     * Hide preview container
     */
    hidePreview() {
        if (this.container) {
            this.container.style.display = 'none';
        }
    }
    
    /**
     * Show validation error
     * @param {string} message - Error message
     */
    showValidationError(message) {
        if (this.input) {
            this.input.classList.add('is-invalid');
        }
        
        const feedback = document.querySelector(this.options.feedbackSelector);
        if (feedback) {
            feedback.textContent = message;
        }
        
        // Callback
        if (this.options.onValidationError) {
            this.options.onValidationError(message);
        }
    }
    
    /**
     * Clear validation state
     */
    clearValidation() {
        if (this.input) {
            this.input.classList.remove('is-invalid');
        }
    }
    
    /**
     * Get current files
     * @returns {Array} Array of File objects
     */
    getFiles() {
        return this.files;
    }
    
    /**
     * Check if any files are selected
     * @returns {boolean}
     */
    hasFiles() {
        return this.files.length > 0;
    }
}

// Global instance for backward compatibility
window.ImagePreview = ImagePreview;
window.imagePreviewInstance = null;

// Utility function to quickly setup image preview
window.setupImagePreview = function(inputSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    if (!input) {
        console.error('ImagePreview: Input not found:', inputSelector);
        return null;
    }
    
    const preview = new ImagePreview(options);
    preview.setupInput(input, options);
    
    // Store global instance for onclick handlers
    window.imagePreviewInstance = preview;
    
    return preview;
};
