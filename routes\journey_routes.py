from flask import Blueprint, render_template, request, redirect, url_for, flash, session, g, jsonify
import os
from services import departure_board_service, event_service as event_service, subscription_service, user_service
from services import journey_service as journey_service
from services import location_service as location_service
from utils.security import content_manager_required, login_required
from utils.file_utils import allowed_file
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

bp = Blueprint('journey', __name__, url_prefix='/journey')

# Private Journey Routes (My Journeys)


@bp.route('/private', methods=['GET'])
@login_required
def get_private_journeys():
    """Get current user's journeys with pagination"""
    new_journey_id = session.get('new_journey_id')
    session['journey_page'] = 'private'

    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    limit = 10
    offset = (page - 1) * limit

    # Get journeys with pagination
    journeys = journey_service.get_private_journeys(
        user_id=session['user_id'],
        limit=limit,
        offset=offset,
        search=search
    )

    # Get total count for pagination
    total_count = journey_service.get_private_journeys_count(
        user_id=session['user_id'],
        search=search
    )

    total_pages = (total_count + limit - 1) // limit

    session.pop('new_journey_id', None)

    return render_template(
        'journey/list.html',
        journeys=journeys,
        page=page,
        total_pages=total_pages,
        total_count=total_count, new_journey_id=new_journey_id
    )


@bp.route('/private/new', methods=['POST'])
@login_required
def create_journey():
    """Create a new journey"""
    # Get form data
    title = request.form.get('title')
    description = request.form.get('description')
    start_date = request.form.get('start_date')
    visibility = request.form.get('visibility', 'private')
    no_edits = 'no_edits' in request.form

    if user_service.is_user_blocked(session['user_id']):
        if visibility != 'private':
            flash('You cannot create a public journey because you are blocked.', 'danger')
            visibility = 'private'

    # Check if image was uploaded
    image = request.files.get('image')
    valid_image = None

    # Validate image
    if image and image.filename:
        image.seek(0, os.SEEK_END)
        file_size = image.tell()
        image.seek(0)

        if file_size > 5 * 1024 * 1024:  # 5MB size limit
            flash(f"Image '{image.filename}' exceeds the 5MB size limit and will be skipped.", "warning")
        else:
            valid_image = image

    # Create journey
    success, message, journey_id = journey_service.create_journey(
        user_id=session['user_id'],
        title=title,
        description=description,
        start_date=start_date,
        visibility=visibility,
        cover_image=valid_image,
        no_edits=no_edits
    )

    flash(message, 'success' if success else 'danger')
    if success:
        session['new_journey_id'] = journey_id
        return redirect(url_for('journey.get_private_journeys'))

    return render_template('journey/create.html', user_blocked=user_service.is_user_blocked(session['user_id']))


@bp.route('/private/new', methods=['GET'])
@login_required
def get_private_journey_form():
    """Get the form for creating a new journey"""
    user_blocked = user_service.is_user_blocked(session['user_id'])
    premium_access = subscription_service.check_can_use_premium_features(
        user_id=session['user_id']
    )

    return render_template('journey/create.html', user_blocked=user_blocked, premium_access=premium_access)


@bp.route('/private/<int:journey_id>', methods=['GET'])
@login_required
def get_private_journey(journey_id):
    """Get a journey by ID"""
    success, message, journey_data = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )
    premium_access = subscription_service.check_can_use_premium_features(
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_private_journeys'))

    success, message, event_list = event_service.get_journey_events(
        journey_id=journey_id,
        user_id=session['user_id'],
    )

    locations = location_service.get_locations_for_journey(journey_id=journey_id)

    # Get appeal status if journey is hidden and user is the owner
    appeal_status = None
    if journey_data.get('is_hidden') and journey_data.get('user_id') == session['user_id']:
        from services import helpdesk_service
        appeal_status = helpdesk_service.get_journey_appeal_status(session['user_id'], journey_id)

    return render_template('journey/detail.html', journey=journey_data, events=event_list, premium_access=premium_access, locations=locations, appeal_status=appeal_status)


@bp.route('/private/<int:journey_id>/edit', methods=['GET'])
@login_required
def get_journey_edit_form(journey_id):
    """Get the form for editing a journey"""
    success, message, journey = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    user_blocked = user_service.is_user_blocked(session['user_id'])
    premium_access = subscription_service.check_can_use_premium_features(
        user_id=session['user_id']
    )
    ever_had_premium = subscription_service.check_user_ever_had_premium(
        user_id=session['user_id']
    )

    if not success or journey is None:
        flash(message, 'danger')
        return redirect(url_for('journey.get_private_journeys'))

    # Check if user is the owner or a content manager
    from utils.permissions import PermissionChecker
    is_owner = journey.get('user_id') == session['user_id']
    is_content_manager = PermissionChecker.can_manage_content()

    if not (is_owner or is_content_manager):
        flash('Only the owner or content managers can edit a journey.', 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    events_success, events_message, events = event_service.get_journey_events(journey_id, session['user_id'])

    if not events_success:
        flash(events_message, 'danger')
        events = []

    return render_template('journey/edit.html', journey=journey, events=events, user_blocked=user_blocked, premium_access=premium_access, ever_had_premium=ever_had_premium)

@bp.route('/private/<int:journey_id>/edit', methods=['POST'])
@login_required
def update_journey(journey_id):
    """Update a journey"""

    success, message, journey = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_private_journeys'))

    # Check if user is the owner or a content manager
    from utils.permissions import PermissionChecker
    is_owner = journey['user_id'] == session['user_id']
    is_content_manager = PermissionChecker.can_manage_content()

    if not (is_owner or is_content_manager):
        flash('Only the owner or content managers can edit a journey.', 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    # Get form data
    title = request.form.get('title')
    description = request.form.get('description')
    start_date = request.form.get('start_date')
    visibility = request.form.get('visibility', 'private')
    edit_reason = request.form.get('edit_reason')
    no_edits = 'no_edits' in request.form

    # Check if user is a content manager editing someone else's journey
    is_staff_edit = is_content_manager and not is_owner

    # Staff editing other users' content must provide a reason
    if is_staff_edit and (not edit_reason or not edit_reason.strip()):
        flash('You must provide a reason when editing a user\'s journey.', 'danger')
        return redirect(url_for('journey.get_journey_edit_form', journey_id=journey_id))

    if user_service.is_user_blocked(session['user_id']):
        if visibility != 'private':
            flash('You cannot set this journey to public because you are blocked.', 'danger')
            visibility = 'private'

    image = request.files.get('image')
    valid_image = None
    if image and image.filename:
        image.seek(0, os.SEEK_END)
        file_size = image.tell()
        image.seek(0)

        if file_size > 5 * 1024 * 1024:
            flash(f"Image '{image.filename}' exceeds the 5MB size limit and will be skipped.", "warning")
        else:
            valid_image = image

    success, message = journey_service.update_journey(
        journey_id=journey_id,
        user_id=session['user_id'],
        title=title,
        description=description,
        start_date=start_date,
        visibility=visibility,
        cover_image=valid_image,
        no_edits=no_edits,
        edit_reason=edit_reason
    )

    # Check if this was a modal submission
    is_modal_submission = request.form.get('modal_submission') == '1'

    # Check if this is an AJAX request
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or 'application/json' in request.headers.get('Accept', ''):
        # Return JSON response for AJAX requests
        return jsonify({
            'success': success,
            'message': message,
            'redirect_url': url_for('journey.get_private_journey', journey_id=journey_id) if session.get('journey_page') == 'private' else url_for('journey.get_public_journey', journey_id=journey_id)
        })

    if success:
        flash(message, 'success')
        if session.get('journey_page') == 'private':
            return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
        else:
            return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    else:
        flash(message, 'danger')
        if is_modal_submission:
            # For modal submissions, redirect to the journey detail page to show the error
            if session.get('journey_page') == 'private':
                return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
            else:
                return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
        else:
            # For regular form submissions, redirect back to the edit form
            return redirect(url_for('journey.get_journey_edit_form', journey_id=journey_id))

@bp.route('/<int:journey_id>/hide', methods=['POST'])
@login_required
def update_journey_hidden_status(journey_id):
    """Update journey status to hidden (set is_hidden to True)"""
    success, message, journey = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_private_journeys'))

    success, message = journey_service.update_journey_hidden_status(
        journey_id=journey_id,
        user_id=session['user_id'],
        is_hidden= not journey['is_hidden']
    )

    flash(message, 'success' if success else 'danger')

    # 리다이렉트 로직: 요청 경로에 'manage'가 포함되어 있는지 확인
    referrer = request.referrer or ""
    if 'manage' in referrer:
        return redirect(url_for('journey.admin_hidden_journeys'))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))

@bp.route('/private/<int:journey_id>/delete', methods=['POST'])
@login_required
def delete_journey(journey_id):
    """Delete a journey"""
    success, message = journey_service.delete_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    flash(message, 'success' if success else 'danger')
    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journeys', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journeys', journey_id=journey_id))

# Public Journey Routes


@bp.route('/public', methods=['GET'])
@login_required
def get_public_journeys():
    """Get list of public journeys with optional filtering"""
    session['journey_page'] = 'public'
    page = request.args.get('page', 1, type=int)
    search = request.args.get('q', '')
    filter_type = request.args.get('filter')
    active_tab = request.args.get('active_tab', 'journeys')

    limit = 8
    offset = (page - 1) * limit

    if filter_type == 'public':
        journeys = journey_service.get_public_journeys(
            limit=limit,
            offset=offset,
            search=search
        )
        total_count = journey_service.get_public_journeys_count(search=search)
    elif filter_type == 'hidden':
        journeys = journey_service.get_hidden_journeys(
            limit=limit,
            offset=offset,
            search=search
        )
        total_count = journey_service.get_hidden_journeys_count(search=search)
    else:
        journeys = journey_service.get_public_journeys(
            limit=limit,
            offset=offset,
            search=search
        )
        total_count = journey_service.get_public_journeys_count(search=search)

    total_pages = (total_count + limit - 1) // limit

    return render_template(
        'discovery/journey/list.html',
        journeys=journeys,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        filter=filter_type,
        active_tab=active_tab,
        search_term=search
    )

@bp.route('/published/<int:journey_id>', methods=['GET'])
def get_published_journey_detail(journey_id):
    """Get a published journey by ID (accessible without login)"""
    try:
        # Set journey_page to 'published' for navigation context
        session['journey_page'] = 'published'

        # Get journey details - only allow published journeys for anonymous users
        success, message, journey = journey_service.get_journey(
            journey_id=journey_id,
            user_id=session['user_id'] if g.current_user else None
        )

        if not success:
            flash(message, "danger")
            # Redirect to published journeys list on failure
            return redirect(url_for('main.get_published_journey'))

        # Double-check that this is actually a published journey
        if journey['visibility'] != 'published':
            flash("This journey is not publicly available", "danger")
            return redirect(url_for('main.get_published_journey'))

        # Get journey events
        success, message, events = event_service.get_journey_events(
            journey_id=journey_id,
            user_id=session['user_id'] if g.current_user else None
        )

        if not success:
            events = []

        # Get journey locations
        locations = location_service.get_locations_for_journey(journey_id=journey_id)

        # For anonymous users, set default values for user-specific features
        is_following_journey = False
        is_following_user = False
        is_premium = False
        premium_access = False

        # For logged-in users, get their specific data
        if g.current_user:
            from services import departure_board_service
            is_following_journey, _ = departure_board_service.check_following_journey(
                user_id=session['user_id'],
                journey_id=journey_id
            )
            is_following_user, _ = departure_board_service.check_following_user(
                follower_id=session['user_id'],
                followed_id=journey['user_id']
            )
            is_premium = subscription_service.check_can_use_premium_features(
                user_id=session['user_id']
            )
            premium_access = subscription_service.check_can_use_premium_features(
                user_id=session['user_id']
            )

        # Get appeal status if journey is hidden and user is the owner
        appeal_status = None
        if g.current_user and journey.get('is_hidden') and journey.get('user_id') == session['user_id']:
            from services import helpdesk_service
            appeal_status = helpdesk_service.get_journey_appeal_status(session['user_id'], journey_id)

        return render_template(
            'journey/detail.html',
            journey=journey,
            events=events,
            locations=locations,
            is_following_journey=is_following_journey,
            is_following_user=is_following_user,
            is_premium=is_premium,
            premium_access=premium_access,
            appeal_status=appeal_status
        )
    except Exception as e:
        logger.error(f"Error in get_published_journey_detail: {str(e)}")
        flash("An error occurred while loading the journey", "danger")
        return redirect(url_for('main.get_published_journey'))


import traceback
import sys

@bp.route('/public/<int:journey_id>', methods=['GET'])
def get_public_journey(journey_id):
    """Get a public journey by ID (requires login)"""
    try:
        # Check if user is logged in for public journeys
        if not g.current_user:
            flash("Please log in to view public journeys", "info")
            return redirect(url_for('auth.login'))

        # Always set journey_page to 'public' for all users (authenticated or not)
        session['journey_page'] = 'public'

        # Debug: Log initial state
        logger.debug(f"Processing journey_id: {journey_id}")
        logger.debug(f"User ID: {session.get('user_id')}")

        # Get journey details
        # The service layer handles visibility and permissions
        success, message, journey = journey_service.get_journey(
            journey_id=journey_id,
            user_id=session['user_id']
        )

        # Debug: Log journey retrieval result
        logger.debug(f"Journey retrieval - Success: {success}, Message: {message}")
        if success and journey:
            logger.debug(f"Journey data keys: {list(journey.keys()) if isinstance(journey, dict) else 'Not a dict'}")

        if not success:
            flash(message, "danger")
            # Redirect to the public journeys list on failure
            return redirect(url_for('journey.get_public_journeys'))

        # Get journey events
        success, message, events = event_service.get_journey_events(
            journey_id=journey_id,
            user_id=session['user_id']
        )

        # Debug: Log events retrieval and validate event data
        logger.debug(f"Events retrieval - Success: {success}, Message: {message}")
        if events:
            logger.debug(f"Number of events: {len(events)}")
            for i, event in enumerate(events):
                logger.debug(f"Event {i} - ID: {getattr(event, 'id', 'N/A')}")
                logger.debug(f"Event {i} - Title: {getattr(event, 'title', 'N/A')}")
                logger.debug(f"Event {i} - Latitude: {getattr(event, 'latitude', 'N/A')} (type: {type(getattr(event, 'latitude', None))})")
                logger.debug(f"Event {i} - Longitude: {getattr(event, 'longitude', 'N/A')} (type: {type(getattr(event, 'longitude', None))})")
                # logger.debug(f"Event {i} - Location name: '{getattr(event, 'location_name', 'N/A')}' (contains quotes: {'quotes found' if any(q in str(getattr(event, 'location_name', '')) for q in ['"', "'"]) else 'no quotes'})")

        # If events retrieval fails, use an empty list instead of None, show warning
        if not success:
            flash(message, "warning")
            events = []

        # Get journey locations
        locations = location_service.get_locations_for_journey(journey_id=journey_id)
        logger.debug(f"Locations count: {len(locations) if locations else 0}")

        # Get user-specific data for logged-in users
        is_following_journey, _ = departure_board_service.check_following_journey(
            user_id=session['user_id'],
            journey_id=journey_id
        )
        is_following_user, _ = departure_board_service.check_following_user(
            follower_id=session['user_id'],
            followed_id=journey['user_id']
        )
        is_premium = subscription_service.check_can_use_premium_features(
            user_id=session['user_id']
        )
        premium_access = subscription_service.check_can_use_premium_features(
            user_id=session['user_id']
        )

        # Debug: Log user-specific data
        logger.debug(f"Following journey: {is_following_journey}")
        logger.debug(f"Following user: {is_following_user}")
        logger.debug(f"Premium access: {premium_access}")

        # Get appeal status if journey is hidden and user is the owner
        appeal_status = None
        if journey.get('is_hidden') and journey.get('user_id') == session['user_id']:
            from services import helpdesk_service
            appeal_status = helpdesk_service.get_journey_appeal_status(session['user_id'], journey_id)
            logger.debug(f"Appeal status: {appeal_status}")

        # Debug: Log template data being passed
        logger.debug("About to render template with:")
        logger.debug(f"- Journey type: {type(journey)}")
        logger.debug(f"- Events type: {type(events)}, count: {len(events) if events else 0}")
        logger.debug(f"- Locations type: {type(locations)}, count: {len(locations) if locations else 0}")

        return render_template(
            'journey/detail.html',
            journey=journey,
            events=events,
            locations=locations,
            is_following_journey=is_following_journey,
            is_following_user=is_following_user,
            is_premium=is_premium,
            premium_access=premium_access,
            appeal_status=appeal_status
        )
    except Exception as e:
        # Enhanced error logging with full context
        error_info = {
            'journey_id': journey_id,
            'user_id': session.get('user_id'),
            'session_data': dict(session),
            'g_current_user': getattr(g, 'current_user', None),
            'exception_type': type(e).__name__,
            'exception_message': str(e),
            'traceback': traceback.format_exc()
        }
        
        # Print to console for immediate visibility
        print("=" * 80)
        print("DETAILED ERROR in get_public_journey:")
        print("=" * 80)
        for key, value in error_info.items():
            if key == 'traceback':
                print(f"{key.upper()}:")
                print(value)
            else:
                print(f"{key.upper()}: {value}")
        print("=" * 80)
        
        # Also log to file
        logger.error("DETAILED ERROR in get_public_journey:")
        for key, value in error_info.items():
            logger.error(f"{key}: {value}")
        
        # Print to stderr for better visibility
        print(f"\nERROR SUMMARY:", file=sys.stderr)
        print(f"Journey ID: {journey_id}", file=sys.stderr)
        print(f"Error: {type(e).__name__}: {str(e)}", file=sys.stderr)
        print(f"Full traceback above ↑", file=sys.stderr)
        
        flash("An error occurred while loading the journey", "danger")
        # Redirect to the public journeys list on internal error
        return redirect(url_for('journey.get_public_journeys'))

# @bp.route('/manage', methods=['GET'])
# @login_required
# @editor_or_admin_required
# def get_admin_journeys():
#     """Get list of journeys with pagination and filtering"""
#     page = request.args.get('page', 1, type=int)
#     search = request.args.get('search', '')
#     filter_type = request.args.get('filter', 'public')  # Default to 'public' if not specified

#     limit = 10
#     offset = (page - 1) * limit

#     # Get filtered journeys
#     if filter_type == 'public':
#         journeys = journey_service.get_public_journeys(
#             limit=limit,
#             offset=offset,
#             search=search
#         )
#         total_count = journey_service.get_public_journeys_count(search=search)
#     elif filter_type == 'hidden':
#         journeys = journey_service.get_hidden_journeys(
#             limit=limit,
#             offset=offset,
#             search=search
#         )
#         total_count = journey_service.get_hidden_journeys_count(search=search)
#     else:
#         journeys = journey_service.get_all_journeys(
#             limit=limit,
#             offset=offset,
#             search=search
#         )
#         total_count = journey_service.get_all_journeys_count(search=search)

#     total_pages = (total_count + limit - 1) // limit

#     return render_template(
#         'admin/hidden_journey/list.html',
#         journeys=journeys,
#         page=page,
#         total_pages=total_pages,
#         total_count=total_count,
#         filter_type=filter_type
#     )

@bp.route('/manage/hidden-journeys')
@content_manager_required
def admin_hidden_journeys():
    """Hidden journeys management"""
    # Get all hidden journeys (no pagination needed for this view)
    journey_list = journey_service.get_hidden_journeys(limit=0)

    # Organize journeys by user
    journeys_by_user = journey_service.organize_journeys_by_user(journey_list)

    return render_template('admin/hidden_journey/list.html',
                          journeys_by_user=journeys_by_user)


@bp.route('/manage/<int:journey_id>', methods=['GET'])
@login_required
@content_manager_required
def get_admin_journey(journey_id):
    """Get a journey for admin management (editor or admin only)"""
    success, message, journey_data = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.admin_hidden_journeys'))

    success, message, event_list = event_service.get_journey_events(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    # Get journey locations
    locations = location_service.get_locations_for_journey(journey_id=journey_id)

    # Get appeal status if user is the owner (for both hidden and recently unhidden journeys)
    appeal_status = None
    if journey_data.get('user_id') == session['user_id']:
        from services import helpdesk_service
        appeal_status = helpdesk_service.get_journey_appeal_status(session['user_id'], journey_id)

    return render_template(
        'journey/detail.html',
        journey=journey_data,
        events=event_list,
        locations=locations,
        is_admin_view=True,
        appeal_status=appeal_status
    )

# @bp.route('/manage/<int:journey_id>/edit', methods=['POST'])
# @login_required
# @editor_or_admin_required
# def update_admin_journey(journey_id):
#     """Update a journey (admin view)"""
#     title = request.form.get('title')
#     description = request.form.get('description')
#     start_date = request.form.get('start_date')
#     is_public = request.form.get('is_public') == '1'

#     success, message = journey_service.update_journey(
#         journey_id=journey_id,
#         user_id=session['user_id'],
#         title=title,
#         description=description,
#         start_date=start_date,
#         is_public=is_public
#     )

#     flash(message, 'success' if success else 'danger')
#     if success:
#         return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

#     success, _, journey = journey_service.get_journey(journey_id, session['user_id'])
#     if not success:
#         return redirect(url_for('journey.get_admin_journeys'))

#     return render_template(
#         'admin/journey/edit.html',
#         journey=journey
#     )

# @bp.route('/manage/<int:journey_id>/delete', methods=['POST'])
# @login_required
# @editor_or_admin_required
# def delete_admin_journey(journey_id):
#     """Delete a journey (admin view)"""
#     success, message = journey_service.delete_journey(
#         journey_id=journey_id,
#         user_id=session['user_id']
#     )

#     flash(message, 'success' if success else 'danger')
#     return redirect(url_for('journey.get_admin_journeys'))

# @bp.route('/manage/<int:journey_id>/hide', methods=['POST'])
# @editor_or_admin_required
# def update_admin_journey_hidden_status(journey_id):
#     """Update journey status to hidden (set is_hidden to True)"""
#     success, message, journey = journey_service.get_journey(
#         journey_id=journey_id,
#         user_id=session['user_id']
#     )

#     if not success:
#         flash(message, 'danger')
#         return redirect(url_for('journey.get_private_journeys'))

#     success, message = journey_service.update_journey_hidden_status(
#         journey_id=journey_id,
#         user_id=session['user_id'],
#         is_hidden= not journey['is_hidden']
#     )

#     flash(message, 'success' if success else 'danger')

    # Determine where to redirect based on the source page
    from_page = request.form.get('from_page')
    if from_page == 'public':
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    elif from_page == 'hidden':
        return redirect(url_for('journey.hidden_journeys'))
    else:
        # Fallback to the hidden journeys page
        return redirect(url_for('journey.hidden_journeys'))


# dashboard - redirect to the main admin hidden journeys page
@bp.route('/hidden-journeys')
@content_manager_required
def hidden_journeys():
    """Hidden journeys management - redirect to admin page"""
    return redirect(url_for('journey.admin_hidden_journeys'))

@bp.route('/<int:journey_id>/toggle-hidden', methods=['POST'])
@content_manager_required
def toggle_hidden(journey_id):
    """Toggle a journey's hidden status"""
    try:
        # Get journey data directly from data layer (bypassing permission checks for staff)
        from data import journey_data
        journey = journey_data.get_journey(journey_id)

        if not journey:
            flash('Journey not found', 'danger')
            return redirect(url_for('journey.admin_hidden_journeys'))

        # Toggle hidden status
        is_hidden = not journey['is_hidden']

        success, message = journey_service.update_journey_hidden_status(
            journey_id=journey_id,
            user_id=session['user_id'],
            is_hidden=is_hidden
        )

        flash(message, 'success' if success else 'danger')

    except Exception as e:
        logger.error(f"Error toggling hidden status for journey {journey_id}: {str(e)}")
        flash('An error occurred while updating the journey status', 'danger')

    return redirect(url_for('journey.admin_hidden_journeys'))

@bp.route('/private/<int:journey_id>/remove-cover', methods=['GET'])
@login_required
def remove_journey_cover(journey_id):
    """Remove a journey's cover image"""
    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.headers.get('Content-Type', '').startswith('application/json')

    success, message = journey_service.remove_journey_cover_image(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    if is_ajax:
        return jsonify({'success': success, 'message': message})

    flash(message, 'success' if success else 'danger')
    # Determine redirect based on journey visibility and session
    if session.get('journey_page') == 'public':
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

@bp.route('/<int:journey_id>/upload-cover', methods=['POST'])
@login_required
def upload_cover_image(journey_id):
    """Upload a cover image for a journey"""
    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.headers.get('Content-Type', '').startswith('application/json')

    # Check if user owns the journey
    success, message, journey = journey_service.get_journey(journey_id, session['user_id'])
    if not success or journey.get('user_id') != session['user_id']:
        error_msg = 'You do not have permission to modify this journey'
        if is_ajax:
            return jsonify({'success': False, 'message': error_msg}), 403
        flash(error_msg, 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    # Check if user has premium access
    premium_access = subscription_service.check_can_use_premium_features(session['user_id'])
    if not premium_access:
        error_msg = 'Cover images are available for premium users only'
        if is_ajax:
            return jsonify({'success': False, 'message': error_msg}), 403
        flash(error_msg, 'warning')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    # Check if a file was included
    if 'image' not in request.files:
        error_msg = 'No file selected'
        if is_ajax:
            return jsonify({'success': False, 'message': error_msg}), 400
        flash(error_msg, 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    file = request.files['image']

    # Check if file is valid
    if file.filename == '':
        error_msg = 'No file selected'
        if is_ajax:
            return jsonify({'success': False, 'message': error_msg}), 400
        flash(error_msg, 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    # Process the file
    if file and allowed_file(file.filename):
        success, message, _ = journey_service.upload_journey_cover(
            journey_id=journey_id,
            user_id=session['user_id'],
            file=file
        )

        if is_ajax:
            return jsonify({'success': success, 'message': message})
        flash(message, 'success' if success else 'danger')
    else:
        error_msg = 'Invalid file type. Allowed types: jpg, jpeg, png, gif'
        if is_ajax:
            return jsonify({'success': False, 'message': error_msg}), 400
        flash(error_msg, 'danger')

    # Determine redirect based on journey visibility and session
    if session.get('journey_page') == 'public':
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

# New image routes
@bp.route('/<int:journey_id>/update-cover-image', methods=['POST'])
@login_required
def update_cover_image(journey_id):
    """Update a journey's cover image"""
    return upload_cover_image(journey_id)  # Reuse the upload function

@bp.route('/<int:journey_id>/remove-cover-image', methods=['POST'])
@login_required
def remove_cover_image(journey_id):
    """Remove a journey's cover image"""
    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.headers.get('Content-Type', '').startswith('application/json')

    # Get edit reason if provided (for staff edits)
    edit_reason = None
    if request.is_json:
        edit_reason = request.json.get('edit_reason')
    else:
        edit_reason = request.form.get('edit_reason')

    success, message = journey_service.remove_journey_cover_image(
        journey_id=journey_id,
        user_id=session['user_id'],
        edit_reason=edit_reason
    )

    if is_ajax:
        return jsonify({'success': success, 'message': message})

    flash(message, 'success' if success else 'danger')
    # Determine redirect based on journey visibility and session
    if session.get('journey_page') == 'public':
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))



@bp.route('/<int:journey_id>/remove-cover-photo', methods=['POST'])
@login_required
def remove_cover_photo(journey_id):
    """Remove a journey's cover image (legacy route)"""
    return remove_cover_image(journey_id)


