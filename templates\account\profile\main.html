{% extends "base.html" %}

{% block title %}My Profile - Footprints{% endblock %}

{% block content %}
<div class="container">
    <div class="mb-4">
        <h1 class="display-6 fw-bold">
            <span class="position-relative">
                Account
                <span class="position-absolute start-0 bottom-0"
                    style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
            </span>
        </h1>
        <div class="border-bottom position-relative">
            <div class="d-flex" id="profileTabNav">
                <div class="me-4 position-relative">
                    <a href="{{ url_for('account.get_profile', active_tab='profile') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'profile' %}text-primary{% else %}text-secondary{% endif %} profile-tab-link"
                        data-tab="profile-content">
                        Profile
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'profile' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('account.get_profile', active_tab='activities') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'activities' %}text-primary{% else %}text-secondary{% endif %} profile-tab-link"
                        data-tab="activities-content">
                        Activities
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'activities' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('account.get_profile', active_tab='password') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'password' %}text-primary{% else %}text-secondary{% endif %} profile-tab-link"
                        data-tab="password-content">
                        Password
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'password' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'subscription' %}text-primary{% else %}text-secondary{% endif %} profile-tab-link"
                        data-tab="subscription-content">
                        Subscription
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'subscription' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('account.get_profile', active_tab='map') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'map' %}text-primary{% else %}text-secondary{% endif %} profile-tab-link"
                        data-tab="map-content">
                        Map
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'map' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-content" id="accountTabContent">
        {% if active_tab == 'profile' %}
        <div class="tab-pane fade show active" id="profile-content" role="tabpanel">
            <div class="container">
                <div class="row g-4">
                    <div class="col-lg-4 col-md-5">
                        <div class="card shadow-sm border-0 rounded-4 mb-3">
                            <div class="card-header bg-light py-3 rounded-top-4 border-0">
                                <h5 class="fw-bold mb-0">Profile</h5>
                            </div>
                            <div class="card-body py-4">
                                <form id='imageForm' action="{{ url_for('account.update_profile_image') }}"
                                    method="post" enctype="multipart/form-data">
                                    <div class="d-flex flex-column align-items-center">
                                        <div class="mb-3">
                                            <img id="imagePreview"
                                                src="{{ url_for('static', filename=get_safe_image_url(account.profile_image, 'profile')) }}"
                                                class="img-fluid rounded-circle shadow-sm"
                                                style="width: 150px; height: 150px; object-fit: cover; border: 2px solid #000;"
                                                alt="{{ account.username }}'s profile">
                                        </div>

                                        <div class="d-flex flex-column gap-2">
                                            <label for="imageUpload" class="btn btn-primary rounded-pill py-2 px-3">
                                                <i class="bi bi-cloud-upload me-1"></i> Upload Photo
                                            </label>
                                            <input id="imageUpload" type="file" name="profile_image" class="d-none"
                                                accept=".png,.jpg,.jpeg,.gif">

                                            {% if account.profile_image %}
                                            <button type="button" class="btn btn-outline-danger rounded-pill py-2 px-3"
                                                id="deleteImageBtn">
                                                <i class="bi bi-trash me-1"></i> Remove
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Account Actions Card -->
                        <div class="card shadow-sm border-0 rounded-4">
                            <div class="card-header bg-light py-3 rounded-top-4 border-0">
                                <h5 class="card-title fw-bold mb-0">Account</h5>
                            </div>
                            <div class="w-100 text-center mt-4">
                                <div class="text-uppercase text-muted small fw-semibold mb-2"
                                    style="letter-spacing: 1px;">ACCOUNT TYPE
                                </div>
                                <span
                                    class="badge rounded-pill {{ get_role_badge_class(account.role) }} border-0 fs-6 py-2 px-3">
                                    <i class="{{ get_role_icon(account.role) }} me-1"></i>{{
                                    account.role|title }}
                                </span>
                            </div>
                            <div class="card-body">
                                {% if account.is_blocked %}
                                <div class="alert alert-warning my-3 py-2 small rounded-3 border-0 shadow-sm">
                                    Your account is currently blocked. You cannot publish public journeys until the
                                    block is lifted.
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-8 col-md-7">
                        <div class="card shadow-sm border-0 rounded-4 h-100">
                            <div
                                class="card-header bg-light rounded-top-4 border-0 d-flex justify-content-between align-items-center">
                                <h5 class="card-title fw-bold mb-0">
                                    <span id="headerText">Personal Information</span>
                                    <span
                                        class="ms-2 badge {% if account.is_public %}bg-success-subtle text-success{% else %}bg-danger-subtle text-danger{% endif %} rounded-pill fs-smaller">
                                        <i
                                            class="bi {% if account.is_public %}bi-eye{% else %}bi-eye-slash{% endif %} me-1"></i>
                                        {% if account.is_public %}Public Profile{% else %}Private Profile{% endif %}
                                    </span>
                                </h5>
                                <div class="button-container d-flex align-items-center gap-2">
                                    <a href="{{ url_for('account.fix_privacy_settings') }}" id="resetPrivacyBtn"
                                        class="btn btn-outline-secondary rounded-pill py-2 px-3">
                                        <i class="bi bi-shield-check me-2"></i>Reset Privacy Settings
                                    </a>
                                    <button id="toggleEditBtn" class="btn btn-primary rounded-pill py-2 px-3">
                                        Edit Profile
                                    </button>
                                    <div id="editModeButtons" class="d-none">
                                        <button type="button" id="cancelEditBtn"
                                            class="btn btn-outline-secondary rounded-pill py-2 px-3 me-2">
                                            Cancel
                                        </button>
                                        <button type="button" id="saveProfileBtn"
                                            class="btn btn-dark rounded-pill py-2 px-3">
                                            Save Changes
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="viewMode" class="card-body px-4 py-3">
                                <div class="row g-3 mb-2">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <label class="form-label text-muted small text-uppercase fw-medium">First
                                                Name
                                                {% if account.show_first_name %}
                                                <span
                                                    class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                                    <i class="bi bi-eye-fill text-success"></i>
                                                </span>
                                                {% else %}
                                                <span
                                                    class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                                    <i class="bi bi-eye-slash-fill text-muted"></i>
                                                </span>
                                                {% endif %}
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light"><i
                                                        class="bi bi-person-fill"></i></span>
                                                <input type="text" class="form-control bg-light" id="view_first_name"
                                                    value="{{ account.first_name or 'Not provided' }}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <label class="form-label text-muted small text-uppercase fw-medium">Last
                                                Name
                                                {% if account.show_last_name %}
                                                <span
                                                    class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                                    <i class="bi bi-eye-fill text-success"></i>
                                                </span>
                                                {% else %}
                                                <span
                                                    class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                                    <i class="bi bi-eye-slash-fill text-muted"></i>
                                                </span>
                                                {% endif %}
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-light"><i
                                                        class="bi bi-person-fill"></i></span>
                                                <input type="text" class="form-control bg-light" id="view_last_name"
                                                    value="{{ account.last_name or 'Not provided' }}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted small text-uppercase fw-medium">Username
                                        {% if account.show_username %}
                                        <span class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-fill text-success"></i>
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-slash-fill text-muted"></i>
                                        </span>
                                        {% endif %}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i
                                                class="bi bi-person-badge"></i></span>
                                        <input type="text" class="form-control bg-light" id="view_username"
                                            value="{{ account.username }}" readonly>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted small text-uppercase fw-medium">Email Address
                                        {% if account.show_email %}
                                        <span class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-fill text-success"></i>
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-slash-fill text-muted"></i>
                                        </span>
                                        {% endif %}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="bi bi-envelope"></i></span>
                                        <input type="text" class="form-control bg-light" id="view_email"
                                            value="{{ account.email }}" readonly>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted small text-uppercase fw-medium">Location
                                        {% if account.show_location %}
                                        <span class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-fill text-success"></i>
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-slash-fill text-muted"></i>
                                        </span>
                                        {% endif %}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="bi bi-geo-alt"></i></span>
                                        <input type="text" class="form-control bg-light" id="view_location"
                                            value="{{ account.location or 'Not provided' }}" readonly>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted small text-uppercase fw-medium">About Me
                                        {% if account.show_description %}
                                        <span class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-fill text-success"></i>
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-slash-fill text-muted"></i>
                                        </span>
                                        {% endif %}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="bi bi-quote"></i></span>
                                        <textarea class="form-control bg-light" id="view_description" rows="2"
                                            readonly>{{ account.description or 'Not provided' }}</textarea>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label text-muted small text-uppercase fw-medium">Interests
                                        {% if account.show_interests %}
                                        <span class="badge bg-success-subtle text-success rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-fill text-success"></i>
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary rounded-pill px-2 ms-1">
                                            <i class="bi bi-eye-slash-fill text-muted"></i>
                                        </span>
                                        {% endif %}
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light"><i class="bi bi-tags"></i></span>
                                        <div class="form-control bg-light d-flex flex-wrap align-items-center gap-1">
                                            {% if account.interests %}
                                            {% for interest in account.interests.split(',') %}
                                            <span class="badge bg-primary-subtle text-primary rounded-pill py-1 px-2">{{
                                                interest.strip() }}</span>
                                            {% endfor %}
                                            {% else %}
                                            <span class="text-muted">Not provided</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="editMode" class="card-body p-4 d-none">
                                <form method="post" action="{{ url_for('account.update_profile') }}" id="profile-form"
                                    class="needs-validation" novalidate>
                                    <div class="row g-3">

                                        <!-- Add profile visibility toggle -->
                                        <div class="d-flex justify-content-between align-items-center">
                                            <label class="form-label text-muted small text-uppercase fw-medium">
                                                Public Profile
                                                <span class="ms-1 text-muted small fw-normal text-lowercase">(Your
                                                    profile will be
                                                    visible to others)</span>
                                            </label>
                                            <div class="form-check form-switch">
                                                <!-- Use a static value checkbox for is_public to avoid problems with hidden fields -->
                                                <input class="form-check-input" type="checkbox" role="switch"
                                                    name="is_public" id="publicProfileSwitch" value='true' {% if
                                                    account.is_public %}checked{% endif %}>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <label
                                                    class="form-label text-muted small text-uppercase fw-medium">First
                                                    Name
                                                    <span
                                                        class="ms-1 text-muted small fw-normal text-lowercase">(Optional)</span>
                                                </label>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="input-group">

                                                        <input type="text" class="form-control optional-field"
                                                            id="first_name" name="first_name"
                                                            value="{{ account.first_name or '' }}"
                                                            oninput="validateName(this)"
                                                            placeholder="Enter your first name"
                                                            autocomplete="given-name">
                                                    </div>
                                                    <div class="form-check form-switch mb-2">
                                                        <input type="hidden" name="show_first_name" value='false'>
                                                        <input class="form-check-input" type="checkbox" role="switch"
                                                            name="show_first_name" id="firstNameSwitch" value='true' {%
                                                            if account.show_first_name %}checked{% endif %}>
                                                    </div>
                                                </div>
                                                <div class="invalid-feedback"></div>
                                            </div>

                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <label class="form-label text-muted small text-uppercase fw-medium">Last
                                                    Name
                                                    <span
                                                        class="ms-1 text-muted small fw-normal text-lowercase">(Optional)</span>
                                                </label>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="input-group">

                                                        <input type="text" class="form-control optional-field"
                                                            id="last_name" name="last_name"
                                                            value="{{ account.last_name or '' }}"
                                                            oninput="validateName(this)"
                                                            placeholder="Enter your last name"
                                                            autocomplete="family-name">
                                                    </div>
                                                    <div class="form-check form-switch mb-2">
                                                        <input type="hidden" name="show_last_name" value='false'>
                                                        <input class="form-check-input" type="checkbox" role="switch"
                                                            name="show_last_name" id="lastNameSwitch" value='true' {% if
                                                            account.show_last_name %}checked{% endif %}>
                                                    </div>
                                                </div>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <label class="form-label text-muted small text-uppercase fw-medium">
                                            Username
                                            <span class="ms-1 text-muted small fw-normal text-lowercase">(Username
                                                cannot be
                                                changed)</span>
                                        </label>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bi bi-person-badge"></i></span>

                                                <input type="text" class="form-control bg-light" id="username"
                                                    name="username" value="{{ account.username }}" readonly disabled>
                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input type="hidden" name="show_username" value='false'>
                                                <input class="form-check-input" type="checkbox" role="switch"
                                                    name="show_username" id="usernameSwitch" value='true' {% if
                                                    account.show_username %}checked{% endif %}>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-2">
                                        <label class="form-label text-muted small text-uppercase fw-medium">
                                            Email Address
                                        </label>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="input-group has-validation">

                                                <span class="input-group-text"><i class="bi bi-envelope "></i></span>

                                                <input type="email" class="form-control" id="email" name="email"
                                                    value="{{ account.email }}" required oninput="validateEmail(this)"
                                                    placeholder="<EMAIL>" autocomplete="email">

                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input type="hidden" name="show_email" value='false'>
                                                <input class="form-check-input" type="checkbox" role="switch"
                                                    name="show_email" id="emailSwitch" value='true' {% if
                                                    account.show_email %}checked{% endif %}>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback">Please enter a valid email address</div>
                                    </div>

                                    <div class="mb-2">
                                        <label class="form-label text-muted small text-uppercase fw-medium">
                                            Location
                                            <span
                                                class="ms-1 text-muted small fw-normal text-lowercase">(Optional)</span>
                                        </label>
                                        <div class="d-flex align-items-center gap-2">
                                            <div class="input-group">

                                                <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>

                                                <input type="text" class="form-control optional-field" id="location"
                                                    name="location" value="{{ account.location or '' }}"
                                                    placeholder="City, Country" autocomplete="address-level2">

                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input type="hidden" name="show_location" value='false'>
                                                <input class="form-check-input" type="checkbox" role="switch"
                                                    name="show_location" id="locationSwitch" value='true' {% if
                                                    account.show_location %}checked{% endif %}>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="mb-0">
                                        <div class="d-flex align-items-center gap-2">

                                            <label class="form-label text-muted small text-uppercase fw-medium">
                                                About
                                                <span
                                                    class="ms-1 text-muted small fw-normal text-lowercase">(Optional)</span>

                                            </label>
                                            <div class=" form-check form-switch mb-2">
                                                <input type="hidden" name="show_description" value='false'>
                                                <input class="form-check-input" type="checkbox" role="switch"
                                                    name="show_description" id="descriptionSwitch" value='true' {% if
                                                    account.show_description %}checked{% endif %}>
                                            </div>
                                        </div>
                                        <textarea class="form-control optional-field" id="description"
                                            name="description" rows="2"
                                            placeholder="Tell us about yourself...">{{ account.description or '' }}</textarea>
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="d-flex align-items-center gap-2">
                                            <label class="form-label text-muted small text-uppercase fw-medium">
                                                Interests
                                                <span class="ms-1 text-muted small fw-normal text-lowercase">(Optional -
                                                    e.g.,
                                                    hiking, photography, food)</span>
                                            </label>
                                            <div class="form-check form-switch mb-2">
                                                <input type="hidden" name="show_interests" value='false'>
                                                <input class="form-check-input" type="checkbox" role="switch"
                                                    name="show_interests" id="interestsSwitch" value='true' {% if
                                                    account.show_interests %}checked{% endif %}>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="bi bi-tags"></i></span>
                                            <input type="text" class="form-control optional-field" id="interests"
                                                name="interests" value="{{ account.interests or '' }}"
                                                placeholder="Enter your interests separated by commas...">
                                        </div>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% elif active_tab == 'activities' %}
        <div class="tab-pane fade show active" id="activities-content" role="tabpanel">
            {% include 'account/activities/main.html' %}
        </div>
        {% elif active_tab == 'password' %}
        <div class="tab-pane fade show active" id="password-content" role="tabpanel">
            {% include 'account/password/main.html' %}
        </div>
        {% elif active_tab == 'subscription' %}
        <div class="tab-pane fade show active" id="subscription-content" role="tabpanel">
            {% include 'account/subscription/main.html' %}
        </div>
        {% elif active_tab == 'map' %}
        <div class="tab-pane fade show active" id="subscription-content" role="tabpanel">
            {% include 'account/map/main.html' %}
        </div>
        {% endif %}
    </div>
</div>

{% block scripts %}
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
{% endblock %}

<script>
    const imageUpload = document.getElementById('imageUpload');
    const imagePreview = document.getElementById('imagePreview');
    const imageForm = document.getElementById('imageForm');
    const deleteImageBtn = document.getElementById('deleteImageBtn');

    // Edit mode elements
    const toggleEditBtn = document.getElementById('toggleEditBtn');
    const viewMode = document.getElementById('viewMode');
    const editMode = document.getElementById('editMode');
    const activeTabSwitch1 = document.getElementById('active-tab-switch1');
    const activeTabSwitch2 = document.getElementById('active-tab-switch2');
    const activeTabSwitch3 = document.getElementById('active-tab-switch3');
    const activeTabSwitch4 = document.getElementById('active-tab-switch4');
    const editModeButtons = document.getElementById('editModeButtons');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const profileForm = document.getElementById('profile-form');
    const headerText = document.getElementById('headerText');
    const publicProfileSwitch = document.getElementById('publicProfileSwitch');
    const resetPrivacyBtn = document.getElementById('resetPrivacyBtn');

    // Toggle between view and edit modes
    toggleEditBtn.addEventListener('click', function () {
        // Switch to edit mode
        viewMode.classList.add('d-none');
        editMode.classList.remove('d-none');

        // Show the edit mode buttons
        toggleEditBtn.style.display = 'none';
        editModeButtons.classList.remove('d-none');

        // Hide the reset privacy button when in edit mode
        resetPrivacyBtn.classList.add('d-none');

        // Show the tab visibility toggles
        activeTabSwitch1.classList.remove('d-none');
        activeTabSwitch2.classList.remove('d-none');
        activeTabSwitch3.classList.remove('d-none');
        activeTabSwitch4.classList.remove('d-none');

        // Update the header text
        headerText.textContent = 'Edit Personal Information';

        // Reset the form
        profileForm.reset();
    });

    // Cancel edit mode
    cancelEditBtn.addEventListener('click', function () {
        // Switch back to view mode
        viewMode.classList.remove('d-none');
        editMode.classList.add('d-none');

        // Hide the edit mode buttons
        toggleEditBtn.style.display = '';
        editModeButtons.classList.add('d-none');

        // Show the reset privacy button when not in edit mode
        resetPrivacyBtn.classList.remove('d-none');

        // Hide the tab visibility toggles
        activeTabSwitch1.classList.add('d-none');
        activeTabSwitch2.classList.add('d-none');
        activeTabSwitch3.classList.add('d-none');
        activeTabSwitch4.classList.add('d-none');

        // Update the header text
        headerText.textContent = 'Personal Information';

        // Reset the form
        profileForm.reset();
    });

    // Public profile switch event - when user toggles public/private profile
    if (publicProfileSwitch) {
        publicProfileSwitch.addEventListener('change', function () {
            const isPublic = this.checked;

            // If turning profile private, inform user that all their information will be hidden
            if (!isPublic) {
                const modalResult = showModal(
                    'Make Profile Private',
                    '<p>When your profile is private, no one will be able to view your profile information or activities.</p><p>Are you sure you want to make your profile private?</p>',
                    {
                        actionText: 'Make Private',
                        actionClass: 'btn-secondary',
                        onAction: function () {
                            // Keep the switch off (private) and close the modal
                            publicProfileSwitch.checked = false;
                            bootstrap.Modal.getInstance(document.getElementById('commonModal')).hide();
                        }
                    }
                );

                // Add a listener for when the modal is dismissed
                const modalElement = document.getElementById('commonModal');
                modalElement.addEventListener('hidden.bs.modal', function handleModalClose() {
                    // If the action button wasn't clicked, turn the switch back on
                    if (!modalElement.querySelector('#modalActionBtn').classList.contains('clicked')) {
                        publicProfileSwitch.checked = true;
                    }
                    // Remove this listener to avoid duplicates
                    modalElement.removeEventListener('hidden.bs.modal', handleModalClose);
                }, { once: true });

                // Mark the action button as clicked when used
                const actionBtn = document.getElementById('modalActionBtn');
                actionBtn.addEventListener('click', function () {
                    this.classList.add('clicked');
                }, { once: true });
            }
        });
    }

    // Save profile changes
    saveProfileBtn.addEventListener('click', function (e) {
        e.preventDefault();

        // Check form validity first
        if (!profileForm.checkValidity()) {
            profileForm.classList.add('was-validated');
            return;
        }

        // Process all toggle switches before submission
        document.querySelectorAll('.form-check-input[type="checkbox"][role="switch"]').forEach(toggle => {
            console.log(`Processing toggle ${toggle.name}: checked=${toggle.checked}`);

            // Remove any existing hidden fields for this toggle
            document.querySelectorAll(`input[type="hidden"][name="${toggle.name}"]`).forEach(hidden => {
                hidden.remove();
            });

            if (toggle.checked) {
                // Set the toggle value to 'true'
                toggle.value = 'true';
                console.log(`Set ${toggle.name} to true (checked)`);
            } else {
                // Add a hidden field with value='false'
                const hidden = document.createElement('input');
                hidden.type = 'hidden';
                hidden.name = toggle.name;
                hidden.value = 'false';
                profileForm.appendChild(hidden);

                // Remove the unchecked toggle to avoid sending both values
                toggle.name = `_${toggle.name}`;

                console.log(`Set ${hidden.name} to false (unchecked)`);
            }
        });

        // Log all form values before submission
        console.log('Form data being submitted:', Object.fromEntries(new FormData(profileForm)));

        // Show confirmation modal
        showModal(
            'Save Profile Changes',
            '<p>Are you sure you want to update your profile information?</p>',
            {
                actionText: 'Save Changes',
                onAction: function () {
                    // Submit the form
                    profileForm.submit();
                }
            }
        );
    });

    // Add a hidden input to specify redirect location
    function appendRedirectInput() {
        const redirectInput = document.createElement('input');
        redirectInput.type = 'hidden';
        redirectInput.name = 'redirect_to';
        redirectInput.value = 'account.get_account';  // Redirect back to profile page
        imageForm.appendChild(redirectInput);
    }

    imageUpload.addEventListener('change', function () {
        const file = imageUpload.files[0];
        if (file) {
            // Validate file is an image
            if (!file.type.match('image.*')) {
                alert('Please select an image file (jpeg, png, etc.)');
                return;
            }

            // File size validation (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size too large. Please select an image under 5MB.');
                return;
            }

            // Show loading state
            imagePreview.classList.add('opacity-50');

            // Ensure redirect parameter is included
            appendRedirectInput();

            const reader = new FileReader();
            reader.onload = function (e) {
                imagePreview.src = e.target.result;
                imagePreview.alt = file.name;

                // Submit form after brief delay to allow preview to render
                setTimeout(function () {
                    imageForm.submit();
                }, 300);
            };

            reader.readAsDataURL(file);
        } else {
            imagePreview.src = "{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}";
            imagePreview.alt = 'Profile placeholder';
        }
    });

    // Handle image deletion with modal
    if (deleteImageBtn) {
        deleteImageBtn.addEventListener('click', function () {
            showModal(
                'Remove Profile Image',
                '<p>Are you sure you want to remove your profile image? This action cannot be undone.</p>',
                {
                    actionText: 'Remove Image',
                    onAction: function () {
                        window.location.href = "{{ url_for('account.delete_profile_image') }}";
                    }
                }
            );
        });
    }
</script>
{% endblock %}