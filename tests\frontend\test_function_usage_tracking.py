"""
Function Usage Tracking Tests

This module provides comprehensive testing to track which functions and scripts
are actually used during real user workflows, helping identify unused code
in event templates (detail.html and edit.html).
"""

import json
import os
from datetime import datetime
from playwright.sync_api import Page, expect
from typing import Dict, List, Set


class FunctionUsageTracker:
    """Track JavaScript function calls and Python route usage during testing"""
    
    def __init__(self):
        self.js_functions_called: Set[str] = set()
        self.routes_accessed: Set[str] = set()
        self.templates_rendered: Set[str] = set()
        self.js_files_loaded: Set[str] = set()
        self.coverage_data: Dict = {}
        
    def reset(self):
        """Reset tracking data"""
        self.js_functions_called.clear()
        self.routes_accessed.clear()
        self.templates_rendered.clear()
        self.js_files_loaded.clear()
        self.coverage_data.clear()
    
    def save_report(self, filename: str = None):
        """Save usage report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"function_usage_report_{timestamp}.json"
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "js_functions_called": list(self.js_functions_called),
            "routes_accessed": list(self.routes_accessed),
            "templates_rendered": list(self.templates_rendered),
            "js_files_loaded": list(self.js_files_loaded),
            "coverage_data": self.coverage_data
        }
        
        os.makedirs("test_reports", exist_ok=True)
        with open(f"test_reports/{filename}", "w") as f:
            json.dump(report, f, indent=2)
        
        return filename


def login_traveller(page: Page) -> None:
    """Helper function to login as traveller"""
    page.goto("http://127.0.0.1:5000/")
    page.locator("#navbarLogin").get_by_role("link", name="Login").click()
    page.get_by_role("textbox", name="Username").click()
    page.get_by_role("textbox", name="Username").fill("traveller1")
    page.get_by_role("textbox", name="Password").click()
    page.get_by_role("textbox", name="Password").fill("Password123!")
    page.get_by_role("button", name="Login").click()
    expect(page.locator("#my_journeys_navbar")).to_be_visible(timeout=10000)


def setup_function_tracking(page: Page, tracker: FunctionUsageTracker):
    """Setup JavaScript function call tracking"""
    
    # Inject tracking script
    tracking_script = """
    window.functionTracker = {
        calledFunctions: new Set(),
        originalFunctions: new Map(),
        
        wrapFunction: function(obj, funcName) {
            if (typeof obj[funcName] === 'function' && !this.originalFunctions.has(funcName)) {
                this.originalFunctions.set(funcName, obj[funcName]);
                obj[funcName] = (...args) => {
                    this.calledFunctions.add(funcName);
                    return this.originalFunctions.get(funcName).apply(obj, args);
                };
            }
        },
        
        getCalledFunctions: function() {
            return Array.from(this.calledFunctions);
        }
    };
    
    // Track common global functions
    const functionsToTrack = [
        'smartBack', 'confirmDeleteEvent', 'confirmDeleteComment', 'showProtectedEventMessage',
        'editEvent', 'deleteEvent', 'likeEvent', 'addComment', 'deleteComment',
        'manageImages', 'uploadImages', 'deleteImage', 'reportComment',
        'initMap', 'updateMap', 'searchLocation', 'selectLocation',
        'validateForm', 'submitForm', 'showModal', 'hideModal'
    ];
    
    // Wrap existing functions
    functionsToTrack.forEach(funcName => {
        if (window[funcName]) {
            window.functionTracker.wrapFunction(window, funcName);
        }
    });
    
    // Track jQuery functions if available
    if (typeof $ !== 'undefined') {
        const jqueryFunctions = ['click', 'submit', 'change', 'load', 'ready'];
        jqueryFunctions.forEach(funcName => {
            if ($.fn[funcName]) {
                window.functionTracker.wrapFunction($.fn, funcName);
            }
        });
    }
    """
    
    page.add_init_script(tracking_script)


def collect_function_usage(page: Page, tracker: FunctionUsageTracker):
    """Collect function usage data from the page"""
    
    # Get called JavaScript functions
    try:
        called_functions = page.evaluate("window.functionTracker ? window.functionTracker.getCalledFunctions() : []")
        tracker.js_functions_called.update(called_functions)
    except Exception as e:
        print(f"Error collecting function usage: {e}")
    
    # Get loaded JavaScript files
    try:
        js_files = page.evaluate("""
            Array.from(document.querySelectorAll('script[src]')).map(script => script.src)
        """)
        tracker.js_files_loaded.update(js_files)
    except Exception as e:
        print(f"Error collecting JS files: {e}")


# Global tracker instance
usage_tracker = FunctionUsageTracker()


def test_event_detail_page_usage(page: Page) -> None:
    """Test event detail page to track function usage"""

    # Setup tracking
    setup_function_tracking(page, usage_tracker)

    try:
        # Try to login and navigate to event detail
        login_traveller(page)

        # Navigate to journeys
        page.locator("#my_journeys_navbar").click()
        journey_card = page.locator(".journey-card").first

        # Click on journey and get journey id from url
        journey = journey_card.locator("a")
        href = journey.get_attribute("href")
        journey_id = href.split("/")[-1]
        journey.click()

        # Wait for page to load
        expect(page).to_have_url(f"http://127.0.0.1:5000/journey/private/{journey_id}")

        # Click on first event to go to detail page
        event_link = page.locator(".event-card a").first
        if event_link.is_visible():
            event_link.click()
            page.wait_for_load_state("networkidle")

            # Collect usage data
            collect_function_usage(page, usage_tracker)

            # Test various interactions on detail page
            test_event_detail_interactions(page)

            # Collect final usage data
            collect_function_usage(page, usage_tracker)

    except Exception as e:
        print(f"Login-based test failed: {e}")
        print("Falling back to public page testing...")

        # Fallback: Test public pages without login
        test_public_pages_usage(page)


def test_public_pages_usage(page: Page) -> None:
    """Test public pages without requiring login"""

    try:
        # Visit homepage and collect basic usage
        page.goto("http://127.0.0.1:5000/")
        page.wait_for_load_state("networkidle")
        collect_function_usage(page, usage_tracker)

        # Try to visit discovery/public journeys page
        page.goto("http://127.0.0.1:5000/discovery")
        page.wait_for_load_state("networkidle")
        collect_function_usage(page, usage_tracker)

        # Test any visible interactions on public pages
        try:
            # Look for any clickable elements
            buttons = page.locator("button, .btn").all()
            for i, button in enumerate(buttons[:3]):  # Test first 3 buttons
                if button.is_visible():
                    button.click()
                    page.wait_for_timeout(500)
                    collect_function_usage(page, usage_tracker)
        except Exception as e:
            print(f"Error testing public page interactions: {e}")

    except Exception as e:
        print(f"Error testing public pages: {e}")
        # Even if public pages fail, we can still generate a basic report
        collect_function_usage(page, usage_tracker)


def test_event_detail_interactions(page: Page) -> None:
    """Test various interactions on event detail page"""

    try:
        # Test like button if visible
        like_btn = page.locator(".like-btn")
        if like_btn.is_visible():
            like_btn.click()
            page.wait_for_timeout(1000)

        # Test menu dropdown if visible
        menu_btn = page.locator(".menu-btn")
        if menu_btn.is_visible():
            menu_btn.click()
            page.wait_for_timeout(500)

            # Test edit event button if visible
            edit_btn = page.locator(".editEventBtn")
            if edit_btn.is_visible():
                edit_btn.click()
                page.wait_for_timeout(2000)

                # Test edit modal interactions
                test_edit_modal_interactions(page)

        # Test image gallery if visible
        event_image = page.locator(".event-image-button")
        if event_image.is_visible():
            event_image.click()
            page.wait_for_timeout(1000)

        # Test manage images button if visible
        manage_btn = page.locator("#manageImagesBtn")
        if manage_btn.is_visible():
            manage_btn.click()
            page.wait_for_timeout(1000)

        # Test comment form if visible
        comment_input = page.locator(".comment-input")
        if comment_input.is_visible():
            comment_input.fill("Test comment")
            page.wait_for_timeout(500)

        # Test back button
        back_btn = page.locator(".back-button")
        if back_btn.is_visible():
            back_btn.click()
            page.wait_for_timeout(1000)

    except Exception as e:
        print(f"Error during detail page interactions: {e}")


def test_edit_modal_interactions(page: Page) -> None:
    """Test interactions within the edit event modal"""

    try:
        # Wait for modal to be visible
        page.wait_for_selector("#editEventModal", state="visible", timeout=5000)

        # Test form fields
        title_input = page.locator("#title")
        if title_input.is_visible():
            title_input.fill("Updated Event Title")

        description_input = page.locator("#description")
        if description_input.is_visible():
            description_input.fill("Updated description")

        # Test location change button
        change_location_btn = page.locator("#changeLocationBtn")
        if change_location_btn.is_visible():
            change_location_btn.click()
            page.wait_for_timeout(1000)

            # Test location search
            location_search = page.locator("#locationSearch")
            if location_search.is_visible():
                location_search.fill("New York")

                search_btn = page.locator("#searchLocationBtn")
                if search_btn.is_visible():
                    search_btn.click()
                    page.wait_for_timeout(2000)

        # Test staff location edit if visible
        edit_location_name_btn = page.locator("#editLocationNameBtn")
        if edit_location_name_btn.is_visible():
            edit_location_name_btn.click()
            page.wait_for_timeout(1000)

        # Close modal by clicking outside or escape
        page.keyboard.press("Escape")
        page.wait_for_timeout(1000)

    except Exception as e:
        print(f"Error during edit modal interactions: {e}")


def test_comprehensive_user_workflow(page: Page) -> None:
    """Test comprehensive user workflow to track all function usage"""

    # Reset tracker for comprehensive test
    usage_tracker.reset()
    setup_function_tracking(page, usage_tracker)

    # Test event detail page
    test_event_detail_page_usage(page)

    # Save comprehensive report
    report_file = usage_tracker.save_report("comprehensive_workflow_report.json")
    print(f"Comprehensive usage report saved to: {report_file}")


def test_generate_usage_report(page: Page) -> None:
    """Generate final usage report with analysis"""

    # Run comprehensive workflow
    test_comprehensive_user_workflow(page)

    # Generate analysis report
    analysis = analyze_function_usage()

    # Save analysis
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    analysis_file = f"test_reports/function_analysis_{timestamp}.json"

    with open(analysis_file, "w") as f:
        json.dump(analysis, f, indent=2)

    print(f"Function usage analysis saved to: {analysis_file}")
    print("\nSummary:")
    print(f"JavaScript functions called: {len(usage_tracker.js_functions_called)}")
    print(f"JavaScript files loaded: {len(usage_tracker.js_files_loaded)}")
    print(f"Routes accessed: {len(usage_tracker.routes_accessed)}")


def analyze_function_usage() -> Dict:
    """Analyze function usage and identify potentially unused functions"""

    # This would need to be expanded based on actual template analysis
    all_possible_functions = {
        'smartBack', 'confirmDeleteEvent', 'confirmDeleteComment', 'showProtectedEventMessage',
        'editEvent', 'deleteEvent', 'likeEvent', 'addComment', 'deleteComment',
        'manageImages', 'uploadImages', 'deleteImage', 'reportComment',
        'initMap', 'updateMap', 'searchLocation', 'selectLocation',
        'validateForm', 'submitForm', 'showModal', 'hideModal',
        'changeLocation', 'editLocationName', 'cancelLocationEdit',
        'confirmLocationChange', 'backToSearch', 'editMapLocation'
    }

    used_functions = usage_tracker.js_functions_called
    unused_functions = all_possible_functions - used_functions

    return {
        "analysis_timestamp": datetime.now().isoformat(),
        "total_possible_functions": len(all_possible_functions),
        "functions_used": list(used_functions),
        "functions_unused": list(unused_functions),
        "usage_percentage": len(used_functions) / len(all_possible_functions) * 100,
        "js_files_loaded": list(usage_tracker.js_files_loaded),
        "recommendations": {
            "potentially_removable_functions": list(unused_functions),
            "files_to_review": ["templates/event/detail.html", "templates/event/edit.html"]
        }
    }
