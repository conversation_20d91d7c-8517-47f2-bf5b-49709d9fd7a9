<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Usage Tracker - Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        .tracker-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 250px;
        }
        
        .tracker-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.active {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .report-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .function-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .function-item {
            padding: 2px 5px;
            margin: 1px 0;
            border-radius: 3px;
        }
        
        .function-used {
            background: #d4edda;
            color: #155724;
        }
        
        .function-unused {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="bi bi-bug"></i> Function Usage Tracker - Debug Mode</h1>
        <p class="text-muted">Use this page to manually test function tracking on your event pages.</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list-check"></i> Instructions</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Start Tracking:</strong> Click "Start Tracking" in the control panel</li>
                            <li><strong>Navigate:</strong> Go to your event detail or edit pages</li>
                            <li><strong>Interact:</strong> Click buttons, open modals, use forms, etc.</li>
                            <li><strong>Stop & Report:</strong> Return here and click "Stop & Report"</li>
                            <li><strong>Download:</strong> Save the report for analysis</li>
                        </ol>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i>
                            <strong>Tip:</strong> Add <code>?debug=true</code> to any page URL to show tracking controls on that page.
                        </div>
                        
                        <div class="mt-3">
                            <h6>Quick Links:</h6>
                            <a href="/journey/private" class="btn btn-outline-primary btn-sm me-2">My Journeys</a>
                            <a href="/discovery" class="btn btn-outline-secondary btn-sm me-2">Public Journeys</a>
                            <a href="/?debug=true" class="btn btn-outline-info btn-sm">Home (Debug)</a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="bi bi-code-square"></i> Manual Testing Functions</h5>
                    </div>
                    <div class="card-body">
                        <p>Test these functions manually to see if they're tracked:</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-primary btn-sm mb-2" onclick="testFunction('smartBack')">
                                    Test smartBack()
                                </button><br>
                                <button class="btn btn-warning btn-sm mb-2" onclick="testFunction('showModal')">
                                    Test showModal()
                                </button><br>
                                <button class="btn btn-info btn-sm mb-2" onclick="testFunction('validateForm')">
                                    Test validateForm()
                                </button><br>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-success btn-sm mb-2" onclick="testFunction('likeEvent')">
                                    Test likeEvent()
                                </button><br>
                                <button class="btn btn-secondary btn-sm mb-2" onclick="testFunction('initMap')">
                                    Test initMap()
                                </button><br>
                                <button class="btn btn-danger btn-sm mb-2" onclick="testFunction('deleteEvent')">
                                    Test deleteEvent()
                                </button><br>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div id="reportDisplay" class="report-section" style="display: none;">
                    <h6><i class="bi bi-graph-up"></i> Live Report</h6>
                    <div id="reportContent"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tracker Controls -->
    <div class="tracker-controls">
        <div class="tracker-status">
            <div id="statusIndicator" class="status-indicator"></div>
            <strong>Function Tracker</strong>
        </div>
        
        <div class="d-grid gap-2">
            <button id="startBtn" class="btn btn-success btn-sm" onclick="startTracking()">
                <i class="bi bi-play"></i> Start Tracking
            </button>
            <button id="stopBtn" class="btn btn-danger btn-sm" onclick="stopTracking()" disabled>
                <i class="bi bi-stop"></i> Stop & Report
            </button>
            <button id="downloadBtn" class="btn btn-info btn-sm" onclick="downloadReport()" disabled>
                <i class="bi bi-download"></i> Download Report
            </button>
            <button class="btn btn-outline-secondary btn-sm" onclick="clearReport()">
                <i class="bi bi-trash"></i> Clear
            </button>
        </div>
        
        <div id="liveStats" class="mt-2" style="display: none;">
            <small>
                <div>Functions Called: <span id="calledCount">0</span></div>
                <div>Duration: <span id="duration">0</span>s</div>
            </small>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/function_tracker.js') }}"></script>
    <script>
        let trackingInterval;
        
        function startTracking() {
            window.FunctionTracker.start();
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('statusIndicator').classList.add('active');
            document.getElementById('liveStats').style.display = 'block';
            
            // Update live stats
            trackingInterval = setInterval(updateLiveStats, 1000);
            
            console.log('✓ Function tracking started');
        }
        
        function stopTracking() {
            const report = window.FunctionTracker.stop();
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('downloadBtn').disabled = false;
            document.getElementById('statusIndicator').classList.remove('active');
            
            clearInterval(trackingInterval);
            
            displayReport(report);
            console.log('✓ Function tracking stopped');
        }
        
        function downloadReport() {
            window.FunctionTracker.downloadReport();
        }
        
        function clearReport() {
            document.getElementById('reportDisplay').style.display = 'none';
            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('liveStats').style.display = 'none';
        }
        
        function updateLiveStats() {
            if (window.FunctionTracker.isTracking()) {
                const report = window.FunctionTracker.getReport();
                document.getElementById('calledCount').textContent = report.functionsCalled.length;
                document.getElementById('duration').textContent = Math.floor(report.duration / 1000);
            }
        }
        
        function displayReport(report) {
            const reportDiv = document.getElementById('reportDisplay');
            const contentDiv = document.getElementById('reportContent');
            
            contentDiv.innerHTML = `
                <div><strong>Summary:</strong></div>
                <div>Duration: ${Math.floor(report.duration / 1000)}s</div>
                <div>Functions Used: ${report.functionsCalled.length}</div>
                <div>Usage: ${report.summary.usagePercentage}%</div>
                
                <div class="function-list">
                    <div><strong>Used Functions:</strong></div>
                    ${report.functionsCalled.map(f => 
                        `<div class="function-item function-used">${f}</div>`
                    ).join('')}
                    
                    <div class="mt-2"><strong>Unused Functions:</strong></div>
                    ${report.functionsNotCalled.map(f => 
                        `<div class="function-item function-unused">${f}</div>`
                    ).join('')}
                </div>
            `;
            
            reportDiv.style.display = 'block';
        }
        
        // Test functions for manual testing
        function testFunction(funcName) {
            if (typeof window[funcName] === 'function') {
                window[funcName]();
                console.log(`✓ Called ${funcName}()`);
            } else {
                console.log(`✗ Function ${funcName}() not found`);
                // Create a dummy function for testing
                window[funcName] = function() {
                    console.log(`Dummy ${funcName}() called`);
                };
                window[funcName]();
            }
        }
        
        // Add some dummy functions for testing
        window.smartBack = function() { console.log('smartBack called'); };
        window.showModal = function() { console.log('showModal called'); };
        window.validateForm = function() { console.log('validateForm called'); };
        window.likeEvent = function() { console.log('likeEvent called'); };
        window.initMap = function() { console.log('initMap called'); };
        window.deleteEvent = function() { console.log('deleteEvent called'); };
    </script>
</body>
</html>
