{% extends "base.html" %}

{% block title %}User Management - Footprints{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 fw-bold">
            <span class="position-relative">
                User Management
                <span class="position-absolute start-0 bottom-0"
                    style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
            </span>
        </h1>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ url_for('user.get_users', active_tab=active_tab) }}" method="get" class="mb-0">
                <div class="d-flex">
                    <div class="input-group flex-grow-1">
                        <input type="text" name="q" id="searchInput" class="form-control" placeholder="Search users..."
                            value="{{ search_term }}">
                        {% if search_term %}
                        <a href="{{ url_for('user.get_users', active_tab=active_tab) }}"
                            class="btn btn-outline-secondary border-start-0">
                            <i class="bi bi-x"></i>
                        </a>
                        {% endif %}
                        <input type="hidden" name="active_tab" value="{{ active_tab }}">
                    </div>
                    <button type="submit" class="btn btn-outline-secondary ms-2">Search</button>
                </div>
                <div class="form-text text-muted mt-2">
                    <i class="bi bi-info-circle"></i> Search for users to manage their accounts, roles, and permissions.
                </div>
            </form>
        </div>
    </div>

    <div class="mb-4">
        <div class="border-bottom position-relative">
            <div class="d-flex">
                <div class="me-4 position-relative">
                    <a href="{{ url_for('user.get_users', active_tab='all', q=search_term) }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'all' %}text-primary{% else %}text-secondary{% endif %}">
                        All
                    </a>
                    {% if active_tab == 'all' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('user.get_users', active_tab='staff', q=search_term) }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'staff' %}text-primary{% else %}text-secondary{% endif %}">
                        Staff
                    </a>
                    {% if active_tab == 'staff' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('user.get_users', active_tab='blocked', q=search_term) }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'blocked' %}text-primary{% else %}text-secondary{% endif %}">
                        Blocked
                    </a>
                    {% if active_tab == 'blocked' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>
                <div class="position-relative">
                    <a href="{{ url_for('user.get_users', active_tab='banned', q=search_term) }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'banned' %}text-primary{% else %}text-secondary{% endif %}">
                        Banned
                    </a>
                    {% if active_tab == 'banned' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if active_tab == 'blocked' %}
    <div class="mt-3 alert alert-warning d-flex align-items-center">
        <span class="badge bg-warning-subtle text-warning border border-warning-subtle me-2">
            <i class="bi bi-lock-fill"></i>
        </span>
        <span>Blocked users can still use the system but cannot share their journeys publicly.</span>
    </div>
    {% elif active_tab == 'banned' %}
    <div class="mt-3 alert alert-danger d-flex align-items-center">
        <span class="badge bg-danger-subtle text-danger border border-danger-subtle me-2">
            <i class="bi bi-person-x-fill"></i>
        </span>
        <span>Banned users cannot log in to the system at all.</span>
    </div>
    {% elif active_tab == 'staff' %}
    <div class="mt-3 alert alert-info d-flex align-items-center">
        <span class="badge bg-info-subtle text-info border border-info-subtle me-2">
            <i class="bi bi-people-fill"></i>
        </span>
        <span>Staff members have elevated privileges to manage content and users.</span>
    </div>
    {% endif %}

    {% if users %}
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>User</th>
                    <th>Email</th>
                    <th>Name</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ (page - 1) * 10 + loop.index }}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-container me-2 flex-shrink-0">
                                {% if user.profile_image %}
                                <img src="{{ url_for('static', filename='uploads/profile_images/' + user.profile_image) }}"
                                    alt="{{ user.username }}"
                                    style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"
                                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                                {% else %}
                                <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                                    alt="Profile Placeholder"
                                    style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                                {% endif %}
                            </div>
                            <span class="fw-medium">{{ user.username }}</span>
                        </div>
                    </td>
                    <td>{{ user.email }}</td>
                    <td>
                        {% if user.first_name or user.last_name %}
                        {{ user.first_name or '' }} {{ user.last_name or '' }}
                        {% else %}
                        <em class="text-muted">Not specified</em>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge rounded-pill {{ get_role_badge_class(user.role) }} border-0 px-3 py-2">
                            <i class="{{ get_role_icon(user.role) }} me-1"></i>{{ user.role|default('User')|capitalize
                            }}
                        </span>
                    </td>
                    <td>
                        <div class="d-flex flex-wrap gap-1">
                            {% if user.is_banned|default(false) %}
                            <span class="badge rounded-pill bg-danger-subtle text-danger border-0 px-3 py-2"
                                style="min-width: 90px; text-align: center;">
                                <i class="bi bi-person-x-fill me-1"></i>Banned
                            </span>
                            {% endif %}
                            {% if user.is_blocked|default(false) %}
                            <span class="badge rounded-pill bg-warning-subtle text-warning border-0 px-3 py-2"
                                style="min-width: 90px; text-align: center;">
                                <i class="bi bi-lock-fill me-1"></i>Blocked
                            </span>
                            {% endif %}
                            {% if not user.is_banned|default(false) and not user.is_blocked|default(false) %}
                            <span class="badge rounded-pill bg-success-subtle text-success border-0 px-3 py-2"
                                style="min-width: 90px; text-align: center;">
                                <i class="bi bi-check-circle-fill me-1"></i>Active
                            </span>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        <a href="{{ url_for('user.get_user', user_id=user.id, active_tab='profile', q=search_term) }}"
                            class="btn btn-outline-dark btn-sm">View</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center"
            style="--bs-pagination-active-bg: #000; --bs-pagination-active-border-color: #000; --bs-pagination-color: #000; --bs-pagination-hover-color: #000; --bs-pagination-focus-color: #000;">
            {% if page > 1 %}
            <li class="page-item">
                <a class="page-link border-0"
                    href="{{ url_for('user.get_users', page=page-1, q=search_term, active_tab=active_tab) }}"
                    aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% for p in range(1, total_pages + 1) %}
            {% if p == page %}
            <li class="page-item active">
                <span class="page-link border-0">{{ p }}</span>
            </li>
            {% else %}
            <li class="page-item">
                <a class="page-link border-0"
                    href="{{ url_for('user.get_users', page=p, q=search_term, active_tab=active_tab) }}">{{ p }}</a>
            </li>
            {% endif %}
            {% endfor %}

            {% if page < total_pages %} <li class="page-item">
                <a class="page-link border-0"
                    href="{{ url_for('user.get_users', page=page+1, q=search_term, active_tab=active_tab) }}"
                    aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
                </li>
                {% endif %}
        </ul>
    </nav>
    {% else %}
    <div class="alert alert-info">
        <p class="mb-0"><i class="bi bi-info-circle me-2"></i>No users found. Try a different search term or view all
            users.</p>
    </div>
    {% endif %}
</div>
{% endblock %}