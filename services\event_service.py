"""
Event Service Module

This module handles all event-related operations including:
- Event creation, reading, updating and deletion
- Event image management (multiple images with primary designation)
- Event location handling
- Event permissions and access control
"""

import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from werkzeug.datastructures import FileStorage

from data import community_data, event_data, location_data, user_data
from services import journey_service, subscription_service
from utils.file_utils import save_event_image, delete_file
from utils.logger import get_logger

# Set up logging
logger = get_logger(__name__)

# ===== Core Event Operations =====

def create_event(
    journey_id: int,
    user_id: int,
    location_name: str,
    title: str,
    description: str,
    start_datetime: datetime,
    end_datetime: Optional[datetime] = None,
    images: Optional[List[FileStorage]] = None,
    longitude: Optional[float] = None,
    latitude: Optional[float] = None,
) -> Tuple[bool, str, Optional[int]]:
    """Create a new event.

    Args:
        journey_id: ID of the journey
        user_id: ID of the user creating the event
        location_name: Name of the location
        title: Event title
        description: Event description
        start_datetime: Start date and time
        end_datetime: End date and time
        images: List of event image files
        longitude: Longitude of location
        latitude: Latitude of location

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - int or None: Event ID if successful, None otherwise
    """
    try:
        # Check if journey exists and user has permission
        success, message, journey = journey_service.get_journey(journey_id, user_id)
        if not success:
            logger.info(f"Event creation failed: {message}")
            return False, message, None

        # Only the owner can add events to their journey
        if journey['user_id'] != user_id:
            logger.info(f"User {user_id} attempted to add event to journey {journey_id} without permission")
            return False, "Only the owner can add events to a journey", None

        # Check if start_datetime is before end_datetime (if provided)
        if end_datetime and start_datetime >= end_datetime:
            logger.info(f"Event creation failed: Invalid datetime range")
            return False, "End date and time must be after start date and time", None

        # Get or create location
        location = location_data.get_location_by_name(location_name)
        if location:
            location_id = location['id']
            logger.debug(f"Using existing location: {location_name} (ID: {location_id})")
        else:
            location_id = location_data.create_location(location_name, longitude, latitude)
            logger.debug(f"Created new location: {location_name} (ID: {location_id})")


        # Create event
        event_id = event_data.create_event(
            journey_id=journey_id,
            location_id=location_id,
            title=title,
            description=description,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
        )

        # Process images if provided
        if images:
            for i, image in enumerate(images):
                if image and hasattr(image, 'filename') and image.filename:
                    filename = save_event_image(image)
                    logger.debug(f"Event image saved: {filename}")

                    # First image is the primary by default
                    is_primary = (i == 0)
                    image_id = event_data.add_event_image(
                        event_id=event_id,
                        image_filename=filename,
                        caption=None, # caption not used currently
                        is_primary=is_primary
                    )
                    logger.debug(f"Added image {image_id} to event {event_id}")

        logger.info(f"Event created successfully: ID {event_id} for journey {journey_id}")
        return True, "Event created successfully", event_id
    except ValueError as e:
        logger.error(f"Value error creating event: {str(e)}")
        return False, str(e), None
    except Exception as e:
        logger.error(f"Exception creating event: {str(e)}", exc_info=True)
        return False, f"Event creation failed: {str(e)}", None


def get_event(event_id: int, user_id: int) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
    """Get an event by ID.

    Args:
        event_id: ID of the event
        user_id: ID of the user requesting the event

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - dict or None: Event data if successful, None otherwise
    """
    try:
        event = event_data.get_event(event_id)

        if not event:
            logger.info(f"Event {event_id} not found")
            return False, "Event not found", None

        # Check if the associated journey is accessible to the user
        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"User {user_id} does not have permission to view event {event_id}")
            return False, "You do not have permission to view this event", None

        # Get primary image for the event
        primary_image = event_data.get_primary_event_image(event_id)
        if primary_image:
            event['primary_image'] = primary_image

        # Get all images for the event
        images = event_data.get_event_images(event_id)
        event['images'] = images

        logger.debug(f"Event {event_id} retrieved successfully for user {user_id}")
        return True, "Event retrieved successfully", event
    except Exception as e:
        logger.error(f"Error retrieving event {event_id}: {str(e)}", exc_info=True)
        return False, f"Error retrieving event: {str(e)}", None

def get_journey_events(journey_id: int, user_id: Optional[int] = None) -> Tuple[bool, str, Optional[List[Dict[str, Any]]]]:
    """Get all events for a journey.

    Args:
        journey_id: ID of the journey
        user_id: ID of the user requesting the events. If None, only public/published journeys are accessible.

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - list or None: List of event data if successful, None otherwise
    """
    try:
        # For anonymous users, check if journey is published
        if user_id is None:
            journey = journey_service.get_published_journey(journey_id)
            if not journey:
                logger.info(f"Anonymous user attempted to access events for non-published journey {journey_id}")
                return False, "Journey not found", None
        else:
            # For logged-in users, check permissions
            success, message, journey = journey_service.get_journey(journey_id, user_id)
            if not success:
                logger.info(f"Journey events retrieval failed: {message}")
                return False, message, None

        # Get all events for the journey
        events = event_data.get_journey_events(journey_id)

        # Get primary image for each event
        for event in events:
            primary_image = event_data.get_primary_event_image(event['id'])
            if primary_image:
                event['primary_image'] = primary_image

        logger.debug(f"Retrieved {len(events)} events for journey {journey_id}")
        return True, "Events retrieved successfully", events
    except Exception as e:
        logger.error(f"Error retrieving events for journey {journey_id}: {str(e)}", exc_info=True)
        return False, f"Error retrieving events: {str(e)}", None


def update_event(
    event_id: int,
    user_id: int,
    title: Optional[str] = None,
    description: Optional[str] = None,
    start_datetime: Optional[Any] = None,
    end_datetime: Optional[Any] = None,
    location_name: Optional[str] = None,
    images: Optional[Dict[str, Any]] = None,
    edit_reason: Optional[str] = None,
    longitude: Optional[float] = None,
    latitude: Optional[float] = None,
    staff_location_scope: Optional[str] = None
) -> Tuple[bool, str]:
    """
    Update event details and handle related images.

    Args:
        event_id: ID of the event
        user_id: ID of the user updating the event
        title: New title (optional)
        description: New description (optional)
        start_datetime: New start datetime (optional)
        end_datetime: New end datetime (optional)
        location_name: New location name (optional)
        images: Dictionary of images to add/delete (optional)
        edit_reason: Reason for the edit (required for staff edits)
        longitude: Longitude of the updated location (float)
        latitude: Latitude of the updated location (float)
        staff_location_scope: For staff edits - 'all_events' or 'this_event_only' (optional)

    Returns:
        Tuple[bool, str]: Success flag and message
    """

    try:
        # Step 1: Validate permissions
        success, message, event = get_event(event_id, user_id)
        if not success:
            logger.info(f"Event {event_id} not found or user {user_id} unauthorized")
            return False, message

        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"Event update failed when checking journey: {message}")
            return False, message

        # Check if user is the owner or a content manager staff
        is_owner = journey['user_id'] == user_id
        user = user_data.get_user_by_id(user_id)
        from utils.permissions import PermissionGroups
        is_staff = user and user.get('role') in PermissionGroups.CONTENT_MANAGERS

        if not (is_owner or is_staff):
            logger.warning(f"User {user_id} attempted to update event {event_id} without permission")
            return False, "You do not have permission to update this event"

        # If staff is editing someone else's event, require edit reason
        if is_staff and not is_owner:
            if not edit_reason or not edit_reason.strip():
                logger.warning(f"Staff user {user_id} attempted to edit event {event_id} without providing a reason")
                return False, "Staff must provide a reason when editing a user's event"

        # Check if journey has no_edits flag (prevents staff edits)
        if not is_owner and journey.get('no_edits', False):
            logger.warning(f"Staff user {user_id} attempted to edit event {event_id} in a protected journey")
            return False, "This journey is protected from edits by staff"

        # Content managers (staff) who are not owners can only edit title, description, and location_name
        # They cannot modify start_datetime, end_datetime, longitude, or latitude
        if is_staff and not is_owner:
            # Check if staff is trying to modify restricted fields
            current_event = event_data.get_event(event_id)
            if current_event:
                # Check if datetime fields are being modified
                if start_datetime is not None:
                    current_start = current_event.get('start_datetime')
                    if current_start != start_datetime:
                        logger.warning(f"Staff user {user_id} attempted to modify start_datetime for event {event_id}")
                        return False, "Content managers cannot modify event start date/time. Only the journey owner can change event dates."

                if end_datetime is not None:
                    current_end = current_event.get('end_datetime')
                    if current_end != end_datetime:
                        logger.warning(f"Staff user {user_id} attempted to modify end_datetime for event {event_id}")
                        return False, "Content managers cannot modify event end date/time. Only the journey owner can change event dates."

                # Check if location coordinates are being modified
                if longitude is not None:
                    current_longitude = current_event.get('location_longitude')
                    if current_longitude != longitude:
                        logger.warning(f"Staff user {user_id} attempted to modify longitude for event {event_id}")
                        return False, "Content managers cannot modify event location coordinates. Only the journey owner can change event location on map."

                if latitude is not None:
                    current_latitude = current_event.get('location_latitude')
                    if current_latitude != latitude:
                        logger.warning(f"Staff user {user_id} attempted to modify latitude for event {event_id}")
                        return False, "Content managers cannot modify event location coordinates. Only the journey owner can change event location on map."

        # Update location if provided
        location_id = None
        if location_name:
            location = location_data.get_location_by_name(location_name)

            # Handle staff location scope choice
            if is_staff and not is_owner and staff_location_scope:
                current_event = event_data.get_event(event_id)
                current_location_name = current_event.get('location_name', '')

                if location_name != current_location_name:
                    # Staff is changing the location name
                    if staff_location_scope == 'all_events':
                        # Update existing location (affects all events)
                        # Find the current location by its current name
                        current_location = location_data.get_location_by_name(current_location_name)
                        if current_location:
                            # Update the existing location's name
                            from services import location_service
                            success_loc, message_loc = location_service.update_location(
                                location_id=current_location['id'],
                                name=location_name,
                                user_id=user_id,
                                edit_reason=edit_reason
                            )
                            if not success_loc:
                                logger.error(f"Failed to update location name: {message_loc}")
                                return False, f"Failed to update location name: {message_loc}"
                            location_id = current_location['id']
                        else:
                            # This shouldn't happen, but handle gracefully
                            logger.warning(f"Current location not found for staff edit: {current_location_name}")
                            return False, "Current location not found in database"

                    elif staff_location_scope == 'this_event_only':
                        # Create new location (affects only this event)
                        # Get existing coordinates from current event
                        existing_longitude = current_event.get('location_longitude')
                        existing_latitude = current_event.get('location_latitude')

                        # Create new location with corrected name but same coordinates
                        location_id = location_data.create_location(
                            location_name,
                            existing_longitude,
                            existing_latitude
                        )
                        if not location_id:
                            logger.error(f"Failed to create new location '{location_name}'")
                            return False, f"Failed to create new location '{location_name}'"
                else:
                    # No name change, use existing location
                    location_id = location['id'] if location else None
            else:
                # Regular location handling (for owners or non-staff)
                if location:
                    location_id = location['id']
                else:
                    # If coordinates are not provided (e.g., staff edit), preserve existing coordinates
                    if longitude is None or latitude is None:
                        current_event = event_data.get_event(event_id)
                        if current_event:
                            existing_longitude = current_event.get('location_longitude')
                            existing_latitude = current_event.get('location_latitude')
                            # Use existing coordinates if available
                            if longitude is None and existing_longitude is not None:
                                longitude = existing_longitude
                            if latitude is None and existing_latitude is not None:
                                latitude = existing_latitude

                    # Create new location
                    location_id = location_data.create_location(location_name, longitude, latitude)
                    if not location_id:
                        logger.error(f"Failed to create location '{location_name}'")
                        return False, f"Failed to create location '{location_name}'"

        # Track field changes for all users to detect if any changes were made
        field_changes = {}
        has_changes = False

        # For staff location edits, we need to track the original location name before it gets updated globally
        original_location_name = event['location_name']

        # Check for changes in all fields
        if title is not None and title != event['title']:
            field_changes['title'] = (event['title'], title)
            has_changes = True
        if description is not None and description != event['description']:
            field_changes['description'] = (event['description'], description)
            has_changes = True
        if location_name is not None and location_name != original_location_name:
            field_changes['location_name'] = (original_location_name, location_name)
            has_changes = True

        # Format datetime changes
        if start_datetime is not None:
            old_start = event['start_datetime'].strftime('%Y-%m-%d %H:%M:%S') if event['start_datetime'] else 'None'
            new_start = start_datetime.strftime('%Y-%m-%d %H:%M:%S') if start_datetime else 'None'
            if old_start != new_start:
                field_changes['start_datetime'] = (old_start, new_start)
                has_changes = True

        if end_datetime is not None:
            old_end = event['end_datetime'].strftime('%Y-%m-%d %H:%M:%S') if event.get('end_datetime') else 'None'
            new_end = end_datetime.strftime('%Y-%m-%d %H:%M:%S') if end_datetime else 'None'
            if old_end != new_end:
                field_changes['end_datetime'] = (old_end, new_end)
                has_changes = True

        # Check for image changes
        has_image_changes = images and ("new" in images or "deleted" in images)

        # If there are no changes at all, return appropriate message
        if not has_changes and not has_image_changes:
            logger.info(f"No changes detected for event {event_id} by user {user_id}")
            return True, "No changes were made to the event. The event remains unchanged."

        # Step 2: Update event details in database
        rows = event_data.update_event(
            event_id=event_id,
            title=title,
            description=description,
            location_id=location_id,
            start_datetime=start_datetime,
            end_datetime=end_datetime
        )

        # Step 3: Handle deleted images
        if images and "deleted" in images:
            for image_id in images["deleted"]:
                success, message = delete_event_image(image_id=image_id, user_id=user_id)
                if not success:
                    return False, f"Failed to delete image ID {image_id}: {message}"
                if is_staff and not is_owner:
                    field_changes['images'] = field_changes.get('images', []) + [(f"Deleted image ID {image_id}", "")]

        # Step 4: Handle new image uploads
        if images and "new" in images:
            for image_data in images["new"]:
                image_file = image_data["file"]
                if image_file and hasattr(image_file, "filename") and image_file.filename:
                    success, message, image_id = add_event_image(
                        event_id=event_id,
                        user_id=user_id,
                        image=image_file,
                        caption=None,         # No caption fields in form
                        is_primary=False      # Set manually if needed
                    )
                    if not success:
                        return False, f"Failed to add new image: {message}"
                    if is_staff and not is_owner:
                        field_changes['images'] = field_changes.get('images', []) + [("", f"Added new image ID {image_id}")]

        # If staff edited (not owner) and changes were made, record edit history
        # Note: For location name changes with "update everywhere", the event record itself may not be updated
        # but we still want to log the change for notification purposes
        if is_staff and not is_owner and field_changes:
            from services import edit_history_service
            success, message, _ = edit_history_service.record_edit(
                editor_id=user_id,
                content_type='event',
                content_id=event_id,
                field_changes=field_changes,
                reason=edit_reason.strip()
            )
            if not success:
                logger.warning(f"Failed to record edit history: {message}")
                # Continue anyway, as the event was successfully updated

        return True, "Event updated successfully"

    except Exception as e:
        logger.error(f"Error updating event {event_id}: {str(e)}", exc_info=True)
        return False, f"Error updating event: {str(e)}"

def delete_event(event_id: int, user_id: int) -> Tuple[bool, str]:
    """Delete an event.

    Args:
        event_id: ID of the event
        user_id: ID of the user making the deletion

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Check if event exists and user has permission
        success, message, event = get_event(event_id, user_id)
        if not success:
            logger.info(f"Event deletion failed: {message}")
            return False, message

        # Check if user is the owner or has admin/editor rights
        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"Event deletion failed when checking journey: {message}")
            return False, message

        # Check if user is the owner
        is_owner = journey['user_id'] == user_id

        # Check if user is an admin or editor (they have permission to edit public journeys)
        user = user_data.get_user_by_id(user_id)
        from utils.permissions import PermissionGroups
        is_admin_or_editor = user and user.get('role') in PermissionGroups.CONTENT_MANAGERS

        # Allow deletion only for owners or admins/editors with public journeys
        if not is_owner and not (is_admin_or_editor and journey.get('visibility') in ('public', 'published')):
            logger.info(f"User {user_id} attempted to delete event without permission")
            return False, "You do not have permission to delete this event"

        # Get all images for the event to delete them
        images = event_data.get_event_images(event_id)
        for image in images:
            try:
                delete_file(os.path.join('event_images', image['image_path']))
            except Exception as image_err:
                logger.warning(f"Failed to delete event image: {str(image_err)}")

        # Delete the event
        event_data.delete_event(event_id)

        logger.info(f"Event {event_id} deleted successfully by user {user_id}")
        return True, "Event deleted successfully"
    except Exception as e:
        logger.error(f"Error deleting event {event_id}: {str(e)}", exc_info=True)
        return False, f"Event deletion failed: {str(e)}"

def get_user_private_events(user_id: int) -> Tuple[bool, str, Optional[List[Dict[str, Any]]]]:
    """Get all events for a user.

    Args:
        user_id: ID of the user requesting the events. If None, only public/published journeys are accessible.

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - list or None: List of event data if successful, None otherwise
    """
    try:
        # Get all events for the journey
        events = event_data.get_user_private_events(user_id)

        if not events:
            logger.info(f"No events found for user {user_id}")
            return True, "No events found", []

        # Allow deletion only for owners or admins/editors with public journeys
        is_owner = events[0]['user_id'] == user_id
        if not is_owner:
            logger.info(f"User {user_id} attempted to access events owned by {events[0]['user_id']}")
            return False, "You do not have permission to view these events"

        logger.debug(f"Retrieved {len(events)} events for user {user_id}")
        return True, "Events retrieved successfully", events
    except Exception as e:
        logger.error(f"Error retrieving events for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Error retrieving events: {str(e)}", None


from typing import Tuple, Optional, List, Dict, Any

def get_user_public_events(user_id: int) -> Tuple[bool, str, Optional[List[Dict[str, Any]]]]:
    """Get all public events for a user.

    Args:
        user_id: ID of the user whose public events are requested.

    Returns:
        Tuple containing:
            - bool: Success status
            - str: Message describing the result
            - list or None: List of event data if successful, None otherwise
    """
    try:
        # Call data layer function to get public events
        events = event_data.get_user_public_events(user_id)

        if events is None or len(events) == 0:
            logger.info(f"No public events found for user {user_id}")
            return True, "No public events found", []

        logger.debug(f"Retrieved {len(events)} public events for user {user_id}")
        return True, "Public events retrieved successfully", events

    except Exception as e:
        logger.error(f"Error retrieving public events for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Error retrieving events: {str(e)}", None


# ===== Image Management =====

def update_event_image(
    image_id: int,
    user_id: int,
    caption: Optional[str] = None,
    is_primary: Optional[bool] = None,
    new_image: Optional[FileStorage] = None
) -> Tuple[bool, str]:
    """Update an event image.

    Args:
        image_id: ID of the image to update
        user_id: ID of the user making the update
        caption: New caption for the image
        is_primary: Whether this should be the primary image
        new_image: New image file to replace the existing one

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Get the image to find associated event
        image = event_data.get_event_image(image_id)
        if not image:
            logger.info(f"Image {image_id} not found")
            return False, "Image not found"

        # Check if user has permission to modify the event
        success, message, event = get_event(image['event_id'], user_id)
        if not success:
            logger.info(f"Image update failed: {message}")
            return False, message

        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"Event deletion failed when checking journey: {message}")
            return False, message

        # Only the journey owner can update images (no admin/editor exception)
        if journey['user_id'] != user_id:
            logger.info(f"User {user_id} attempted to update image without being the journey owner")
            return False, "Only the journey owner can update event images"

        # Process new image if provided
        new_filename = None
        if new_image and hasattr(new_image, 'filename') and new_image.filename:
            new_filename = save_event_image(new_image)
            logger.debug(f"New event image saved: {new_filename}")

            # Delete old image file if exists
            if image['image_filename']:
                try:
                    delete_file(image['image_filename'])
                    logger.debug(f"Deleted old image file: {image['image_filename']}")
                except Exception as file_err:
                    logger.warning(f"Failed to delete old image file: {str(file_err)}")

        # If setting as primary, unset any existing primary image
        if is_primary:
            existing_primary = event_data.get_primary_event_image(event['id'])
            if existing_primary and existing_primary['id'] != image_id:
                event_data.update_event_image(
                    image_id=existing_primary['id'],
                    is_primary=False
                )
                logger.debug(f"Unset existing primary image {existing_primary['id']}")

        # Update the image
        rows_affected = event_data.update_event_image(
            image_id=image_id,
            caption=caption,
            is_primary=is_primary,
            image_filename=new_filename
        )

        if rows_affected == 0:
            logger.warning(f"No changes made to image {image_id}")
            return False, "No changes were made to the image"

        logger.info(f"Image {image_id} updated successfully")
        return True, "Image updated successfully"
    except Exception as e:
        logger.error(f"Error updating image {image_id}: {str(e)}", exc_info=True)
        return False, f"Error updating image: {str(e)}"

def delete_event_image(image_id: int, user_id: int, edit_reason: str = None) -> Tuple[bool, str]:
    """Delete an event image.

    Args:
        image_id: ID of the image to delete
        user_id: ID of the user deleting the image
        edit_reason: Reason for deletion (required for staff editing other users' content)

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Get the image to find associated event and filename
        image = event_data.get_event_image(image_id)
        if not image:
            logger.info(f"Image {image_id} not found")
            return False, "Image not found"

        # Check if user has permission to modify the event
        success, message, event = get_event(image['event_id'], user_id)
        if not success:
            logger.info(f"Image deletion failed: {message}")
            return False, message

        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"Event deletion failed when checking journey: {message}")
            return False, message

        # Only the journey owner or content managers can delete images
        is_owner = journey['user_id'] == user_id
        user = user_data.get_user_by_id(user_id)
        from utils.permissions import PermissionGroups
        is_staff = user and user.get('role') in PermissionGroups.CONTENT_MANAGERS

        if not (is_owner or is_staff):
            logger.info(f"User {user_id} attempted to delete image without permission")
            return False, "Only the journey owner or content managers can delete event images"

        # If staff is editing someone else's content, require edit reason
        if is_staff and not is_owner:
            if not edit_reason or not edit_reason.strip():
                logger.warning(f"Staff user {user_id} attempted to delete image {image_id} without providing a reason")
                return False, "Staff must provide a reason when deleting a user's event image"

        # Store original filename for edit logging
        original_filename = image.get('image_filename', '')

        # Check if this is the primary image
        was_primary = image.get('is_primary', False)
        event_id = image['event_id']

        # Delete the image file
        if image['image_filename']:
            delete_file(image['image_filename'])
            logger.debug(f"Deleted image file: {image['image_filename']}")

        # Delete the database record
        rows_affected = event_data.delete_event_image(image_id)

        if rows_affected == 0:
            logger.warning(f"No image record found to delete for ID {image_id} - treating as successful since desired state is achieved")
            # Treat as success since the desired end state (image deleted) is already achieved

        # If this was the primary image, set a new primary image if available
        if was_primary:
            # Get remaining images for the event
            remaining_images = event_data.get_event_images(event_id)
            if remaining_images:
                # Set the first remaining image as primary
                event_data.update_event_image(
                    image_id=remaining_images[0]['id'],
                    is_primary=True
                )
                logger.debug(f"Set new primary image {remaining_images[0]['id']} after deleting primary")

        # If staff deleted (not owner), record edit history with meaningful counts
        if is_staff and not is_owner and edit_reason:
            from services import edit_history_service

            # Get image counts before and after deletion
            images_before = len(event_data.get_event_images(event_id)) + 1  # +1 because we just deleted one
            images_after = len(event_data.get_event_images(event_id))

            field_changes = {
                'event_images': (f"{images_before} images", f"{images_after} images")
            }
            success_log, message_log, _ = edit_history_service.record_edit(
                editor_id=user_id,
                content_type='event',
                content_id=event_id,
                field_changes=field_changes,
                reason=edit_reason.strip()
            )
            if not success_log:
                logger.warning(f"Failed to record edit history for image deletion: {message_log}")
                # Continue anyway, as the image was successfully deleted

        logger.info(f"Image {image_id} deleted successfully")
        return True, "Image deleted successfully"
    except Exception as e:
        logger.error(f"Error deleting image {image_id}: {str(e)}", exc_info=True)
        return False, f"Error deleting image: {str(e)}"

# ===== Multiple Event Images =====

def get_event_images(event_id: int, user_id: int) -> Tuple[bool, str, Optional[List[Dict[str, Any]]]]:
    """Get all images for an event.

    Args:
        event_id: ID of the event
        user_id: ID of the user requesting the images

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - list or None: List of image data if successful, None otherwise
    """
    try:
        # Check if user has permission to view the event
        success, message, event = get_event(event_id, user_id)
        if not success:
            logger.info(f"Image retrieval failed: {message}")
            return False, message, None

        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"Event retrieval failed when checking journey: {message}")
            return False, message, None

        # Get all images for the event
        images = event_data.get_event_images(event_id)

        # If user is not the journey owner, only show the primary image for non-premium journeys
        if journey['user_id'] != user_id:
            # Check if journey owner has active subscription
            owner_id = journey['user_id']
            owner_has_premium = subscription_service.check_can_use_premium_features(owner_id)

            # If journey owner doesn't have premium, only show primary image
            if not owner_has_premium:
                primary_image = next((img for img in images if img.get('is_primary')), None)
                images = [primary_image] if primary_image else []
                logger.debug(f"Non-owner user {user_id} viewing only primary image (owner lacks premium)")

        logger.debug(f"Retrieved {len(images)} images for event {event_id}")
        return True, "Images retrieved successfully", images
    except Exception as e:
        logger.error(f"Error retrieving images for event {event_id}: {str(e)}", exc_info=True)
        return False, f"Error retrieving images: {str(e)}", None

def add_event_image(
    event_id: int,
    user_id: int,
    image: FileStorage,
    caption: Optional[str] = None,
    is_primary: bool = False
) -> Tuple[bool, str, Optional[int]]:
    """Add an image to an event.

    Args:
        event_id: ID of the event
        user_id: ID of the user adding the image
        image: Image file
        caption: Optional caption for the image
        is_primary: Whether this should be the primary image

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - int or None: Image ID if successful, None otherwise
    """
    try:
        # Check if event exists and user has permission
        success, message, event = get_event(event_id, user_id)
        if not success:
            logger.info(f"Image addition failed: {message}")
            return False, message, None

        success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
        if not success:
            logger.info(f"Event retrieval failed when checking journey: {message}")
            return False, message, None

        # Only the journey owner can add images (no admin/editor exception)
        if journey['user_id'] != user_id:
            logger.info(f"User {user_id} attempted to add image without being the journey owner")
            return False, "Only the journey owner can add images to events", None

        # Check user's subscription status using subscription service
        subscription_status = subscription_service.get_user_subscription_status(user_id)

        # Get existing images count
        existing_images = event_data.get_event_images(event_id)

        user = user_data.get_user_by_id(user_id)

        # If subscription expired and event already has images, prevent adding more
        from utils.permissions import PermissionGroups
        if not subscription_status.get('is_premium') and existing_images and not user.get('role') in PermissionGroups.CONTENT_MANAGERS:
            logger.info(f"User {user_id} with expired subscription attempted to add image to event with existing images")
            return False, "Your subscription has expired. You can only add images to events that don't have any images yet.", None

        # Save the image file
        filename = save_event_image(image)
        logger.debug(f"Event image saved: {filename}")

        # If this is the first image, make it primary
        if not existing_images:
            is_primary = True
            logger.debug(f"Setting first image as primary for event {event_id}")

        # Add image record to database
        image_id = event_data.add_event_image(
            event_id=event_id,
            image_filename=filename,
            caption=caption,
            is_primary=is_primary
        )

        logger.info(f"Image added successfully to event {event_id}")
        return True, "Image added successfully", image_id
    except Exception as e:
        logger.error(f"Error adding image to event {event_id}: {str(e)}", exc_info=True)
        return False, f"Error adding image: {str(e)}", None

def delete_multiple_event_images(event_id: int, image_ids: List[int], user_id: int, edit_reason: str = None) -> Tuple[bool, str, int, int]:
    """Delete multiple event images with a single notification.

    Args:
        event_id: ID of the event
        image_ids: List of image IDs to delete
        user_id: ID of the user deleting the images
        edit_reason: Reason for deletion (required for staff editing other users' content)

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - int: Number of successfully deleted images
            - int: Number of failed deletions
    """
    if not image_ids:
        return False, "No images provided for deletion", 0, 0

    success_count = 0
    fail_count = 0
    deleted_filenames = []

    # Check permissions once for the event
    success, message, event = get_event(event_id, user_id)
    if not success:
        return False, message, 0, len(image_ids)

    success, message, journey = journey_service.get_journey(event['journey_id'], user_id)
    if not success:
        return False, message, 0, len(image_ids)

    # Check if user is the owner or content manager staff
    is_owner = journey['user_id'] == user_id
    user = user_data.get_user_by_id(user_id)
    from utils.permissions import PermissionGroups
    is_staff = user and user.get('role') in PermissionGroups.CONTENT_MANAGERS

    if not (is_owner or is_staff):
        return False, "Only the journey owner or content managers can delete event images", 0, len(image_ids)

    # If staff is editing someone else's content, require edit reason
    if is_staff and not is_owner:
        if not edit_reason or not edit_reason.strip():
            return False, "Staff must provide a reason when deleting a user's event images", 0, len(image_ids)

    # Delete each image
    for image_id in image_ids:
        try:
            # Get the image info before deletion
            image = event_data.get_event_image(image_id)
            if not image or image['event_id'] != event_id:
                fail_count += 1
                continue

            original_filename = image.get('image_filename', '')
            was_primary = image.get('is_primary', False)

            # Delete the image file
            if image['image_filename']:
                delete_file(image['image_filename'])
                logger.debug(f"Deleted image file: {image['image_filename']}")

            # Delete the database record
            rows_affected = event_data.delete_event_image(image_id)

            if rows_affected > 0:
                success_count += 1
                deleted_filenames.append(original_filename)

                # If this was the primary image, set a new primary image if available
                if was_primary:
                    remaining_images = event_data.get_event_images(event_id)
                    if remaining_images:
                        event_data.update_event_image(
                            image_id=remaining_images[0]['id'],
                            is_primary=True
                        )
                        logger.debug(f"Set new primary image {remaining_images[0]['id']} after deleting primary")
            else:
                fail_count += 1

        except Exception as e:
            logger.error(f"Error deleting image {image_id}: {str(e)}")
            fail_count += 1

    # If staff deleted (not owner) and some images were successfully deleted, record edit history with meaningful counts
    if is_staff and not is_owner and edit_reason and success_count > 0:
        from services import edit_history_service

        # Get current image count after deletions
        images_after = len(event_data.get_event_images(event_id))
        images_before = images_after + success_count

        # Create a single field change entry showing before/after counts
        field_changes = {
            'event_images': (f"{images_before} images", f"{images_after} images")
        }

        success_log, message_log, _ = edit_history_service.record_edit(
            editor_id=user_id,
            content_type='event',
            content_id=event_id,
            field_changes=field_changes,
            reason=edit_reason.strip()
        )
        if not success_log:
            logger.warning(f"Failed to record edit history for batch image deletion: {message_log}")

    # Create result message
    if success_count == 0:
        return False, "Failed to delete any images", 0, fail_count
    elif fail_count == 0:
        if success_count == 1:
            return True, "Image deleted successfully", success_count, 0
        else:
            return True, f"{success_count} images deleted successfully", success_count, 0
    else:
        return True, f"{success_count} images deleted successfully, {fail_count} failed", success_count, fail_count

# ===== Event Destination Management =====

def add_event_destination(event_id: int, user_id: int, location_name: str) -> Tuple[bool, str, Optional[int]]:
    """Add a destination to an event.

    Args:
        event_id: ID of the event
        user_id: ID of the user adding the destination
        location_name: Name of the destination location

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - int or None: Destination ID if successful, None otherwise
    """
    try:
        # Check if event exists and user has permission
        success, message, event = get_event(event_id, user_id)
        if not success:
            logger.info(f"Event destination addition failed: {message}")
            return False, message, None

        # Get or create destination location
        location = location_data.get_location_by_name(location_name)
        if location:
            destination_location_id = location['id']
        else:
            destination_location_id = location_data.create_location(location_name)

        # Add destination
        destination_id = event_data.add_event_destination(event_id, destination_location_id)

        logger.info(f"Destination added to event {event_id} by user {user_id}")
        return True, "Destination added successfully", destination_id
    except Exception as e:
        logger.error(f"Error adding destination to event {event_id}: {str(e)}", exc_info=True)
        return False, f"Destination addition failed: {str(e)}", None

# ===== Event Queries =====

def get_events_by_location(location_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get events by location.

    Args:
        location_id: ID of the location
        limit: Maximum number of events to return
        offset: Number of events to skip

    Returns:
        List of event objects for the location
    """
    try:
        events = event_data.get_events_by_location(location_id, limit, offset)
        return events
    except Exception as e:
        logger.error(f"Error retrieving events for location {location_id}: {str(e)}", exc_info=True)
        return []


def add_event_comment(event_id: int, user_id: int, content: str) -> Tuple[bool, str, Optional[int]]:
    """Add a comment to an event.

    Args:
        event_id: ID of the event the comment belongs to
        user_id: ID of the user adding the comment
        content: The content of the comment

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - int or None: ID of the created comment, or None if failed
    """
    try:
        # Validate event exists and user has permission to view
        success, message, event = get_event(event_id, user_id)
        if not success:
            logger.info(f"Cannot add comment: {message}")
            return False, message, None

        # Insert comment into database using community_data which includes notification logic
        comment_id = community_data.add_comment(user_id, event_id, content)

        logger.info(f"User {user_id} added comment {comment_id} to event {event_id}")
        return True, "Comment added successfully", comment_id
    except Exception as e:
        logger.error(f"Error adding comment to event {event_id}: {str(e)}", exc_info=True)
        return False, f"Failed to add comment: {str(e)}", None

def interact_with_comment(
    user_id: int,
    comment_id: int,
    interaction_type: str,

) -> Tuple[bool, str, Optional[int]]:
    """Interact with a comment (like, dislike, or report).

    Args:
        user_id: ID of the user interacting with the comment.
        comment_id: ID of the comment.
        interaction_type: Type of interaction ('like', 'dislike', or 'report').

    Returns:
        A tuple containing:
            - bool: Success status (True or False).
            - str: Message describing the result.
            - int: Interaction ID if successful, 0 if already exists, or None if failed.
    """
    try:
        # Call the data function to interact with the comment
        interaction_id = community_data.interact_with_comment(user_id, comment_id, interaction_type)

        if interaction_id == 0:
            # If the interaction already exists, return success with a message
            return True, f"User {user_id} has already {interaction_type}d this comment.", 0

        if interaction_id > 0:
            # Successfully added a new interaction
            return True, f"Successfully {interaction_type}d a comment", interaction_id

        if interaction_id == -1:
            # Successfully added a new interaction
            return True, f"Successfully un{interaction_type}d a comment", interaction_id

        # If interaction_id is None or an error occurs
        return False, "Failed to interact with comment", None

    except ValueError as e:
        # Catch specific ValueError if the interaction type is invalid or missing report reason
        logger.error(f"Invalid interaction: {str(e)}")
        return False, f"Invalid interaction: {str(e)}", None
    except Exception as e:
        # Log any other errors that may occur
        logger.error(f"Error interacting with comment {comment_id}: {str(e)}", exc_info=True)
        return False, f"Error interacting with comment: {str(e)}", None

# ===== New Image Functions (Preferred) =====

def get_event_images_list(event_id: int) -> List[Dict]:
    """Get all images for an event

    Args:
        event_id: ID of the event

    Returns:
        List of image dictionaries
    """
    try:
        return event_data.get_event_images_list(event_id)
    except Exception as e:
        logger.error(f"Error getting event images: {str(e)}")
        return []

def upload_event_image(event_id: int, user_id: int, file) -> Tuple[bool, str, Optional[int]]:
    """Upload an image for an event

    Args:
        event_id: ID of the event
        user_id: ID of the user uploading the image
        file: File object from request.files

    Returns:
        Tuple containing success flag, message, and image ID if successful
    """
    try:
        # First verify the user owns this event
        success, message, event = get_event(event_id, user_id)

        if not success:
            return False, "Event not found", None

        # Verify the journey belongs to the user
        journey_id = event.get('journey_id')
        success, message, journey = journey_service.get_journey(journey_id, user_id)

        if not success or journey.get('user_id') != user_id:
            return False, "You don't have permission to modify this event", None

        # Check premium restrictions for free users
        premium_access = subscription_service.check_can_use_premium_features(user_id)
        if not premium_access:
            existing_images = event_data.get_event_images(event_id)
            if existing_images and len(existing_images) >= 1:
                return False, "Free users can upload only one image", None

        # Validate using file_utils
        try:
            # The file_utils.save_event_image function already validates file type
            filename = save_event_image(file)
        except ValueError as e:
            return False, str(e), None

        if not filename:
            return False, "Failed to save file", None

        # Check if this is the first image
        is_first_image = len(event_data.get_event_images(event_id)) == 0

        # Add to database
        image_id = event_data.add_event_image(
            event_id=event_id,
            image_filename=filename,
            caption=None,
            is_primary=is_first_image
        )

        return True, "Image uploaded successfully", image_id

    except Exception as e:
        logger.error(f"Error uploading event image: {str(e)}")
        return False, "An error occurred while uploading the image", None

def delete_event_image_by_id(image_id: int, user_id: int) -> Tuple[bool, str, Optional[int]]:
    """Delete an event image by ID

    Args:
        image_id: ID of the image
        user_id: ID of the user deleting the image

    Returns:
        Tuple containing success flag, message, and event ID if successful
    """
    try:
        # Get the image info first
        image = event_data.get_event_image(image_id)
        if not image:
            return False, "Image not found", None

        event_id = image.get('event_id')
        if not event_id:
            return False, "Invalid image data", None

        # Get the event to verify ownership
        success, message, event = get_event(event_id, user_id)
        if not success:
            return False, "Event not found", None

        # Verify the journey belongs to the user
        journey_id = event.get('journey_id')
        success, message, journey = journey_service.get_journey(journey_id, user_id)

        if not success or journey.get('user_id') != user_id:
            return False, "You don't have permission to delete this image", None

        # Delete from database
        success, _ = delete_event_image(image_id, user_id)
        if not success:
            return False, "Failed to delete image from database", None

        return True, "Image deleted successfully", event_id

    except Exception as e:
        logger.error(f"Error deleting event image: {str(e)}")
        return False, "An error occurred while deleting the image", None


