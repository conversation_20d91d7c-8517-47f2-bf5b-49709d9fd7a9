from flask import url_for

# Test for /event/new/<journey_id> route (GET)
def test_get_event_form(client, mocker, mock_journey):
    with client.session_transaction() as session:
        session['user_id'] = 1

    mock_get_journey = mocker.patch(
        'services.journey_service.get_journey',
        return_value=(True, "Journey found", mock_journey)
    )

    response = client.get(url_for('event.get_event_form', journey_id=1))

    mock_get_journey.assert_called_once_with(1, user_id=1)
    assert response.status_code == 200
    assert b'Title' in response.data
    assert b'Description' in response.data
    assert b'Location' in response.data


# Test for /event/new/<journey_id> route (POST)
def test_create_event(client, mocker, mock_journey):
    with client.session_transaction() as session:
        session['user_id'] = 1

    mock_get_journey = mocker.patch(
        'services.journey_service.get_journey',
        return_value=(True, "Journey found", mock_journey)
    )

    mock_create_event = mocker.patch(
        'services.event_service.create_event',
        return_value=(True, "Event created successfully!", 1)
    )

    response = client.post(
        url_for('event.create_event', journey_id=1),
        data={
            'title': 'New Event',
            'description': 'Event description',
            'location': 'Test Location',
            'start_datetime': '2025-05-10T12:00',
            'end_datetime': '2025-05-10T14:00'
        },
        headers={'Referer': '/journey/private/1'}
    )

    assert response.status_code == 302
    assert response.headers['Location'] in url_for('journey.get_private_journey', journey_id=1)

    mock_get_journey.assert_called_once_with(journey_id=1, user_id=1)
    mock_create_event.assert_called_once()
    # checks that create_event was called with correct args
    args = mock_create_event.call_args[1]
    assert args['journey_id'] == 1
    assert args['user_id'] == 1
    assert args['title'] == 'New Event'
    assert args['description'] == 'Event description'
    assert args['location_name'] == 'Test Location'



# Test for invalid end date when creating an event
def test_create_event_invalid_end_date(client, mocker, mock_journey):
    with client.session_transaction() as session:
        session['user_id'] = 1

    mock_get_journey = mocker.patch(
        'services.journey_service.get_journey',
        return_value=(True, "Journey found", mock_journey)
    )

    response = client.post(
        url_for('event.create_event', journey_id=1),
        data={
            'title': 'New Event',
            'description': 'Event description',
            'location': 'Test Location',
            'start_datetime': '2025-05-10T14:00',  # end date is before start
            'end_datetime': '2025-05-10T12:00'
        }
    )

    # returns form with error message
    assert response.status_code == 200
    assert b'End date and time must be after start date and time' in response.data
    mock_get_journey.assert_called_once_with(journey_id=1, user_id=1)

# Test for /event/<event_id>/edit route (GET)
def test_get_event_edit_form(client, mocker, mock_journey, mock_event):
    with client.session_transaction() as session:
        session['user_id'] = 1

    mock_get_event = mocker.patch(
        'services.event_service.get_event',
        return_value=(True, "Event found", mock_event)
    )

    mock_get_journey = mocker.patch(
        'services.journey_service.get_journey',
        return_value=(True, "Journey found", mock_journey)
    )

    response = client.get(url_for('event.get_event_edit_form', event_id=1))

    assert response.status_code == 200
    assert b'Test Event' in response.data
    assert b'Test Description' in response.data
    assert b'Test Location' in response.data

    mock_get_event.assert_called_once_with(event_id=1, user_id=1)
    mock_get_journey.assert_called_once_with(1, user_id=1)

# Test for /event/<event_id>/edit route (POST)
def test_update_event(client, mocker, mock_event):
    with client.session_transaction() as session:
        session['user_id'] = 1
        session['journey_page'] = 'private'


    mock_get_event = mocker.patch(
        'services.event_service.get_event',
        return_value=(True, "Event found", mock_event)
    )

    mock_update_event = mocker.patch(
        'services.event_service.update_event',
        return_value=(True, "Event updated successfully!")
    )

    response = client.post(
        url_for('event.update_event', event_id=1),
        data={
            'title': 'Updated Event',
            'description': 'Updated description',
            'location': 'Updated Location',
            'start_datetime': '2025-05-10T12:00',
            'end_datetime': '2025-05-10T14:00'
        }
    )

    assert response.status_code == 302
    assert response.headers['Location'] in url_for('journey.get_private_journey', journey_id=1)

    mock_get_event.assert_called_once_with(event_id=1, user_id=1)
    mock_update_event.assert_called_once()
    # Check that update_event was called with correct args
    args = mock_update_event.call_args[1]
    assert args['event_id'] == 1
    assert args['user_id'] == 1
    assert args['title'] == 'Updated Event'
    assert args['description'] == 'Updated description'
    assert args['location_name'] == 'Updated Location'

# Test for /event/<event_id>/delete route (POST)
def test_delete_event(client, mocker):
    with client.session_transaction() as session:
        session['user_id'] = 1
        session['journey_page'] = 'private'

    mock_event = {
        "id": 1,
        "title": "Test Event",
        "journey_id": 1,
        "user_id": 1
    }

    mock_get_event = mocker.patch(
        'services.event_service.get_event',
        return_value=(True, "Event found", mock_event)
    )

    mock_delete_event = mocker.patch(
        'services.event_service.delete_event',
        return_value=(True, "Event deleted successfully!")
    )

    response = client.post(url_for('event.delete_event', event_id=1))

    assert response.status_code == 302
    assert response.headers['Location'] in url_for('journey.get_private_journey', journey_id=1)

    mock_get_event.assert_called_once_with(event_id=1, user_id=1)
    mock_delete_event.assert_called_once_with(event_id=1, user_id=1)


# Test for /event/<event_id>/detail route (GET) - Published Journey for Non-logged-in User
def test_get_event_details_published_journey_anonymous(client, mocker):
    """Test event detail page for published journey accessed by non-logged-in user"""
    mock_event = {
        "id": 1,
        "title": "Test Event",
        "description": "Test Description",
        "journey_id": 1,
        "user_id": 1,
        "location_name": "Test Location",
        "start_datetime": "2025-05-10T12:00:00",
        "end_datetime": "2025-05-10T14:00:00"
    }

    mock_journey = {
        "id": 1,
        "title": "Test Journey",
        "description": "Test Journey Description",
        "visibility": "published",
        "user_id": 1,
        "username": "testuser",
        "updated_at": "2025-01-01T12:00:00"
    }

    mock_get_event = mocker.patch(
        'services.event_service.get_event',
        return_value=(True, "Event found", mock_event)
    )

    mock_get_journey = mocker.patch(
        'services.journey_service.get_journey',
        return_value=(True, "Journey found", mock_journey)
    )

    # Mock subscription service for anonymous user
    mock_subscription = mocker.patch(
        'services.subscription_service.check_can_use_premium_features',
        return_value=False
    )

    response = client.get(url_for('event.get_event_details', event_id=1))

    assert response.status_code == 200
    assert b'Test Event' in response.data
    assert b'Test Description' in response.data

    # Verify the smart back button logic is present
    assert b'smartBack()' in response.data
    assert b'published' in response.data  # Journey visibility should be available in template

    # Verify that get_event was called with correct parameters (may be called multiple times)
    assert mock_get_event.call_count >= 1
    # Check that at least one call was with the expected parameters
    expected_call_found = any(
        call.kwargs.get('event_id') == 1 and call.kwargs.get('user_id') is None
        for call in mock_get_event.call_args_list
    )
    assert expected_call_found, f"Expected call not found in {mock_get_event.call_args_list}"

    # Verify that get_journey was called with correct parameters (may be called multiple times)
    assert mock_get_journey.call_count >= 1
    # Check that at least one call was with the expected parameters
    journey_call_found = any(
        (call.args == (1,) and call.kwargs.get('user_id') is None) or
        (len(call.args) >= 2 and call.args[0] == 1 and call.args[1] is None)
        for call in mock_get_journey.call_args_list
    )
    assert journey_call_found, f"Expected journey call not found in {mock_get_journey.call_args_list}"


# Test for /event/<event_id>/detail route (GET) - Published Journey for Logged-in User
def test_get_event_details_published_journey_logged_in(client, mocker):
    """Test event detail page for published journey accessed by logged-in user"""
    with client.session_transaction() as session:
        session['user_id'] = 2
        session['journey_page'] = 'published'

    mock_event = {
        "id": 1,
        "title": "Test Event",
        "description": "Test Description",
        "journey_id": 1,
        "user_id": 1,
        "location_name": "Test Location",
        "start_datetime": "2025-05-10T12:00:00",
        "end_datetime": "2025-05-10T14:00:00"
    }

    mock_journey = {
        "id": 1,
        "title": "Test Journey",
        "description": "Test Journey Description",
        "visibility": "published",
        "user_id": 1,
        "username": "testuser",
        "updated_at": "2025-01-01T12:00:00"
    }

    mock_get_event = mocker.patch(
        'services.event_service.get_event',
        return_value=(True, "Event found", mock_event)
    )

    mock_get_journey = mocker.patch(
        'services.journey_service.get_journey',
        return_value=(True, "Journey found", mock_journey)
    )

    mock_subscription = mocker.patch(
        'services.subscription_service.check_can_use_premium_features',
        return_value=False
    )

    response = client.get(url_for('event.get_event_details', event_id=1))

    assert response.status_code == 200
    assert b'Test Event' in response.data

    # Verify session context is available in template
    assert b'published' in response.data

    # Verify that get_event was called with correct parameters (may be called multiple times)
    assert mock_get_event.call_count >= 1
    # Check that at least one call was with the expected parameters
    expected_call_found = any(
        call.kwargs.get('event_id') == 1 and call.kwargs.get('user_id') == 2
        for call in mock_get_event.call_args_list
    )
    assert expected_call_found, f"Expected call not found in {mock_get_event.call_args_list}"

    # Verify that get_journey was called with correct parameters (may be called multiple times)
    assert mock_get_journey.call_count >= 1
    # Check that at least one call was with the expected parameters
    journey_call_found = any(
        (call.args == (1,) and call.kwargs.get('user_id') == 2) or
        (len(call.args) >= 2 and call.args[0] == 1 and call.args[1] == 2)
        for call in mock_get_journey.call_args_list
    )
    assert journey_call_found, f"Expected journey call not found in {mock_get_journey.call_args_list}"


# Test for /event/<event_id>/detail route (GET) - Public Journey for Non-logged-in User (Should Redirect)
def test_get_event_details_public_journey_anonymous_redirect(client, mocker):
    """Test event detail page for public journey accessed by non-logged-in user - should redirect to login"""
    mock_event = {
        "id": 1,
        "title": "Test Event",
        "description": "Test Description",
        "journey_id": 1,
        "user_id": 1,
        "location_name": "Test Location",
        "start_datetime": "2025-05-10T12:00:00",
        "end_datetime": "2025-05-10T14:00:00"
    }

    mock_journey = {
        "id": 1,
        "title": "Test Journey",
        "description": "Test Journey Description",
        "visibility": "public",  # Public journey, not published
        "user_id": 1,
        "username": "testuser",
        "updated_at": "2025-01-01T12:00:00"
    }

    # Mock event service to return permission error for anonymous user accessing public journey
    mock_get_event = mocker.patch(
        'services.event_service.get_event',
        return_value=(False, "You do not have permission to view this event", None)
    )

    response = client.get(url_for('event.get_event_details', event_id=1))

    # Should redirect to login page
    assert response.status_code == 302
    assert '/login' in response.headers['Location']

    # Verify the event service was called with anonymous user
    mock_get_event.assert_called_once_with(event_id=1, user_id=None)




