/**
 * Image Preview Styles
 * Centralized CSS for image preview functionality
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

/* Image Preview Container */
.image-preview-grid {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  margin-top: 12px;
}

.image-preview-grid.compact {
  padding: 12px;
  margin-top: 8px;
}

/* Preview Header */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 6px;
}

.preview-title i {
  color: #667eea;
  font-size: 16px;
}

/* Preview Cards */
.preview-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
  position: relative;
  height: 100%;
}

.preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.preview-card.compact {
  border-radius: 6px;
}

/* Preview Images */
.preview-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  transition: all 0.2s ease;
  display: block;
}

.preview-image.large {
  height: 160px;
}

.preview-image.small {
  height: 80px;
}

.preview-card:hover .preview-image {
  transform: scale(1.05);
}

/* Preview Info */
.preview-info {
  padding: 8px 10px;
  background: white;
}

.preview-info.compact {
  padding: 6px 8px;
}

.preview-filename {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.preview-filesize {
  font-size: 11px;
  color: #6c757d;
  margin: 2px 0 0 0;
  line-height: 1.2;
}

/* Remove Button */
.preview-remove {
  position: absolute;
  top: 6px;
  right: 6px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
  z-index: 10;
}

.preview-card:hover .preview-remove {
  opacity: 1;
}

.preview-remove:hover {
  background: #dc3545;
  transform: scale(1.1);
}

.preview-remove:focus {
  outline: 2px solid #dc3545;
  outline-offset: 2px;
}

/* Error States */
.preview-error {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.preview-error .preview-image {
  opacity: 0.5;
  filter: grayscale(50%);
}

.error-message {
  font-size: 11px;
  color: #721c24;
  margin: 2px 0 0 0;
  font-weight: 500;
  line-height: 1.2;
}

/* Loading States */
.preview-loading {
  position: relative;
  opacity: 0.7;
}

.preview-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Grid Layouts */
.preview-grid-2 .preview-card {
  /* 2 columns on all screens */
}

.preview-grid-3 .preview-card {
  /* 3 columns on desktop, 2 on tablet, 1 on mobile */
}

.preview-grid-4 .preview-card {
  /* 4 columns on desktop, 3 on tablet, 2 on mobile */
}

/* Single Image Preview */
.single-image-preview {
  max-width: 300px;
  margin: 12px 0;
}

.single-image-preview .preview-image {
  height: auto;
  max-height: 200px;
  border-radius: 8px;
}

/* Drag and Drop States */
.preview-dropzone {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.preview-dropzone.dragover {
  border-color: #667eea;
  background: #f0f4ff;
}

.preview-dropzone-text {
  color: #6c757d;
  font-size: 14px;
  margin: 8px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .image-preview-grid {
    padding: 12px;
    margin-top: 8px;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .preview-title {
    font-size: 13px;
    justify-content: center;
  }
  
  .preview-image {
    height: 100px;
  }
  
  .preview-info {
    padding: 6px 8px;
  }
  
  .preview-filename {
    font-size: 11px;
  }
  
  .preview-filesize {
    font-size: 10px;
  }
  
  .preview-remove {
    width: 20px;
    height: 20px;
    font-size: 10px;
    top: 4px;
    right: 4px;
  }
}

@media (max-width: 480px) {
  .image-preview-grid {
    padding: 8px;
  }
  
  .preview-image {
    height: 80px;
  }
  
  .preview-info {
    padding: 4px 6px;
  }
  
  .preview-filename {
    font-size: 10px;
  }
  
  .preview-filesize {
    font-size: 9px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .image-preview-grid {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .preview-card {
    background: #1a202c;
    border-color: #4a5568;
  }
  
  .preview-info {
    background: #1a202c;
  }
  
  .preview-filename {
    color: #e2e8f0;
  }
  
  .preview-filesize {
    color: #a0aec0;
  }
  
  .preview-title {
    color: #e2e8f0;
  }
  
  .preview-dropzone {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .preview-dropzone.dragover {
    background: #3d4852;
    border-color: #667eea;
  }
}

/* Animation Classes */
.preview-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.preview-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Accessibility */
.preview-remove:focus-visible {
  outline: 2px solid #dc3545;
  outline-offset: 2px;
}

.preview-card:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .image-preview-grid {
    display: none;
  }
}
