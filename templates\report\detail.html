{% extends "base.html" %}

{% block title %}
{{ event.title }} - Footprints
{% endblock %}

{% block content %}
<a href="javascript:void(0)" onclick="smartBack()"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back to Reports</span>
</a>
<div class="row g-4" id="eventContainer">
  <div class="col-md-5 col-12 mb-4 mb-md-0 {% if journey.visibility == 'private' %}mx-auto{% endif %}">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h1 class="fs-3 fw-bold mb-0">Event Details</h1>
          <div class="d-flex align-items-center gap-2">
            <i class="bi bi-hand-thumbs-up{% if user_liked_event %}-fill{% endif %}"></i>
            <span class="fw-medium">{{ event_likes|length|default(0) }}</span>
          </div>
        </div>
        <div class="mb-2">
          <span class="badge bg-success rounded-pill py-1 px-3">{{ journey.visibility|capitalize }}</span>
        </div>
        <div class="d-flex align-items-center mb-4">
          <div class="rounded-circle overflow-hidden me-3" style="width: 50px; height: 50px;">
            <img {% if journey.profile_image %}
              src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
              alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;"
              onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
            {% else %}
            <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
              alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;">
            {% endif %}
          </div>
          <div class="d-flex flex-column align-items-start">
            <div class="fw-semibold text-dark">{{ journey.username }}</div>
            <small class="text-muted">Created on {{ event.updated_at|date('F d, Y') }}</small>
          </div>
        </div>
        <!-- Journey Info Card -->
        <div class="card bg-light border-0 rounded-3 mb-3">
          <div class="card-body py-2 px-3">
            <div class="fw-semibold mb-1">{{ journey.title }}</div>
            <div class="text-muted small mb-1">{{ journey.description }}</div>
            <div class="d-flex align-items-center text-muted small">
              <i class="bi bi-clock-history me-2"></i>
              {{ journey.start_date|datetime }}
            </div>
          </div>
        </div>
        <div class="mb-4">
          <h6 class="text-uppercase text-muted small fw-bold">Title</h6>
          <p class="mb-0">{{ event.title }}</p>
        </div>
        <div class="d-flex gap-3">
          <div class="date-box">
            <h6 class="text-uppercase text-muted small fw-bold">Start Date and Time</h6>
            <p class="mb-0">{{ event.start_datetime | datetime}}</p>
          </div>
          {% if event.end_datetime %}
          <div class="date-box">
            <h6 class="text-uppercase text-muted small fw-bold">End Date and Time</h6>
            <p class="mb-4">{{ event.end_datetime | datetime}}</p>
          </div>
          {% endif %}
        </div>
        <div class="mb-4">
          <h6 class="text-uppercase text-muted small fw-bold">Description</h6>
          <p class="mb-0">{{ event.description }}</p>
        </div>
        <div class="mb-4">
          <h6 class="text-uppercase text-muted small fw-bold">Location</h6>
          <div class="location-container">
          </div>
        </div>
        <div class="mb-4">
          <h6 class="text-uppercase text-muted small fw-bold">Event Images</h6>
          {% if images %}
          <div class="image-wrapper position-relative d-inline-block">
            <button class="event-image-button" event-id="{{ event.id }}">
              <div class="event-image">
                <img src="{{ url_for('static', filename='uploads/event_images/' + images[0].image_filename) }}"
                  alt="Event image"
                  onerror="this.parentElement.parentElement.parentElement.style.display='none'; document.getElementById('noImagesMessage-{{ event.id }}').style.display='block';">
              </div>
            </button>
            {% if images|length > 1 %}
            <div class="image-indicator badge text-white rounded-circle">
              +{{ images|length - 1 }}
            </div>
            {% endif %}
          </div>
          {% endif %}

          <!-- No images message -->
          <div id="noImagesMessage-{{ event.id }}" {% if images %}style="display: none;" {% endif %}
            class="text-center p-3">
            <i class="bi bi-images fs-3 text-secondary"></i>
            <p class="text-muted mt-2 mb-0">No images available</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% if journey.visibility != 'private' %}
  <div class="col-md-7 col-12 d-flex flex-column h-100">
    <div class="card shadow-sm border-0 rounded-3 flex-fill mb-4" style="max-height: 500px;">
      <div class="card-body p-0">
        <h2 class="fs-4 fw-bold text-dark mb-3 px-4 pt-4">Comments</h2>
        <div class="comments-scroll px-4 pb-4">
          {% for comment in comments %}
          {% if comment.id == report.content_id %}
          <!-- Reported Comment -->
          <div id="reported-comment" class="mb-3 p-3 rounded-3" style="background:#fbeaea; border:2px solid #d32f2f;">
            <div class="d-flex align-items-center mb-2">
              <img {% if comment.profile_image
                %}src="{{ url_for('static', filename='uploads/profile_images/' + comment.profile_image) }}" {% else
                %}src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}" {% endif %}
                alt="{{ comment.username }}" class="rounded-circle me-2"
                style="width:36px; height:36px; object-fit:cover;">
              <div>
                <span class="fw-semibold">{{ comment.username }}</span>
                <span class="text-muted small ms-2"><i class="bi bi-clock-history me-1"></i>{{ comment.created_at
                  }}</span>
              </div>
              <span class="badge bg-danger ms-auto"><i class="bi bi-flag-fill me-1"></i>Reported</span>
            </div>
            <div class="mb-2">{{ comment.content }}</div>
            <div class="d-flex align-items-center gap-3">
              <i class="bi bi-hand-thumbs-up"></i> {{ comment.like_count|default(0) }}
              <i class="bi bi-hand-thumbs-down"></i> {{ comment.dislike_count|default(0) }}
            </div>
          </div>
          {% else %}
          <!-- Normal Comment -->
          <div class="mb-3 p-3 rounded-3 bg-light">
            <div class="d-flex align-items-center mb-2">
              <img {% if comment.profile_image
                %}src="{{ url_for('static', filename='uploads/profile_images/' + comment.profile_image) }}" {% else
                %}src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}" {% endif %}
                alt="{{ comment.username }}" class="rounded-circle me-2"
                style="width:36px; height:36px; object-fit:cover;">
              <div>
                <span class="fw-semibold">{{ comment.username }}</span>
                <span class="text-muted small ms-2"><i class="bi bi-clock-history me-1"></i>{{ comment.created_at
                  }}</span>
              </div>
            </div>
            <div class="mb-2">{{ comment.content }}</div>
            <div class="d-flex align-items-center gap-3">
              <i class="bi bi-hand-thumbs-up"></i> {{ comment.like_count|default(0) }}
              <i class="bi bi-hand-thumbs-down"></i> {{ comment.dislike_count|default(0) }}
            </div>
          </div>
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
    <div class="card shadow-sm border-0 rounded-3 flex-fill" style="min-height: 300px;">
      <div class="card-body">
        <h2 class="fs-4 fw-bold text-dark mb-3">Report Details</h2>
        <div class="mb-3 p-4 rounded-3" style="background:#fffbe6; border:1.5px solid #ffe082;">
          <div class="d-flex align-items-center mb-2">
            <i class="bi bi-clock-fill fs-5 me-2"></i>
            <span class="fw-bold fs-5">
              {% if report.status == 'dismissed' %}Report Dismissed{% elif report.escalated_to_admin %}Escalated to
              Admin{% elif report.is_banned %}User Banned{% elif report.is_hidden %}Comment Hidden{% else %}Pending
              Review{% endif %}
            </span>
          </div>
          <div class="mb-2">
            <span class="text-muted">Reported by <b>{{ report.reporter_username }}</b></span>
            <span class="text-muted ms-3"><i class="bi bi-clock-history me-1"></i>{{ report.created_at }}</span>
          </div>
          <div class="mb-2"><span class="fw-semibold">Reason:</span> {{ report.reason }}</div>

        </div>
        <div class="d-flex gap-2 mt-3 flex-wrap justify-content-end flex-row">
          {% if report.status == 'dismissed' %}
          <button type="button" style="background:#f0f4ff; color:#2d3a5b;" class="btn btn-sm px-4 py-2 rounded-pill"
            class=" btn btn-sm" onclick="submitReportForm('undismiss')">Undismiss Report</button>
          {% else %}
          <button type="button" style="background:#f0f4ff; color:#2d3a5b;"" class=" btn btn-sm px-4 py-2 rounded-pill"
            onclick="submitReportForm('dismiss')">Dismiss</button>
          <button type="button" style="background:#e9e9e9; color:#444;" class=" btn btn-sm px-4 py-2 rounded-pill"
            onclick="submitReportForm('hide')">{{ 'Unhide Comment' if report.is_hidden else 'Hide
            Comment' }}</button>
          {% if not can_change_user_roles() %}
          <button type="button" style="background:#e9e9e9; color:#444;" class=" btn btn-sm px-4 py-2 rounded-pill"
            onclick="submitReportForm('escalate')">{{'Unreport to Admin' if report.escalated_to_admin
            else 'Report to admin'}}</button>
          {% else %}
          <button type="button" class=" btn btn-sm px-4 py-2 bth-dark round-pill" onclick="submitReportForm('ban')">{{
            'Unban User' if report.is_banned else 'Ban User'
            }}</button>
          {% endif %}
          {% endif %}
        </div>

        <form id="form-dismiss" action="{{ url_for('report.dismiss_comment_report', report_id=report.id) }}"
          method="post" style="display:none"></form>
        <form id="form-undismiss" action="{{ url_for('report.undismiss_comment_report', report_id=report.id) }}"
          method="post" style="display:none"></form>
        <form id="form-hide"
          action="{{ url_for('report.' + ('unhide_comment' if report.is_hidden else 'hide_comment'), comment_id=report.content_id, report_id=report.id) }}"
          method="post" style="display:none"></form>
        <form id="form-escalate"
          action="{{ url_for('report.' + ('unescalate_comment' if report.escalated_to_admin else 'escalate_comment'), report_id=report.id) }}"
          method="post" style="display:none"></form>
        <form id="form-ban"
          action="{{ url_for('report.' + ('unban_user' if report.is_banned else 'ban_user'), report_id=report.id) }}"
          method="post" style="display:none"></form>
      </div>
    </div>
  </div>
  {% endif %}
</div>

<script>
  const isLoggedIn = document.body.dataset.loggedIn === 'True' || document.body.dataset.loggedIn === 'true';

  // Simple and reliable back button function
  function smartBack() {
    // Method 1: Check for explicit back URL parameter (most reliable)
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
      try {
        const decodedUrl = decodeURIComponent(backUrl);
        // Only allow internal URLs for security
        if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
          window.location.href = decodedUrl;
          return;
        }
      } catch (e) {
        // Invalid URL, continue to next method
      }
    }

    // Method 2: Simple history back (works most of the time)
    if (window.history.length > 1) {
      window.history.back();
      return;
    }

    // Method 3: Fallback based on context
    if (isLoggedIn) {
      window.location.href = '{{ url_for("report.get_reports") }}';
    } else {
      window.location.href = '{{ url_for("main.get_landing_page") }}';
    }
  }


  // Update back button text based on context
  function updateBackButtonText() {
    const backButtonText = document.getElementById('backButtonText');
    const referrer = document.referrer;
    const currentDomain = window.location.origin;

    if (referrer && referrer.startsWith(currentDomain)) {
      if (referrer.includes('/report/list') || referrer.includes('/report')) {
        backButtonText.textContent = 'Back to Reports';
      } else if (referrer.includes('/admin') || referrer.includes('/dashboard')) {
        backButtonText.textContent = 'Back to Dashboard';
      } else {
        backButtonText.textContent = 'Back';
      }
    } else {
      // No referrer or external referrer
      backButtonText.textContent = 'Back to Reports';
    }
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', function () {
    updateBackButtonText();
    const reported = document.getElementById('reported-comment');
    if (reported) {
      reported.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  });

  function submitReportForm(action) {
    document.getElementById('form-' + action).submit();
  }
</script>

<style>
  .comments-scroll {
    max-height: 300px;
    overflow-y: auto;
  }
</style>

{% endblock %}