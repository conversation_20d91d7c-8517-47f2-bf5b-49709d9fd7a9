{% extends "base.html" %}

{% block title %}Support Tech Dashboard - Footprints{% endblock %}

{% block content %}

<div class="container py-2">
    <!-- Welcome Section -->
    <div class="mb-4 p-4"
        style="border-radius: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);">
        <div class="d-flex align-items-center">
            <div>
                <h1 class="h3 fw-bold mb-1">Welcome back, {{ session.username }}!</h1>
                <p class="text-muted mb-0 small">Ready to manage your support tech tasks?</p>
            </div>
        </div>
    </div>

    <!-- Announcements Section - Displays both read and unread announcements in tabs -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">New Announcements</h2>
            <div class="d-flex align-items-center">
                <a href="{{ url_for('announcement.get_user_unread_announcements') }}"
                    class="btn btn-outline-dark rounded-pill px-4">View All</a>
            </div>
        </div>

        <div class="tab-content" id="announcementTabsContent">
            <!-- Unread Announcements Tab -->
            <div class="tab-pane fade show active" id="unread-tab-pane" role="tabpanel" aria-labelledby="unread-tab"
                tabindex="0">
                {% if unread_announcements|length > 0 %}
                <div class="row g-4">
                    {% for announcement in unread_announcements[:3] %}
                    <div class="col-lg-4">
                        <div class="card h-100 border-0 shadow-sm rounded-4 announcement-card">
                            <div class="card-body p-4">
                                <span class="ms-auto text-muted small">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    {{ announcement['created_at'].strftime('%B %d, %Y') }}
                                </span>
                                <h3 class="h5 fw-bold mb-3">{{ announcement['title'] }}</h3>
                                <p class="text-muted mb-3 small announcement-content">{{ announcement['content'][:100]
                                    }}{% if
                                    announcement['content']|length > 100 %}...{% endif %}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="#" class="text-decoration-none read-more-link"
                                        data-title="{{ announcement['title'] }}"
                                        data-content="{{ announcement['content'] }}"
                                        data-date="{{ announcement['created_at'].strftime('%B %d, %Y') }}"
                                        data-id="{{ announcement['id'] }}"
                                        data-form-id="markAsReadForm_{{ announcement['id'] }}">
                                        Read more
                                    </a>
                                    <form method="POST"
                                        action="/announcement/{{ announcement['id'] }}/mark-dashboard-read"
                                        id="markAsReadForm_{{ announcement['id'] }}" style="display: none;"></form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert rounded-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle-fill me-3 fs-4"></i>
                        <p class="mb-0">You're all caught up! No unread announcements at the moment.</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>


    <!-- New Help Desk Tickets -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <div class="d-flex align-items-center gap-2 mb-0">
                <h2 class="mb-0">Help Desk Tickets</h2> <span class=" badge-sm">{{ tickets |
                    length }} new</span>
            </div>
            <a href="{{ url_for('helpdesk.get_tickets') }}" class="btn btn-outline-dark rounded-pill px-4">View
                All</a>
        </div>
        <div class="border rounded-4 p-3" style="max-height: 300px; overflow-y: auto;">
            {% if tickets %}
            <ul class="list-group list-group-flush">
                {% for ticket in tickets %}
                <li class="list-group-item d-flex justify-content-between align-items-start">
                    <div>
                        <div class="fw-bold text-truncate" style="max-width: 200px;" title="{{ ticket.subject }}">
                            {{ ticket.subject }}
                        </div>
                        <small class="text-muted">{{ ticket.created_at|datetime }}</small>
                    </div>

                    <div class="d-flex align-items-center gap-5">

                        {% if ticket.request_type == 'help' %}
                        <span class="badge rounded-pill bg-primary-subtle text-primary p-2"
                            style="min-width: 90px; text-align: center;">
                            <i class="bi bi-question-circle me-1"></i>Help
                        </span>
                        {% elif ticket.request_type == 'bug' %}
                        <span class="badge rounded-pill bg-danger-subtle text-danger p-2"
                            style="min-width: 90px; text-align: center;">
                            <i class="bi bi-bug me-1"></i>Bug
                        </span>
                        {% elif ticket.request_type == 'appeal' %}
                        <span class="badge rounded-pill bg-warning-subtle text-warning p-2"
                            style="min-width: 90px; text-align: center;">
                            <i class="bi bi-megaphone me-1"></i>Appeal
                        </span>
                        {% elif ticket.request_type == 'other' %}
                        <span class="badge rounded-pill bg-secondary-subtle text-secondary p-2"
                            style="min-width: 90px; text-align: center;">
                            <i class="bi bi-three-dots me-1"></i>Other
                        </span>
                        {% endif %}
                        <a href="{{ url_for('helpdesk.query_ticket', ticket_id=ticket.id) }}"
                            class="btn btn-sm btn-outline-dark">View</a>
                    </div>
                </li>


                {% endfor %}
            </ul>
            {% else %}
            <p class="text-muted mb-0">No new tickets.</p>
            {% endif %}
        </div>
    </div>




    <h2 class=" admin-card mb-0 pt-2">Quick Access</h2>
    <div class="row mb-4" style="display: flex; flex-wrap: wrap;">
        <div class="col-md-4 mb-4 d-flex align-items-stretch">
            <a href="{{ url_for('user.get_users', active_tab='blocked') }}"
                class="text-decoration-none text-dark w-100">
                <div class="card h-100 journey-slide-card hover-card quick-access-card">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                        <div class="icon-container mb-3">
                            <i class="bi bi-person-x-fill fs-1"></i>
                        </div>
                        <h5 class="card-title text-center mb-2">Blocked Users</h5>
                        <p class="card-text text-center text-muted mb-0">Users who cannot share public journeys</p>
                        <span class="btn btn-outline-dark rounded-pill px-4 mt-3">Review</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-4 d-flex align-items-stretch">
            <a href="{{ url_for('user.get_users', active_tab='banned') }}" class="text-decoration-none text-dark w-100">
                <div class="card h-100 journey-slide-card hover-card quick-access-card">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                        <div class="icon-container mb-3">
                            <i class="bi bi-slash-circle fs-1"></i>
                        </div>
                        <h5 class="card-title text-center mb-2">Banned Users</h5>
                        <p class="card-text text-center text-muted mb-0">Users who cannot access the system</p>
                        <span class="btn btn-outline-dark rounded-pill px-4 mt-3">View All</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-4 d-flex align-items-stretch">
            <a href="{{ url_for('journey.admin_hidden_journeys') }}" class="text-decoration-none text-dark w-100">
                <div class="card h-100 journey-slide-card hover-card quick-access-card">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                        <div class="icon-container mb-3">
                            <i class="bi bi-eye-slash fs-1"></i>
                        </div>
                        <h5 class="card-title text-center mb-2">Hidden Journeys</h5>
                        <p class="card-text text-center text-muted mb-0">Access and review journeys hidden from public
                            view</p>
                        <span class="btn btn-outline-dark rounded-pill px-4 mt-3">Manage</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-4 d-flex align-items-stretch">
            <a href="{{ url_for('location.get_locations') }}" class="text-decoration-none text-dark w-100">
                <div class="card h-100 journey-slide-card hover-card quick-access-card">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                        <div class="icon-container mb-3">
                            <i class="bi bi-geo-alt fs-1"></i>
                        </div>
                        <h5 class="card-title text-center mb-2">Location Management</h5>
                        <p class="card-text text-center text-muted mb-0">Edit, merge and organize location data</p>
                        <span class="btn btn-outline-dark rounded-pill px-4 mt-3">Organize</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-4 d-flex align-items-stretch">
            <a href="{{ url_for('announcement.get_all_announcements') }}" class="text-decoration-none text-dark w-100">
                <div class="card h-100 journey-slide-card hover-card quick-access-card">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                        <div class="icon-container mb-3">
                            <i class="bi bi-megaphone fs-1"></i>
                        </div>
                        <h5 class="card-title text-center mb-2">Announcements</h5>
                        <p class="card-text text-center text-muted mb-0">Create and manage system-wide notifications</p>
                        <span class="btn btn-outline-dark rounded-pill px-4 mt-3">Publish</span>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4 mb-4 d-flex align-items-stretch">
            <a href="{{ url_for('report.get_reports') }}" class="text-decoration-none text-dark w-100">
                <div class="card h-100 journey-slide-card hover-card quick-access-card">
                    <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                        <div class="icon-container mb-3">
                            <i class="bi bi-flag fs-1"></i>
                        </div>
                        <h5 class="card-title text-center mb-2">Reports</h5>
                        <p class="card-text text-center text-muted mb-0">Manage reported comments</p>
                        <span class="btn btn-outline-dark rounded-pill px-4 mt-3">View</span>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

</div>

<!-- Link to Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">

<style>
    .user-management-section {
        padding: 20px;
    }

    .badge-sm {
        font-size: 1rem;
        padding: 0.35em 0.6em;
        background: black;
        color: white;
        border-radius: 5rem;
    }

    .btn-outline-dark:hover {
        background-color: #1a1a1a;
        border-color: #1a1a1a;
    }

    .btn-dark {
        background-color: #1a1a1a;
        border-color: #1a1a1a;
    }

    .btn-primary {
        background-color: #1a1a1a;
        border-color: #1a1a1a;
    }

    .btn-primary:hover {
        background-color: #2c2c2c;
        border-color: #2c2c2c;
    }

    .badge {
        font-weight: 500;
    }

    .nav-tabs .nav-link {
        background-color: white;
        color: #6c757d;
        border: none;
        padding: 0.7rem 1.5rem;
        font-weight: 500;
        position: relative;
    }

    .nav-tabs .nav-link.active {
        background: white;
        border: none;
    }

    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 20%;
        width: 60%;
        height: 3px;
        background-color: #4e6bff;
        border-radius: 3px;
    }

    .carousel-control-btn {
        width: 40px;
        height: 40px;
        opacity: 0.8;
        transition: all 0.2s ease;
        background-color: rgba(26, 26, 26, 0.9);
        border: none;
        position: absolute;
        z-index: 10;
    }

    .carousel-control-btn:hover {
        opacity: 1;
        transform: translateY(-50%) scale(1.1);
        background-color: #1a1a1a;
    }

    .journey-carousel-container {
        position: relative;
        margin: 0 20px;
        max-width: 100%;
    }

    #journeyCarousel {
        width: 100%;
        gap: 0;
    }

    .carousel-indicators-custom {
        gap: 8px;
    }

    .carousel-indicators-custom .indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #dee2e6;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .carousel-indicators-custom .indicator.active {
        background-color: #1a1a1a;
        transform: scale(1.2);
    }

    .journey-slide-card {
        width: 100%;
        height: 350px;
        border-radius: 15px;
        background-color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative;
    }

    .journey-slide-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .journey-slide-image {
        height: 140px;
        width: 100%;
        overflow: hidden;
        position: relative;
        border-radius: 15px 15px 0 0;
        background-color: #f8f9fa;
    }

    .journey-slide-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
    }

    .journey-slide-content {
        padding: 16px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .journey-slide-date {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .journey-slide-title {
        font-weight: bold;
        font-size: 1rem;
        line-height: 1.3;
        margin-bottom: 8px;
        max-height: 2.6rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-clamp: 2;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .journey-slide-description {
        color: #6c757d;
        font-size: 0.8rem;
        line-height: 1.5;
        margin-bottom: 16px;
        flex-grow: 1;
        max-height: 3.6rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        line-clamp: 3;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .journey-slide-author {
        display: flex;
        align-items: center;
        margin-top: auto;
        padding-top: 10px;
    }

    .journey-slide-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
    }

    .journey-slide-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .journey-slide-author-info {
        margin-left: 10px;
    }

    .journey-slide-author-label {
        font-size: 0.7rem;
        color: #6c757d;
        margin-bottom: 0;
        line-height: 1;
    }

    .journey-slide-author-name {
        font-size: 0.8rem;
        font-weight: 500;
        margin-bottom: 0;
        line-height: 1.2;
    }

    .journey-slide-card {
        background: linear-gradient(135deg, #f7f9ff, #fafcff, #ffffff);
        border: 1px solid #f3f3f3;
    }

    .nav-tabs .nav-tab {
        background-color: white;
        color: #6c757d;
        border: none;
        padding: 0.7rem 1.5rem;
        font-weight: 500;
        position: relative;
        transition: color 0.3s ease;
    }

    .nav-tabs .nav-tab:hover {
        color: #4e6bff;
    }

    .nav-tabs .nav-tab.active {
        background-color: white;
        color: #4e6bff;
        border: none;
    }

    .nav-tabs .nav-tab.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 20%;
        width: 60%;
        height: 3px;
        background-color: #4e6bff;
        border-radius: 3px;
    }

    .nav-tabs .nav-tab:focus {
        outline: none;
        box-shadow: none;
    }

    .admin-card {
        padding-top: 32px;
        padding-bottom: 20px;
    }

    .announcement-card {
        background: linear-gradient(135deg, #f0f4ff, #e0eaff);
        border: 1px solid #dce3f0;
    }

    /* Quick Access Cards Styling */
    .quick-access-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        overflow: hidden;
    }

    .quick-access-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border-color: rgba(78, 107, 255, 0.3);
    }

    .quick-access-card .icon-container {
        height: 70px;
        width: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(78, 107, 255, 0.1);
        border-radius: 50%;
        color: #4e6bff;
        margin-bottom: 15px;
    }

    .quick-access-card:hover .icon-container {
        background-color: #4e6bff;
        color: white;
        transform: scale(1.1);
        transition: all 0.3s ease;
    }

    .quick-access-card .display-4 {
        font-size: 2.5rem;
        font-weight: 600;
        color: #4e6bff;
        transition: all 0.3s ease;
    }

    .quick-access-card:hover .display-4 {
        transform: scale(1.1);
    }

    .quick-access-card .card-title {
        font-weight: 600;
        margin-bottom: 10px;
    }

    .quick-access-card .card-text {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .quick-access-card .btn {
        transition: all 0.3s ease;
    }

    .quick-access-card:hover .btn {
        background-color: #4e6bff;
        color: white;
        border-color: #4e6bff;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Add event listeners for read more links
        document.querySelectorAll('.read-more-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                const title = link.dataset.title;
                const content = link.dataset.content;
                const date = link.dataset.date;
                const formId = link.dataset.formId;

                const modalContent = `
                    <div class="announcement-detail">
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-calendar3 me-2"></i>
                            <small class="text-muted">${date}</small>
                        </div>
                        <p class="mb-3">${content}</p>
                    </div>
                `;

                showModal(title, modalContent, {
                    actionText: 'Mark as Read',
                    onAction: () => {
                        document.getElementById(formId).submit();
                    }
                });
            });
        });

        // Only initialize carousel if it exists
        const carousel = document.getElementById('journeyCarousel');
        if (carousel) {
            const slides = document.querySelectorAll('.journey-slide');
            const totalSlides = slides.length;
            const slidesPerView = 4;
            const totalPages = Math.ceil(totalSlides / slidesPerView);
            let currentPage = 0;

            if (totalSlides > slidesPerView) {
                document.getElementById('nextJourney').addEventListener('click', () => {
                    currentPage = (currentPage + 1) % totalPages;
                    updateCarousel();
                });

                document.getElementById('prevJourney').addEventListener('click', () => {
                    currentPage = (currentPage - 1 + totalPages) % totalPages;
                    updateCarousel();
                });

                document.querySelectorAll('.indicator').forEach(indicator => {
                    indicator.addEventListener('click', () => {
                        currentPage = parseInt(indicator.dataset.index);
                        updateCarousel();
                    });
                });

                let autoScroll = setInterval(() => {
                    document.getElementById('nextJourney').click();
                }, 5000);

                document.querySelector('.journey-carousel-container').addEventListener('mouseenter', () => {
                    clearInterval(autoScroll);
                });

                document.querySelector('.journey-carousel-container').addEventListener('mouseleave', () => {
                    autoScroll = setInterval(() => {
                        document.getElementById('nextJourney').click();
                    }, 5000);
                });
            } else {
                const prevButton = document.getElementById('prevJourney');
                const nextButton = document.getElementById('nextJourney');
                if (prevButton) prevButton.style.display = 'none';
                if (nextButton) nextButton.style.display = 'none';
            }

            function updateCarousel() {
                const translateValue = -currentPage * 100;
                carousel.style.transform = `translateX(${translateValue}%)`;

                document.querySelectorAll('.indicator').forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === currentPage);
                });
            }
        }
    });
</script>
{% endblock %}