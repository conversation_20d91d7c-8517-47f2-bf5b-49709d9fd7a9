from flask import Blueprint, render_template, request, redirect, url_for, flash, session, g
from services import account_service, departure_board_service, user_service, subscription_service, community_service, journey_service, event_service
from utils.security import login_required
from data import privacy_settings_data, location_data
from flask import current_app

bp = Blueprint('account', __name__, url_prefix='/account')


@bp.route('/')
@bp.route('/profile')
@login_required
def get_profile():
    active_tab = request.args.get('active_tab', 'profile')
    user_profile = g.current_user.copy()
    context = {'active_tab': active_tab, 'account': user_profile}
    role = session.get('role')
    context['user_role'] = role

    # Add premium_access to context for template access
    premium_access = subscription_service.check_can_use_premium_features(user_id=session['user_id'])
    context['premium_access'] = premium_access

    if active_tab == 'profile':
        privacy_settings = privacy_settings_data.get_user_privacy_settings(user_profile['id'])
        default_settings = {
            'first_name': 'public', 'last_name': 'public', 'email': 'public', 'username': 'public',
            'location': 'public', 'description': 'public', 'interests': 'public',
            'recent_likes': 'public', 'recent_comments': 'public', 'public_journeys': 'public', 'visited_places': 'public'
        }
        for field, default_visibility in default_settings.items():
            if field not in privacy_settings:
                privacy_settings[field] = default_visibility
        for attr, visibility in privacy_settings.items():
            user_profile[f"show_{attr}"] = (visibility == 'public')
        recent_likes = community_service.get_user_liked_events(user_profile['id'], limit=5)
        user_profile['recent_likes'] = recent_likes
        recent_comments = community_service.get_user_comments(user_profile['id'], limit=5)
        user_profile['recent_comments'] = recent_comments
        user_profile['public_journeys'] = journey_service.get_public_journeys(user_id=user_profile['id'], limit=10, offset=0)
        user_profile['visited_places'] = location_data.get_user_visited_locations(user_profile['id'], limit=10)
        user_id = user_profile['id']
        page = request.args.get('page', 1, type=int)
        per_page = 5
        subscriptions, total = subscription_service.get_user_subscription_history(user_id, per_page, (page - 1) * per_page)
        total_pages = (total + per_page - 1) // per_page
        active_subscription = subscription_service.get_user_active_subscription(user_id)
        payments = subscription_service.get_user_payments(user_id)
        latest_payment = payments[0] if payments else None
        # Patch period_months if missing/zero using subscription's own months field
        for sub in subscriptions:
            period = sub.get('period_months')
            if not period or period == 0:
                # Use the months value from the subscriptions table
                if sub.get('months'):
                    sub['period_months'] = sub['months']
        context.update({
            'active_subscription': active_subscription,
            'all_subscriptions': subscriptions,
            'all_payments': payments,
            'latest_payment': latest_payment,
            'page': page,
            'total_pages': total_pages
        })
    elif active_tab == 'activities':
        user_profile['public_journeys'] = journey_service.get_public_journeys(user_id=user_profile['id'], limit=10, offset=0)
        user_profile['visited_places'] = location_data.get_user_visited_locations(user_profile['id'], limit=10)
        user_profile['recent_likes'] = community_service.get_user_liked_events(user_profile['id'], limit=5)
        user_profile['recent_comments'] = community_service.get_user_comments(user_profile['id'], limit=5)
        context['activities'] = community_service.get_user_activities(user_profile['id']) if hasattr(community_service, 'get_user_activities') else []
    elif active_tab == 'subscription':
        user_id = user_profile['id']
        page = request.args.get('page', 1, type=int)
        per_page = 5
        subscriptions, total = subscription_service.get_user_subscription_history(user_id, per_page, (page - 1) * per_page)
        total_pages = (total + per_page - 1) // per_page
        active_subscription = subscription_service.get_user_active_subscription(user_id)
        has_had_free_trial = subscription_service.check_user_had_free_trial(user_id)
        has_had_premium = subscription_service.check_user_had_premium(user_id)
        payments = subscription_service.get_user_payments(user_id)
        latest_payment = payments[0] if payments else None
        # Patch period_months if missing/zero using subscription's own months field
        for sub in subscriptions:
            period = sub.get('period_months')
            if not period or period == 0:
                # Use the months value from the subscriptions table
                if sub.get('months'):
                    sub['period_months'] = sub['months']
        context.update({
            'active_subscription': active_subscription,
            'all_subscriptions': subscriptions,
            'all_payments': payments,
            'latest_payment': latest_payment,
            'page': page,
            'total_pages': total_pages,
            'has_had_free_trial': has_had_free_trial,
            'has_had_premium': has_had_premium
        })
        context['subscription'] = subscription_service.get_user_subscription(user_profile['id']) if hasattr(subscription_service, 'get_user_subscription') else None
    elif active_tab == 'map':
        success, message, user_profile['events'] = event_service.get_user_private_events(user_id=user_profile['id'])
    return render_template('account/profile/main.html', **context)

@bp.route('/update', methods=['GET','POST'])
@login_required
def update_profile():
    """Update current user's profile"""
    if request.method=='POST':
        # Get form data
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        location = request.form.get('location')
        description = request.form.get('description')
        interests = request.form.get('interests')

        # Handle is_public (if checkbox not present, will be None = False)
        is_public = request.form.get('is_public') == 'true'

        # Log all form data for debugging
        current_app.logger.debug(f"Raw form data: {dict(request.form)}")
        current_app.logger.debug(f"is_public form value: {request.form.get('is_public')}")
        current_app.logger.debug(f"is_public parsed value: {is_public}")

        # Get field visibility settings
        visibility_settings = {}
        field_mapping = {
            'first_name': 'show_first_name',
            'last_name': 'show_last_name',
            'email': 'show_email',
            'username': 'show_username',
            'location': 'show_location',
            'description': 'show_description',
            'interests': 'show_interests',
            'recent_likes': 'show_recent_likes',
            'recent_comments': 'show_recent_comments',
            'public_journeys': 'show_public_journeys',
            'visited_places': 'show_visited_places'
        }

        # Extended debug log to show all form field values
        for field_name in field_mapping.values():
            current_app.logger.debug(f"Form field '{field_name}' value: '{request.form.get(field_name)}'")

        # Process each visibility field
        for privacy_field, form_field in field_mapping.items():
            # Form value will be 'true' for checked boxes or None/something else for unchecked
            form_value = request.form.get(form_field)
            current_app.logger.debug(f"Raw form value for {form_field}: '{form_value}'")

            # Only consider 'true' as true, everything else is false
            is_visible = form_value == 'true'

            visibility_settings[privacy_field] = 'public' if is_visible else 'private'
            current_app.logger.debug(f"Setting {privacy_field} visibility to {is_visible} (public: {'public' if is_visible else 'private'})")

            # Log the final value for clarity
            current_app.logger.info(f"Final visibility for {privacy_field}: {'public' if is_visible else 'private'}")

        # Create kwargs dict for profile service
        kwargs = {}
        for privacy_field, form_field in field_mapping.items():
            # Set show_X to True/False based on visibility setting
            kwargs[form_field] = visibility_settings[privacy_field] == 'public'

        # Debug log parameters being passed to update function
        current_app.logger.debug(f"Updating profile with parameters: email={email}, first_name={first_name}, " +
                               f"last_name={last_name}, location={location}, interests={interests}, " +
                               f"is_public={is_public}, visibility_settings={visibility_settings}")

        # Update profile
        success, message = account_service.update_user_profile(
            user_id=session['user_id'],
            email=email,
            first_name=first_name,
            last_name=last_name,
            location=location,
            description=description,
            interests=interests,
            is_public=is_public,
            **kwargs
        )

        current_app.logger.debug(f"Profile update result: success={success}, message={message}")

        if success:
            # Update user session data if is_public setting changed
            g.current_user['is_public'] = is_public
            # Update basic field data in session
            if email: g.current_user['email'] = email
            if first_name: g.current_user['first_name'] = first_name
            if last_name: g.current_user['last_name'] = last_name
            if location: g.current_user['location'] = location
            if description: g.current_user['description'] = description
            if interests: g.current_user['interests'] = interests

            # After updating, verify the settings in the database match what was set
            actual_settings = privacy_settings_data.get_user_privacy_settings(session['user_id'])
            current_app.logger.debug(f"Actual settings after update: {actual_settings}")

            # Check for any discrepancies and fix them if needed
            fixed_settings = []
            for field, expected_visibility in visibility_settings.items():
                actual = actual_settings.get(field)
                if actual != expected_visibility:
                    current_app.logger.warning(f"Visibility mismatch after update for {field}: expected={expected_visibility}, actual={actual}")
                    # Fix incorrect settings by directly setting them in the database
                    try:
                        # First reset this attribute
                        privacy_settings_data.reset_attribute_visibility(session['user_id'], field)
                        # Then set it to the correct value
                        result = privacy_settings_data.set_attribute_visibility(session['user_id'], field, expected_visibility)
                        if result > 0:
                            fixed_settings.append(field)
                    except Exception as e:
                        current_app.logger.error(f"Error fixing visibility for {field}: {str(e)}")

            if fixed_settings:
                current_app.logger.info(f"Fixed visibility settings for: {', '.join(fixed_settings)}")

            # Update visibility settings in session
            for field, visibility in visibility_settings.items():
                g.current_user[f"show_{field}"] = (visibility == 'public')

            flash(message, 'success')
            return redirect(url_for('account.get_profile', active_tab='profile'))
        else:
            flash(message, 'danger')

    return render_template('account/profile/edit.html', account=g.current_user)


@bp.route('/profile-image', methods=['GET','POST'])
@login_required
def update_profile_image():
    """Update profile image"""
    if 'profile_image' not in request.files:
        flash('No file selected', 'danger')
        return redirect(url_for('account.update_profile'))

    file = request.files['profile_image']

    if file.filename == '':
        flash('No file selected', 'danger')
        return redirect(url_for('account.update_profile'))

    success, message = account_service.update_user_profile_image(
        user_id=session['user_id'],
        file=file
    )

    if success:
        # Update session with new profile image
        user = account_service.get_user_profile(session['user_id'])
        session['profile_image'] = user['profile_image']

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('account.get_profile', active_tab='profile'))


@bp.route('/profile-image/delete', methods=['GET'])
@login_required
def delete_profile_image():
    """Delete profile image"""
    success, message = account_service.remove_user_profile_image(
        user_id=session['user_id']
    )

    if success:
        # Update session to remove profile image
        session['profile_image'] = None

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('account.get_profile', active_tab='profile'))


@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        success, message = account_service.change_user_password(
            user_id=session['user_id'],
            current_password=current_password,
            new_password=new_password,
            confirm_password=confirm_password
        )

        if success:
            flash(message, 'success')
            return redirect(url_for('account.get_profile', active_tab='password'))
        else:
            flash(message, 'danger')

    return redirect(url_for('account.get_profile', active_tab='password'))


@bp.route('/u/<username>', methods=['GET'])
@login_required
def get_public_profile(username):
    """View another user's profile with tab logic"""
    user = account_service.get_user_by_username(username)
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('main.get_user_dashboard'))

    from utils.permissions import PermissionChecker
    if not user['is_public'] and not PermissionChecker.is_staff():
        flash('This user profile is private', 'warning')
        return redirect(url_for('main.get_user_dashboard'))

    privacy_settings = privacy_settings_data.get_user_privacy_settings(user['id'])
    default_settings = {
        'first_name': 'public', 'last_name': 'public', 'email': 'public', 'username': 'public',
        'location': 'public', 'description': 'public', 'interests': 'public',
        'recent_likes': 'public', 'recent_comments': 'public', 'public_journeys': 'public', 'visited_places': 'public'
    }
    for field, default_visibility in default_settings.items():
        if field not in privacy_settings:
            privacy_settings[field] = default_visibility
    for attr, visibility in privacy_settings.items():
        user[f"show_{attr}"] = (visibility == 'public')

    premium_access = subscription_service.check_can_use_premium_features(user_id=session['user_id'])
    is_following_user, message = departure_board_service.check_following_user(follower_id=session['user_id'], followed_id=user['id'])
    can_message = community_service.check_can_message(session['user_id'], user['id'])

    active_tab = request.args.get('active_tab', 'profile')
    activities_tab = request.args.get('activities_tab', 'journeys')
    context = {
        'user': user,
        'premium_access': premium_access,
        'can_message': can_message,
        'is_following_user': is_following_user,
        'active_tab': active_tab,
        'activities_tab': activities_tab,
    }

    if active_tab == 'profile':
        pass
    elif active_tab == 'activities':
        if premium_access:
            if activities_tab == 'journeys' and user['show_public_journeys']:
                user['public_journeys'] = journey_service.get_public_journeys(user_id=user['id'], limit=10, offset=0)
                success, message, user['events'] = event_service.get_user_public_events(user['id'])
            if activities_tab == 'places' and user['show_visited_places']:
                user['visited_places'] = location_data.get_user_visited_locations(user['id'], limit=10)
            if activities_tab == 'likes' and user['show_recent_likes']:
                user['recent_likes'] = community_service.get_user_liked_events(user['id'], limit=5)
            if activities_tab == 'comments' and user['show_recent_comments']:
                user['recent_comments'] = community_service.get_user_comments(user['id'], limit=5)
    return render_template(
        'discovery/user/detail.html',
        **context
    )


@bp.route('/find', methods=['GET'])
@login_required
def find_users():
    """Find users to message or follow"""
    search_term = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    limit = 10
    offset = (page - 1) * limit

    users = []
    total_count = 0

    if search_term:
        # Only show public profiles
        users = user_service.search_users(
            search_term,
            limit=limit,
            offset=offset,
            public_only=True
        )
        total_count = user_service.count_search_results(
            search_term,
            public_only=True
        )

    total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1

    # Get premium access status
    premium_access = subscription_service.check_can_use_premium_features(
        user_id=session['user_id']
    )

    return render_template(
        'account/find_users.html',
        users=users,
        search_term=search_term,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        premium_access=premium_access
    )


@bp.route('/fix-privacy-settings', methods=['GET'])
@login_required
def fix_privacy_settings():
    """Reset and fix privacy settings for the current user"""
    try:
        current_app.logger.info(f"Resetting privacy settings for user ID: {session['user_id']}")

        # First delete any old settings
        rows_deleted = privacy_settings_data.reset_privacy_settings(session['user_id'])
        current_app.logger.debug(f"Deleted {rows_deleted} existing privacy settings")

        # Apply default settings
        default_settings = privacy_settings_data.set_default_privacy_settings(session['user_id'])
        current_app.logger.debug(f"Applied default settings: {default_settings}")

        # Verify settings were applied correctly
        actual_settings = privacy_settings_data.get_user_privacy_settings(session['user_id'])
        current_app.logger.debug(f"Actual settings after reset: {actual_settings}")

        # Compare settings
        for field, value in default_settings.items():
            if field not in actual_settings or actual_settings[field] != value:
                current_app.logger.warning(f"Setting mismatch for {field}: expected={value}, actual={actual_settings.get(field, 'missing')}")

        # Update session data
        for field in default_settings:
            g.current_user[f"show_{field}"] = default_settings[field] == 'public'

        flash("Your privacy settings have been reset to defaults.", "success")
    except Exception as e:
        current_app.logger.error(f"Error fixing privacy settings: {str(e)}", exc_info=True)
        flash("Failed to reset privacy settings. Please try again.", "danger")

    return redirect(url_for('account.get_profile', active_tab='profile'))


@bp.route('/verify-privacy-settings', methods=['GET'])
@login_required
def verify_privacy_settings():
    """Verify and fix privacy settings for the current user without resetting them"""
    try:
        current_app.logger.info(f"Verifying privacy settings for user ID: {session['user_id']}")

        # Get current settings from session and database
        session_settings = {}
        for key in g.current_user:
            if key.startswith('show_'):
                field_name = key[5:]  # Remove 'show_' prefix
                session_settings[field_name] = 'public' if g.current_user[key] else 'private'

        # Get settings from database
        db_settings = privacy_settings_data.get_user_privacy_settings(session['user_id'])

        # Log current settings
        current_app.logger.debug(f"Session settings: {session_settings}")
        current_app.logger.debug(f"Database settings: {db_settings}")

        # Check for discrepancies and fix them
        fixed_settings = []
        for field, expected in session_settings.items():
            actual = db_settings.get(field)
            if actual != expected:
                current_app.logger.warning(f"Visibility mismatch for {field}: expected={expected}, actual={actual}")
                try:
                    # Clear existing setting first
                    privacy_settings_data.reset_attribute_visibility(session['user_id'], field)
                    # Set to expected value
                    result = privacy_settings_data.set_attribute_visibility(session['user_id'], field, expected)
                    if result > 0:
                        fixed_settings.append(field)
                except Exception as e:
                    current_app.logger.error(f"Error fixing visibility for {field}: {str(e)}")

        if fixed_settings:
            current_app.logger.info(f"Fixed visibility settings for: {', '.join(fixed_settings)}")
            flash(f"Fixed visibility settings for: {', '.join(fixed_settings)}", "success")
        else:
            flash("All privacy settings are correct.", "success")

    except Exception as e:
        current_app.logger.error(f"Error verifying privacy settings: {str(e)}", exc_info=True)
        flash("Error verifying privacy settings. Please try again.", "danger")

    return redirect(url_for('account.get_profile', active_tab='profile'))
