{% extends "base.html" %}

{% block title %}{{ event.title }} - Footprints{% endblock %}

{% block content %}
<div class="event-page">
  <!-- Back Navigation -->
  <a href="javascript:void(0)" onclick="smartBack()" class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
    <i class="bi bi-arrow-left me-2"></i>
    <span id="backButtonText">Back</span>
  </a>

  {% include 'components/modal.html' %}

  <!-- Main Content -->
  <div class="container-fluid">
    <div class="event-content"
         data-event-id="{{ event.id }}"
         data-premium-access="{% if premium_access %}true{% else %}false{% endif %}"
         data-upload-url="{{ url_for('event.upload_event_images', event_id=event.id) }}"
         data-plans-url="{{ url_for('subscription.get_plans') }}"
         data-is-logged-in="{{ 'true' if g.current_user else 'false' }}"
         data-journey-visibility="{{ journey.visibility }}"
         data-journey-id="{{ journey.id }}"
         data-journey-page="{{ session.get('journey_page', 'public') }}"
         data-published-journey-url="{{ url_for('journey.get_published_journey_detail', journey_id=journey.id) }}"
         data-public-journey-url="{{ url_for('journey.get_public_journey', journey_id=journey.id) }}"
         data-private-journey-url="{{ url_for('journey.get_private_journey', journey_id=journey.id) }}"
         data-published-list-url="{{ url_for('main.get_published_journey') }}"
         data-landing-url="{{ url_for('main.get_landing_page') }}"
         data-private-journeys-url="{{ url_for('journey.get_private_journeys') }}"
         data-public-journeys-url="{{ url_for('journey.get_public_journeys') }}">

    <!-- Main Layout with Sidebar -->
    <div class="page-layout">
      <!-- Main Content Area -->
      <div class="main-content">
        <!-- Comprehensive Event Card -->
        <div class="comprehensive-event-card">
          <!-- Combined Title and Actions Header -->
          <div class="title-actions-header">
            <div class="title-content">
              <!-- Journey Breadcrumb -->
              <div class="journey-breadcrumb">
                <i class="bi bi-journal-bookmark"></i>
                <span>{{ journey.title }}</span>
              </div>
              <!-- Event Title -->
              <h1 class="event-title">{{ event.title }}</h1>
            </div>

            <div class="header-actions">
              <!-- Status Badges -->
              <div class="status-badges">
                {% if journey.visibility == 'private' %}
                <span class="status-badge private">
                  <i class="bi bi-lock-fill"></i>Private
                </span>
                {% endif %}
                {% if journey.is_hidden %}
                <span class="status-badge hidden">
                  <i class="bi bi-eye-slash"></i>Hidden
                </span>
                {% endif %}
                {% if journey.no_edits and (journey.user_id == session.get('user_id') or can_manage_content()) %}
                <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1" title="This journey is protected from staff edits">
                <i class="bi bi-shield-lock me-1"></i>Protected
                </span>
                {% endif %}
              </div>

              <!-- Like Button -->
              {% if journey.visibility != 'private' %}
              {% if session.get('user_id') %}
              <form method="post" action="{{ url_for('event.like_event', event_id=event.id) }}" class="like-form">
                <input type="hidden" name="back" class="back-param-field" value="">
                <button type="submit" class="like-btn">
                  <i class="bi bi-heart{% if user_liked_event %}-fill{% endif %}"></i>
                  <span>{{ event_likes|default(0) }}</span>
                </button>
              </form>
              {% else %}
              <!-- Non-logged-in users see like count but cannot interact -->
              <div class="like-btn disabled">
                <i class="bi bi-heart"></i>
                <span>{{ event_likes|default(0) }}</span>
              </div>
              {% endif %}
              {% endif %}

              <!-- Menu -->
              {% if session.get('user_id') and (journey.user_id == session.get('user_id') or can_manage_content()) %}
              <div class="dropdown">
                <button class="menu-btn" type="button" data-bs-toggle="dropdown">
                  <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li>
                    {% if journey.no_edits and journey.user_id != session.get('user_id') %}
                    <a href="#" class="dropdown-item disabled" onclick="showProtectedEventMessage()">
                      <i class="bi bi-shield-lock"></i>Edit Event (Protected)
                    </a>
                    {% else %}
                    <button class="dropdown-item editEventBtn" data-event-id="{{ event.id }}">
                      <i class="bi bi-pencil"></i>Edit Event
                    </button>
                    {% endif %}
                  </li>
                  {% if can_access_edit_history() or journey.user_id == session.get('user_id') %}
                  <li>
                    <a href="{{ url_for('edit_history.get_event_edit_history', event_id=event.id) }}" class="dropdown-item">
                      <i class="bi bi-clock-history"></i>View Edit History
                    </a>
                  </li>
                  {% endif %}
                  {% if journey.user_id == session.get('user_id') %}
                  <li><hr class="dropdown-divider"></li>
                  <li>
                    <form method="post" action="{{ url_for('event.delete_event', event_id=event.id) }}" id="deleteEventForm_{{ event.id }}">
                      <input type="hidden" name="back" class="back-param-field" value="">
                      <button type="button" class="dropdown-item text-danger" onclick="confirmDeleteEvent('{{ event.id }}')">
                        <i class="bi bi-trash"></i>Delete Event
                      </button>
                    </form>
                  </li>
                  {% endif %}
                </ul>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Content Layout Section -->
          <div class="card-content-layout">
            <!-- Left Column: Meta + Content -->
            <div class="left-column">
              <!-- Author and Update Time Section -->
              <div class="content-section meta-section">
                <div class="event-meta">
                  <div class="author-info">
                    <div class="author-avatar">
                      <img src="{{ url_for('static', filename=get_safe_image_url(journey.profile_image, 'profile')) }}"
                           alt="{{ journey.username }}">
                    </div>
                    <div class="author-details">
                      <span class="author-name">{{ journey.username }}</span>
                    </div>
                  </div>
                  <div class="update-time">
                    <i class="bi bi-clock-history"></i>
                    <span>Updated {{ journey.updated_at | timeago }}</span>
                  </div>
                </div>
              </div>

              <!-- Description Section -->
              <div class="content-section description-section">
                <div class="section-header">
                  <div class="section-icon">
                    <i class="bi bi-card-text"></i>
                  </div>
                  <h3>Description</h3>
                </div>
                <div class="section-content">
                  <p id="eventDescription">{{ event.description }}</p>
                </div>
              </div>

              <!-- DateTime Section -->
              <div class="content-section datetime-section">
                <div class="section-header">
                  <div class="section-icon">
                    <i class="bi bi-clock"></i>
                  </div>
                  <h3>Date & Time</h3>
                </div>
                <div class="section-content">
                  <div class="datetime-item">
                    <span class="modern-label">
                      <i class="bi bi-calendar-plus"></i>
                      Starts
                    </span>
                    <span class="value" id="eventStartDate">{{ event.start_datetime | datetime }}</span>
                  </div>
                  {% if event.end_datetime %}
                  <div class="datetime-item">
                    <span class="modern-label">
                      <i class="bi bi-calendar-check"></i>
                      Ends
                    </span>
                    <span class="value" id="eventEndDate">{{ event.end_datetime | datetime }}</span>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Right Column: Location + Images -->
            <div class="right-column">
              <!-- Location Section -->
              <div class="content-section location-section">
                <div class="section-header">
                  <div class="section-icon">
                    <i class="bi bi-geo-alt"></i>
                  </div>
                  <h3>Location</h3>
                </div>
                <div class="section-content">
                  <p class="location-name">{{ event.location_name }}</p>
                  {% if event.location_latitude and event.location_longitude %}
                  <div class="map-container">
                    <div id="map" class="event-map"
                         data-lat="{{ event.location_latitude }}"
                         data-lng="{{ event.location_longitude }}"
                         data-location-name="{{ event.location_name }}"></div>
                  </div>
                  {% endif %}
                </div>
              </div>

              <!-- Images Section -->
              <div class="content-section images-section">
                <div class="section-header">
                  <div class="section-icon">
                    <i class="bi bi-images"></i>
                  </div>
                  <h3>Images</h3>
                </div>
                <div class="section-content">
                  {% if images %}
                  <div class="image-gallery">
                    <div class="main-image">
                      <img src="{{ url_for('static', filename=get_safe_image_url(images[0].image_filename, 'event')) }}"
                           alt="Event image" class="event-image-button" event-id="{{ event.id }}">
                      {% if images|length > 1 %}
                      <div class="image-count">+{{ images|length - 1 }}</div>
                      {% endif %}
                      {% if session.get('user_id') and journey.user_id == session.user_id %}
                      <button type="button" class="manage-btn-inside" id="manageImagesBtn">
                        <i class="bi bi-camera"></i>Manage Images
                      </button>
                      {% elif session.get('user_id') and can_manage_content() %}
                      <button type="button" class="manage-btn-inside staff-manage-btn" id="staffManageImagesBtn">
                        <i class="bi bi-shield-check"></i>Staff Manage
                      </button>
                      {% endif %}
                    </div>
                  </div>
                  {% else %}
                  <div class="empty-images bg-light rounded p-3 text-center" id="noImagesSection">
                    <i class="bi bi-images fs-2 text-secondary"></i>
                    <p class="text-muted mt-2 mb-2 small">No images added yet</p>
                    {% if session.get('user_id') and journey.user_id == session.user_id %}
                    <button type="button" class="btn btn-primary btn-sm" id="addFirstImageBtn">
                      <i class="bi bi-plus-lg me-1"></i>Add Image
                    </button>
                    {% endif %}
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Sidebar -->
      {% if comments|length > 0 or journey.visibility in ['public', 'published'] %}
      <div class="comments-sidebar">
        <div class="comments-section">
          <div class="comments-header">
            <h2><i class="bi bi-chat-dots"></i>Comments</h2>
            <span class="comment-count">{{ comments|length }}</span>
          </div>

          <div class="comments-list">
            {% if comments|length > 0 %}
            {% for comment in comments %}
            <div class="comment-item">
              <div class="comment-avatar">
                <img src="{{ url_for('static', filename=get_safe_image_url(comment.profile_image, 'profile')) }}"
                     alt="{{ comment.username }}">
              </div>
              <div class="comment-content">
                <div class="comment-header">
                  <span class="comment-author">{{ comment.username }}</span>
                  <span class="comment-time">{{ comment.created_at | timeago }}</span>
                </div>
                {% if not comment.is_hidden or comment.user_id == session.user_id or can_manage_content() %}
                <div class="comment-text">
                  {{ comment.content }}
                  {% if comment.is_hidden %}
                  <div class="hidden-notice">This comment is hidden from public view.</div>
                  {% endif %}
                </div>
                <div class="comment-actions">
                  {% if session.get('user_id') %}
                  <form action="{{ url_for('event.interact_with_event_comment', event_id=event.id, comment_id=comment['id']) }}" method="post">
                    <input type="hidden" name="action" value="like">
                    <input type="hidden" name="back" class="back-param-field" value="">
                    <button type="submit" class="action-btn like-btn">
                      <i class="bi bi-hand-thumbs-up{% if comment.user_has_liked[0] %}-fill{% endif %}"></i>
                      <span>{{ comment.like_count|default(0) }}</span>
                    </button>
                  </form>
                  <form action="{{ url_for('event.interact_with_event_comment', event_id=event.id, comment_id=comment['id']) }}" method="post">
                    <input type="hidden" name="action" value="dislike">
                    <input type="hidden" name="back" class="back-param-field" value="">
                    <button type="submit" class="action-btn dislike-btn">
                      <i class="bi bi-hand-thumbs-down{% if comment.user_has_disliked[0] %}-fill{% endif %}"></i>
                      <span>{{ comment.dislike_count|default(0) }}</span>
                    </button>
                  </form>
                  {% else %}
                  <!-- Non-logged-in users see counts but cannot interact -->
                  <div class="action-btn disabled">
                    <i class="bi bi-hand-thumbs-up"></i>
                    <span>{{ comment.like_count|default(0) }}</span>
                  </div>
                  <div class="action-btn disabled">
                    <i class="bi bi-hand-thumbs-down"></i>
                    <span>{{ comment.dislike_count|default(0) }}</span>
                  </div>
                  {% endif %}
                  {% if session.get('user_id') %}
                    {% if comment.user_id == session.user_id %}
                    <form method="post" action="{{ url_for('event.delete_event_comment', event_id=event.id, comment_id=comment.id) }}">
                      <input type="hidden" name="back" class="back-param-field" value="">
                      <button type="button" class="action-btn delete-btn" onclick="confirmDeleteComment('{{ comment.id}},{{ event.id }}')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                    {% else %}
                    {% if comment.user_has_reported %}
                    <div class="action-btn reported">
                      <i class="bi bi-flag-fill"></i>
                    </div>
                    {% else %}
                    <button type="button" class="action-btn report-btn report-comment-button" data-comment-id="{{ comment.id }}">
                      <i class="bi bi-flag"></i>
                    </button>
                    {% endif %}
                    {% endif %}
                  {% endif %}
                </div>
                {% else %}
                <div class="comment-text hidden">
                  This comment has been hidden by a staff member.
                </div>
                {% endif %}
              </div>
            </div>
            {% endfor %}
            {% else %}
            <!-- No Comments State -->
            <div class="no-comments-state">
              <div class="no-comments-icon">
                <i class="bi bi-chat-dots"></i>
              </div>
              <div class="no-comments-content">
                <h4>No comments yet</h4>
                <p>Be the first to share your thoughts about this event!</p>
              </div>
            </div>
            {% endif %}
          </div>

          {% if journey.visibility in ['public', 'published'] and session.get('user_id') %}
          <div class="comment-form-container">
            <form action="{{ url_for('event.add_event_comment', event_id=event.id) }}" method="post" class="comment-form">
              <input type="hidden" name="back" class="back-param-field" value="">
              <input type="text" name="content" class="comment-input" placeholder="Add a comment...">
              <button type="submit" class="submit-btn">
                <i class="bi bi-send"></i>
              </button>
            </form>
          </div>
          {% elif journey.visibility in ['public', 'published'] and not session.get('user_id') %}
          <div class="comment-form-container">
            <div class="login-prompt text-center p-3">
              <p class="text-muted mb-2">Please log in to add a comment</p>
              <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-sm">Log In</a>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
      {% endif %}
    </div>

    </div>
  </div>
  </div>
</div>
{% endblock %}

{% block head %}
<style>
/* Modern Event Page Design */
.event-page {
  max-width: 100%;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}


.back-button:hover {
  color: #007bff !important;
}

/* Main Content */
.event-content {
  max-width: 1400px;
}

/* Page Layout with Sidebar */
.page-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* Main Content Area */
.main-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Top Section: Header + DateTime */
.top-section {
  display: flex;
  gap: 24px;
  align-items: stretch;
}

/* Enhanced Event Header */
.event-header {
  background: white;
  border-radius: 16px;
  padding: 24px 24px 20px 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f1f3f4;
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* DateTime Section */
.datetime-section {
  flex-shrink: 0;
  width: 250px;
}

/* Enhanced DateTime Card */
.datetime-card {
  background: white;
  border: none;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.datetime-card .card-header {
  border-bottom: 1px solid #f1f3f4;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.datetime-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
}



.datetime-card h3 {
  color: #212529;
  font-weight: 600;
}

.datetime-card .datetime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.datetime-card .datetime-item:last-child {
  border-bottom: none;
}

/* Modern Labels - aligned with edit modal styling */
.modern-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-label i {
  color: #667eea;
  font-size: 14px;
}

/* Modern labels in modal contexts */
.modal .modern-label {
  margin-bottom: 8px;
  font-size: 14px;
  text-transform: none;
  letter-spacing: normal;
}

.modal .modern-label i {
  font-size: 16px;
}

.datetime-card .datetime-item .label {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.datetime-card .datetime-item .value {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

/* Compact DateTime Card */
.datetime-card.compact {
  height: fit-content;
}

.datetime-card.compact .card-content {
  padding: 0;
}

.datetime-card.compact .datetime-item {
  padding: 6px 0;
}

.datetime-card.compact .datetime-item .label {
  font-size: 11px;
}

.datetime-card.compact .datetime-item .value {
  font-size: 13px;
}

/* Comments Sidebar */
.comments-sidebar {
  width: 380px;
  flex-shrink: 0;
}

/* Enhanced Comments Section */
.comments-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f1f3f4;
  position: sticky;
  top: 20px;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.title-section {
  flex: 1;
  min-width: 0;
}

/* Status Badges */
.status-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-right: 12px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.status-badge.private {
  background: #fff3cd;
  color: #856404;
}

.status-badge.hidden {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.protected {
  background: #d1ecf1;
  color: #0c5460;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.like-form {
  margin: 0;
}

.like-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  color: #495057;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
}

.like-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.like-btn i.bi-heart-fill {
  color: #dc3545;
}

.like-btn.disabled {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.like-btn.disabled:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: none;
}

.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 50%;
  color: #495057;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Journey Breadcrumb */
.journey-breadcrumb {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.journey-breadcrumb i {
  color: #007bff;
  font-size: 11px;
}

/* Event Title */
.event-title {
  font-size: 24px;
  font-weight: 700;
  color: #212529;
  margin: 0;
  line-height: 1.3;
}

/* Event Meta */
.event-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
  margin-top: 0;
  margin-bottom: 0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e9ecef;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.author-name {
  font-weight: 600;
  color: #212529;
  font-size: 13px;
}

.event-date {
  color: #6c757d;
  font-size: 11px;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;
  font-size: 11px;
}

/* Main Layout - Two Panel Design */
.main-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  align-items: start;
}

/* Event Details Section */
.event-details-section {
  min-width: 0; /* Prevents grid overflow */
}

/* Comprehensive Event Card */
.comprehensive-event-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f1f3f4;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

/* Combined Title and Actions Header */
.title-actions-header {
  padding: 24px 24px 20px 24px;
  border-bottom: 1px solid #f1f3f4;
  background: white;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.title-actions-header .title-content {
  flex: 1;
  min-width: 0;
}

.title-actions-header .header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

/* Meta Section */
.meta-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
  flex-shrink: 0;
}

/* Header Section in Left Column */
.header-section {
  flex-shrink: 0;
}

.header-section .section-header {
  display: none; /* Hide the section header for the header section */
}

/* Content Layout Section - 50/50 Split */
.card-content-layout {
  display: flex;
  gap: 0;
  flex: 1;
  min-height: 0;
}

/* Left Column - Content */
.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #f1f3f4;
}

/* Right Column - Visual Content */
.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Content Sections */
.content-section {
  padding: 20px;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.content-section:last-child {
  border-bottom: none;
  flex: 1;
}

/* Description section takes more space */
.description-section {
  flex: 1;
}

/* DateTime section is compact */
.datetime-section {
  flex-shrink: 0;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f8f9fa;
  flex-shrink: 0;
}

.section-header h3 {
  font-size: 15px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

/* Section Icons */
.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  font-size: 14px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
  flex-shrink: 0;
}

/* Section Content */
.section-content {
  color: #495057;
  line-height: 1.5;
  flex: 1;
  overflow: hidden;
}

/* Comments Sidebar */
.comments-sidebar {
  position: sticky;
  top: 80px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}



.manage-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
}

.manage-btn:hover {
  background: #e9ecef;
}

/* Manage button inside image gallery */
.manage-btn-inside {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  color: #495057;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.manage-btn-inside:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #007bff;
  color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

/* Staff manage button styling */
.manage-btn-inside.staff-manage-btn {
  background: rgba(220, 53, 69, 0.95);
  border-color: rgba(220, 53, 69, 0.3);
  color: white;
}

.manage-btn-inside.staff-manage-btn:hover {
  background: rgba(220, 53, 69, 1);
  border-color: #dc3545;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.25);
}

/* Description Section */
.description-section .section-content p {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* DateTime Section */
.datetime-section .datetime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f8f9fa;
}

.datetime-section .datetime-item:last-child {
  border-bottom: none;
}

.datetime-section .modern-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.datetime-section .modern-label i {
  color: #667eea;
  font-size: 12px;
}

.datetime-section .value {
  font-size: 12px;
  font-weight: 600;
  color: #212529;
}

/* Location Section */
.location-section .location-name {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
}

.location-section .map-container {
  margin-top: 8px;
}

.location-section .event-map {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Images Section */
.images-section .image-gallery {
  position: relative;
}

.images-section .main-image {
  margin-top: 8px;
  position: relative;
}

.images-section .main-image img {
  height: 200px;
  width: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.main-image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  margin-top: 8px;
}

.main-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.2s;
}

.main-image:hover img {
  transform: scale(1.05);
}

.image-count {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Image management styles */
.empty-images {
  min-height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Comments Section */
.comments-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.comments-header h2 {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.comment-count {
  background: #f8f9fa;
  color: #495057;
  padding: 3px 10px;
  border-radius: 14px;
  font-size: 11px;
  font-weight: 600;
}

.comments-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  padding-right: 4px;
}

.comment-form-container {
  flex-shrink: 0;
  border-top: 1px solid #f1f3f4;
  padding-top: 16px;
}

.comment-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.comment-author {
  font-weight: 600;
  color: #212529;
  font-size: 14px;
}

.comment-time {
  color: #6c757d;
  font-size: 12px;
}

.comment-text {
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.comment-text.hidden {
  color: #dc3545;
  font-style: italic;
}

.hidden-notice {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  font-style: italic;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-actions form {
  margin: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 16px;
  color: #6c757d;
  font-size: 12px;
  transition: all 0.2s;
  cursor: pointer;
}

.action-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.action-btn.like-btn:hover {
  color: #007bff;
}

.action-btn.dislike-btn:hover {
  color: #dc3545;
}

.action-btn.delete-btn:hover {
  color: #dc3545;
}

.action-btn.report-btn:hover {
  color: #ffc107;
}

.action-btn.reported {
  color: #ffc107;
  cursor: default;
}

.action-btn.disabled {
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.action-btn.disabled:hover {
  background: transparent;
  color: #6c757d;
}

.comment-form {
  display: flex;
  gap: 12px;
  align-items: center;
}

.comment-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #dee2e6;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.comment-input:focus {
  border-color: #007bff;
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  transition: background 0.2s;
}

.submit-btn:hover {
  background: #0056b3;
}

/* No Comments State */
.no-comments-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;
}

.no-comments-icon {
  margin-bottom: 16px;
}

.no-comments-icon i {
  font-size: 48px;
  color: #dee2e6;
  opacity: 0.8;
}

.no-comments-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #6c757d;
  margin: 0 0 8px 0;
}

.no-comments-content p {
  font-size: 14px;
  color: #adb5bd;
  margin: 0;
  line-height: 1.5;
}

/* Dropdown Menu Fix for All Screen Sizes */
.dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid rgba(0,0,0,.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
  backdrop-filter: none !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-layout {
    grid-template-columns: 1fr 350px;
  }
}

@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .comments-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
  }

  .comments-section {
    height: auto;
  }

  .comments-list {
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .event-content {
    padding: 8px;
    max-width: 100%;
  }

  /* Mobile layout adjustments */
  .page-layout {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .main-content {
    gap: 12px;
    width: 100%;
  }

  .comments-sidebar {
    width: 100%;
    order: 10; /* Move comments to bottom */
  }

  .comments-section {
    max-height: 300px;
    position: static;
    width: 100%;
    padding: 16px;
    margin-top: 8px;
  }

  /* Comprehensive card mobile adjustments */
  .comprehensive-event-card {
    max-height: none;
    margin-bottom: 12px;
  }

  .card-header-section {
    padding: 16px;
  }

  .event-title {
    font-size: 20px;
  }

  .event-meta {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  /* Mobile layout - stack vertically */
  .card-content-layout {
    flex-direction: column;
  }

  .left-column {
    border-right: none;
    border-bottom: 1px solid #f1f3f4;
  }

  .right-column {
    border-bottom: none;
  }

  .content-section {
    padding: 16px;
  }

  .section-header h3 {
    font-size: 16px;
  }

  .section-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  /* Mobile section adjustments */
  .location-section .event-map {
    height: 180px;
  }

  .images-section .main-image img {
    height: 200px;
  }

  .comment-item {
    gap: 10px;
  }

  .comment-avatar {
    width: 32px;
    height: 32px;
  }

  .comments-list {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .event-content {
    padding: 4px;
    max-width: 100%;
  }

  .event-title {
    font-size: 18px;
  }

  .comprehensive-event-card {
    width: 100%;
    margin-bottom: 6px; /* Tighter spacing on very small screens */
  }

  .title-actions-header {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .title-actions-header .header-actions {
    justify-content: flex-end;
  }

  .meta-section {
    padding: 12px 16px;
  }

  .header-section {
    padding: 12px;
  }

  .content-section {
    padding: 12px;
  }

  .comments-section {
    padding: 12px;
    width: 100%;
    margin-top: 6px;
  }

  /* Compact section styling for very small screens */
  .section-header h3 {
    font-size: 14px;
  }

  .section-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .section-header {
    margin-bottom: 10px;
    padding-bottom: 6px;
  }

  .comments-list {
    max-height: 250px;
  }

  /* Mobile adjustments for no comments state */
  .no-comments-state {
    padding: 20px 12px;
    min-height: 120px;
  }

  .no-comments-icon i {
    font-size: 32px;
  }

  .no-comments-content h4 {
    font-size: 14px;
  }

  .no-comments-content p {
    font-size: 12px;
  }

  /* Ensure full width on very small screens */
  .page-layout,
  .main-content {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .comments-sidebar,
  .comments-section,
  .comprehensive-event-card {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  /* Maintain logical order on very small screens */
  .comprehensive-event-card {
    order: 1;
  }

  .comments-sidebar {
    order: 10;
  }
}

/* Manage Images Interface */
.manage-images-container {
  max-width: 100%;
}

.upload-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px dashed #dee2e6;
}

.existing-images-section {
  background: white;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.image-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.image-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.image-preview {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.primary-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.image-actions {
  padding: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.image-actions .btn {
  flex: 1;
  min-width: 80px;
  font-size: 11px;
  padding: 4px 8px;
}

@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }

  .image-preview {
    height: 120px;
  }

  .image-actions {
    padding: 8px;
    gap: 6px;
  }

  .image-actions .btn {
    font-size: 10px;
    padding: 3px 6px;
  }

  /* Compact header on mobile */
  .event-header {
    padding: 12px 16px;
    margin-bottom: 16px;
    max-width: none;
    min-width: auto;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .header-main {
    margin-bottom: 8px;
  }

  .event-title {
    font-size: 18px;
  }

  .journey-breadcrumb {
    font-size: 11px;
  }

  .status-badges {
    margin-right: 8px;
  }

  .status-badge {
    padding: 1px 6px;
    font-size: 9px;
  }

  .like-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .menu-btn {
    width: 28px;
    height: 28px;
  }

  .author-avatar {
    width: 28px;
    height: 28px;
  }

  .author-name {
    font-size: 12px;
  }

  .event-date, .update-time {
    font-size: 10px;
  }

  /* Mobile adjustments for manage button */
  .manage-btn-inside {
    bottom: 8px;
    right: 8px;
    padding: 6px 10px;
    font-size: 12px;
    gap: 4px;
  }

  .manage-btn-corner {
    bottom: 8px;
    right: 8px;
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* Ensure dropdown menus are fully opaque on mobile */
  .dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid rgba(0,0,0,.15) !important;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
  }
}
</style>

<script>
// Global map variables
let mymap;
let locationMarker;
let redIcon;

// Simple and reliable back button function
function smartBack() {
  // Method 1: Check for explicit back URL parameter (most reliable)
  const urlParams = new URLSearchParams(window.location.search);
  const backUrl = urlParams.get('back');

  if (backUrl) {
    try {
      const decodedUrl = decodeURIComponent(backUrl);
      // Only allow internal URLs for security
      if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
        window.location.href = decodedUrl;
        return;
      }
    } catch (e) {
      // Invalid URL, continue to next method
    }
  }

  // Method 2: Simple history back (works most of the time)
  if (window.history.length > 1) {
    window.history.back();
    return;
  }

  // Method 3: Fallback based on context
  const eventContent = document.querySelector('.event-content');
  const isLoggedIn = eventContent.dataset.isLoggedIn === 'true';
  const journeyVisibility = eventContent.dataset.journeyVisibility;

  if (!isLoggedIn) {
    window.location.href = eventContent.dataset.landingUrl || '/';
  } else if (journeyVisibility === 'published') {
    window.location.href = eventContent.dataset.publishedJourneyUrl;
  } else if (journeyVisibility === 'public') {
    window.location.href = eventContent.dataset.publicJourneyUrl;
  } else if (journeyVisibility === 'private') {
    window.location.href = eventContent.dataset.privateJourneyUrl;
  } else {
    window.location.href = '/journey/public';
  }
}

// Simple back button text update
function updateBackButtonText() {
  const backButtonText = document.getElementById('backButtonText');

  // Check for back URL parameter first
  const urlParams = new URLSearchParams(window.location.search);
  const backUrl = urlParams.get('back');

  if (backUrl) {
    // Try to determine context from back URL
    if (backUrl.includes('/journey/')) {
      backButtonText.textContent = 'Back to Journey';
    } else if (backUrl.includes('/discovery')) {
      backButtonText.textContent = 'Back to Discovery';
    } else if (backUrl.includes('/manage')) {
      backButtonText.textContent = 'Back to Management';
    } else {
      backButtonText.textContent = 'Back';
    }
    return;
  }

  // Fallback to simple referrer check
  const referrer = document.referrer;
  if (referrer && referrer.includes('/journey/')) {
    backButtonText.textContent = 'Back to Journey';
  } else if (referrer && referrer.includes('/discovery')) {
    backButtonText.textContent = 'Back to Discovery';
  } else if (referrer && referrer.includes('/manage')) {
    backButtonText.textContent = 'Back to Management';
  } else {
    backButtonText.textContent = 'Back to Journey';
  }
}

// Function to populate back parameter fields in all forms
function populateBackParameterFields() {
  const urlParams = new URLSearchParams(window.location.search);
  const backUrl = urlParams.get('back');

  if (backUrl) {
    // Find all back parameter fields and populate them
    const backFields = document.querySelectorAll('.back-param-field');
    backFields.forEach(field => {
      field.value = backUrl;
    });
  }
}

document.addEventListener('DOMContentLoaded', function() {
  updateBackButtonText();
  populateBackParameterFields();
});

// Staff Image Management Functions
function showStaffImageManager(eventId) {
  fetch(`/event/${eventId}/images`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showStaffImageGallery(eventId, data.images || []);
      } else {
        showModal('Error', data.message || 'Failed to load event images. Please try again.', {
          actionText: 'OK',
          onAction: function() { return true; }
        });
      }
    })
    .catch(error => {
      console.error('Error fetching images:', error);
      showModal('Error', 'Failed to load event images. Please try again.', {
        actionText: 'OK',
        onAction: function() { return true; }
      });
    });
}

function showStaffImageGallery(eventId, images) {
  let imagesHtml = '';

  if (images && images.length > 0) {
    imagesHtml = `
      <div class="row g-3 mb-4">
        ${images.map((image, index) =>
          `<div class="col-md-4 col-6" id="staff-image-item-${image.id}">
            <div class="card h-100 position-relative">
              <div class="position-relative">
                <div class="form-check position-absolute top-0 start-0 m-2">
                  <input class="form-check-input staff-image-checkbox" type="checkbox" value="${image.id}"
                    id="staff-image-check-${image.id}">
                  <label class="form-check-label" for="staff-image-check-${image.id}"></label>
                </div>
                <img src="${image.url}" alt="Event image" class="card-img-top" style="height: 160px; object-fit: cover;">
              </div>
            </div>
          </div>`
        ).join('')}
      </div>

      <!-- Selection Controls -->
      <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
        <div class="btn-group">
          <button type="button" id="selectAllStaffImages" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-check-square me-1"></i>Select All
          </button>
          <button type="button" id="deselectAllStaffImages" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-square me-1"></i>Deselect All
          </button>
        </div>
        <span class="text-muted" id="selectedStaffCount">0 selected</span>
      </div>

      <!-- Delete Section -->
      <div class="border rounded p-3 bg-white">
        <h6 class="text-danger mb-3">
          <i class="bi bi-trash me-2"></i>Delete Selected Images
        </h6>
        <form id="staffDeleteForm" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="staffDeleteReason" class="form-label">
              <strong>Reason for Deletion *</strong>
            </label>
            <textarea
              class="form-control"
              id="staffDeleteReason"
              name="edit_reason"
              rows="3"
              required
              maxlength="500"></textarea>
            <div class="invalid-feedback">
              Please provide a reason for deleting the selected images.
            </div>
            <div class="form-text">
              <i class="bi bi-info-circle me-1"></i>
              This reason will be logged for each image deletion and sent to the event owner.
            </div>
          </div>
          <button type="button" id="executeStaffDelete" class="btn btn-danger" disabled>
            <i class="bi bi-trash me-1"></i>Delete Selected Images
          </button>
        </form>
      </div>
    `;
  } else {
    imagesHtml = `
      <div class="text-center p-4">
        <i class="bi bi-images fs-1 text-secondary"></i>
        <p class="text-muted mt-2">No images found for this event</p>
      </div>
    `;
  }

  const formHtml = `
    <div class="staff-image-manager">
      <div class="alert alert-warning d-flex align-items-center mb-3">
        <i class="bi bi-shield-exclamation me-2"></i>
        <div>
          <small>Select images to delete and provide a reason. </small>
        </div>
      </div>
      ${imagesHtml}
    </div>
  `;

  showModal('Staff Image Management', formHtml, {
    size: 'large'
  });

  // Setup staff image selection functionality
  setupStaffImageSelect(eventId);
}

function setupStaffImageSelect(eventId) {
  // Get references to buttons and checkboxes
  const selectAllBtn = document.getElementById('selectAllStaffImages');
  const deselectAllBtn = document.getElementById('deselectAllStaffImages');
  const executeDeleteBtn = document.getElementById('executeStaffDelete');
  const checkboxes = document.querySelectorAll('.staff-image-checkbox');
  const selectedCountEl = document.getElementById('selectedStaffCount');
  const deleteReasonTextarea = document.getElementById('staffDeleteReason');

  if (!checkboxes.length) return;

  // Function to update the selected count and delete button state
  function updateSelectionState() {
    const selectedCount = document.querySelectorAll('.staff-image-checkbox:checked').length;
    selectedCountEl.textContent = `${selectedCount} selected`;
    executeDeleteBtn.disabled = selectedCount === 0;

    // Update button text based on selection
    if (selectedCount === 0) {
      executeDeleteBtn.innerHTML = '<i class="bi bi-trash me-1"></i>Delete Selected Images';
    } else {
      executeDeleteBtn.innerHTML = `<i class="bi bi-trash me-1"></i>Delete ${selectedCount} Image${selectedCount > 1 ? 's' : ''}`;
    }
  }

  // Add change event listeners to all checkboxes
  checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectionState);

    // Make the entire card clickable to toggle checkbox
    const card = checkbox.closest('.card');
    if (card) {
      card.addEventListener('click', function (e) {
        // Don't toggle if the checkbox itself was clicked (it will toggle itself)
        if (e.target !== checkbox) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event('change'));
        }
      });
    }
  });

  // Select all images
  selectAllBtn?.addEventListener('click', function () {
    checkboxes.forEach(checkbox => {
      checkbox.checked = true;
    });
    updateSelectionState();
  });

  // Deselect all images
  deselectAllBtn?.addEventListener('click', function () {
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
    updateSelectionState();
  });

  // Execute delete with validation
  executeDeleteBtn?.addEventListener('click', function () {
    const selectedImageIds = Array.from(document.querySelectorAll('.staff-image-checkbox:checked'))
      .map(checkbox => checkbox.value);

    if (selectedImageIds.length === 0) return;

    const editReason = deleteReasonTextarea.value.trim();
    const form = document.getElementById('staffDeleteForm');

    // Bootstrap validation
    form.classList.add('was-validated');

    if (!form.checkValidity() || !editReason) {
      deleteReasonTextarea.focus();
      return;
    }

    // Disable button and show loading state
    executeDeleteBtn.disabled = true;
    executeDeleteBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Deleting...';

    // Perform batch deletion
    performStaffBatchImageDelete(eventId, selectedImageIds, editReason);
  });

  // Real-time validation for textarea
  deleteReasonTextarea?.addEventListener('input', function() {
    if (this.value.trim().length > 0) {
      this.setCustomValidity('');
      this.classList.remove('is-invalid');
      this.classList.add('is-valid');
    } else {
      this.setCustomValidity('Please provide a reason for deleting these images.');
      this.classList.remove('is-valid');
      this.classList.add('is-invalid');
    }
  });

  // Initial state update
  updateSelectionState();
}

// Simplified staff image deletion - no more complex modals

// Function to handle batch staff deletion API call
function performStaffBatchImageDelete(eventId, imageIds, editReason) {
  if (!imageIds || imageIds.length === 0) {
    console.error('No image IDs provided for batch deletion');
    return;
  }

  console.log('Attempting batch deletion of images:', imageIds);

  // Show loading state for all images
  imageIds.forEach(id => {
    const imageItem = document.getElementById(`staff-image-item-${id}`);
    if (imageItem) {
      imageItem.classList.add('opacity-50');
    }
  });

  const url = `/event/${eventId}/images/staff-delete-batch`;

  // Create form data with image IDs and edit reason
  const formData = new FormData();
  formData.append('image_ids', imageIds.join(','));
  formData.append('edit_reason', editReason);

  fetch(url, {
    method: 'POST',
    body: formData,
    credentials: 'same-origin'
  })
    .then(response => {
      console.log(`Batch delete response:`, response.status, response.statusText);
      if (!response.ok) {
        return response.text().then(text => {
          console.error(`Server error for batch deletion:`, text);
          throw new Error(`Failed to delete images: ${response.status} ${response.statusText}`);
        });
      }
      return response.json();
    })
    .then(data => {
      console.log(`Batch delete response data:`, data);

      // Close the modal properly using the utility function
      if (typeof window.closeModal === 'function') {
        window.closeModal();
      }

      // Show result message and reload
      setTimeout(() => {
        if (data.success) {
          let message;
          if (data.success_count === 1) {
            message = "Image deleted successfully!";
          } else {
            message = `${data.success_count} images deleted successfully!`;
          }

          if (data.fail_count > 0) {
            message += ` (${data.fail_count} failed)`;
          }

          storeFlashMessage(message, data.fail_count > 0 ? "warning" : "success");
        } else {
          storeFlashMessage(data.message || "Failed to delete images", "danger");
        }

        // Reload the page
        window.location.reload();
      }, 300); // Small delay to ensure modal closes
    })
    .catch(error => {
      console.error(`Error in batch deletion:`, error);

      // Remove loading state for all images
      imageIds.forEach(id => {
        const imageItem = document.getElementById(`staff-image-item-${id}`);
        if (imageItem) {
          imageItem.classList.remove('opacity-50');
        }
      });

      // Re-enable the delete button
      const executeDeleteBtn = document.getElementById('executeStaffDelete');
      if (executeDeleteBtn) {
        executeDeleteBtn.disabled = false;
        executeDeleteBtn.innerHTML = '<i class="bi bi-trash me-1"></i>Delete Selected Images';
      }

      // Show error message
      storeFlashMessage(error.message || "Failed to delete images", "danger");
    });
}

// Function to handle the actual staff deletion API call (kept for single image deletions)
function performStaffImageDelete(eventId, imageIds, editReason) {
  if (!imageIds || imageIds.length === 0) {
    console.error('No image IDs provided for deletion');
    return;
  }

  // Ensure imageIds is an array
  const imageIdsArray = Array.isArray(imageIds) ? imageIds : [imageIds];

  console.log('Attempting to delete images with IDs:', imageIdsArray);

  // Show loading state for all images
  imageIdsArray.forEach(id => {
    const imageItem = document.getElementById(`staff-image-item-${id}`);
    if (imageItem) {
      imageItem.classList.add('opacity-50');
    }
  });

  // Track progress
  let successCount = 0;
  let failCount = 0;
  let errors = [];

  // Process each image deletion sequentially
  const deleteNextImage = (index) => {
    if (index >= imageIdsArray.length) {
      // All images have been processed
      console.log(`Completed all staff image deletions. Success: ${successCount}, Failed: ${failCount}`);

      // Close the modal properly using the utility function
      if (typeof window.closeModal === 'function') {
        window.closeModal();
      }

      // Show result message and reload
      setTimeout(() => {
        if (failCount === 0) {
          // All deletions were successful
          const message = imageIdsArray.length > 1
            ? `${successCount} images deleted successfully!`
            : "Image deleted successfully!";
          storeFlashMessage(message, "success");
        } else if (successCount === 0) {
          // All deletions failed
          storeFlashMessage(`Failed to delete images. ${errors.join(', ')}`, "danger");
        } else {
          // Mixed results
          const mixedMessage = `Deleted ${successCount} images. Failed to delete ${failCount} images.`;
          storeFlashMessage(mixedMessage, "warning");
        }

        // Reload the page
        window.location.reload();
      }, 300); // Small delay to ensure modal closes

      return;
    }

    const imageId = imageIdsArray[index];
    const url = `/event/${eventId}/image/${imageId}/staff-delete`;
    console.log(`Deleting image ${index + 1}/${imageIdsArray.length}, ID: ${imageId}`);

    // Create form data with edit reason
    const formData = new FormData();
    formData.append('edit_reason', editReason);

    fetch(url, {
      method: 'POST',
      body: formData,
      credentials: 'same-origin'
    })
      .then(response => {
        console.log(`Response for image ${imageId}:`, response.status, response.statusText);
        if (!response.ok) {
          return response.text().then(text => {
            console.error(`Server error for image ${imageId}:`, text);
            throw new Error(`Failed to delete image ${imageId}: ${response.status} ${response.statusText}`);
          });
        }
        return response.json();
      })
      .then(data => {
        console.log(`Response data for image ${imageId}:`, data);
        if (data.success) {
          successCount++;
          // Remove the image from the DOM immediately for visual feedback
          const imageElement = document.getElementById(`staff-image-item-${imageId}`);
          if (imageElement) {
            imageElement.style.transition = 'opacity 0.3s ease';
            imageElement.style.opacity = '0.3';
          }
        } else {
          failCount++;
          const errorMsg = `Image ${imageId}: ${data.message || "Unknown error"}`;
          errors.push(errorMsg);
          console.error(`Deletion failed for image ${imageId}:`, data.message);

          // Remove loading state for this image
          const imageItem = document.getElementById(`staff-image-item-${imageId}`);
          if (imageItem) {
            imageItem.classList.remove('opacity-50');
          }
        }

        // Process the next image
        deleteNextImage(index + 1);
      })
      .catch(error => {
        console.error(`Error deleting image ${imageId}:`, error);
        failCount++;
        errors.push(error.message);

        // Remove loading state for this image
        const imageItem = document.getElementById(`staff-image-item-${imageId}`);
        if (imageItem) {
          imageItem.classList.remove('opacity-50');
        }

        // Process the next image
        deleteNextImage(index + 1);
      });
  };

  // Start the deletion process with the first image
  deleteNextImage(0);
}

// Initialize map if coordinates exist
document.addEventListener('DOMContentLoaded', function() {
  const mapElement = document.getElementById('map');
  if (mapElement && mapElement.dataset.lat && mapElement.dataset.lng) {
    const lat = parseFloat(mapElement.dataset.lat);
    const lng = parseFloat(mapElement.dataset.lng);
    const locationName = mapElement.dataset.locationName;

    // Only initialize if we have valid coordinates
    if (!isNaN(lat) && !isNaN(lng)) {
      const map = L.map('map').setView([lat, lng], 15);
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(map);

      L.marker([lat, lng]).addTo(map)
        .bindPopup(locationName)
        .openPopup();
    }
  }
});

// Event deletion confirmation
function confirmDeleteEvent(eventId) {
  if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
    document.getElementById('deleteEventForm_' + eventId).submit();
  }
}

// Comment deletion confirmation
function confirmDeleteComment(commentEventIds) {
  const [commentId, eventId] = commentEventIds.split(',');

  showModal('Delete Comment', 'Are you sure you want to delete this comment? This action cannot be undone.', {
    actionText: 'Delete',
    onAction: function() {
      const form = document.createElement('form');
      form.method = 'post';
      form.action = `/event/${eventId}/comment/${commentId}/delete`;

      // Add back parameter if it exists in the URL
      const urlParams = new URLSearchParams(window.location.search);
      const backUrl = urlParams.get('back');
      if (backUrl) {
        const backInput = document.createElement('input');
        backInput.type = 'hidden';
        backInput.name = 'back';
        backInput.value = backUrl;
        form.appendChild(backInput);
      }

      document.body.appendChild(form);
      form.submit();
      return true;
    }
  });
}

// Protected event message
function showProtectedEventMessage() {
  alert('This event is protected from editing by staff members.');
}

// Edit Event Modal Handler
document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll(".editEventBtn").forEach(button => {
    button.addEventListener("click", async function () {
      let eventId = this.getAttribute("data-event-id");
      const editUrl = `/event/${eventId}/edit`

      const response = await fetch(editUrl);
      const formHtml = await response.text();

      showModal('Edit Event', formHtml, {
        actionText: 'Save',
        onAction: async function () {
          const form = document.getElementById('editEventForm');
          form.classList.add('was-validated');

          const startDatetime = document.getElementById('startDatetime');
          const endDatetime = document.getElementById('endDatetime');
          const title = document.getElementById('title');
          const description = document.getElementById('description');
          const location = document.getElementById('location');
          const imageInput = document.getElementById('image');

          // Only validate datetime fields if they are not disabled (for content managers)
          if (endDatetime && startDatetime.value && endDatetime.value && !startDatetime.disabled && !endDatetime.disabled) {
            if (new Date(endDatetime.value) <= new Date(startDatetime.value)) {
              endDatetime.setCustomValidity("End date must be after start date");
            } else {
              endDatetime.setCustomValidity("");
            }
          }

          if (imageInput && imageInput.files.length > 0) {
            if (imageInput.files[0].size > 5 * 1024 * 1024) {
              imageInput.setCustomValidity("File size exceeds 5MB");
            } else {
              imageInput.setCustomValidity("");
            }
          }

          if (!form.checkValidity()) {
            return false;
          }

          const formData = new FormData(form);

          try {
            const response = await fetch(`/event/${eventId}/edit`, {
              method: 'POST',
              body: formData,
              headers: {
                'X-Requested-With': 'XMLHttpRequest'
              }
            });

            if (response.ok) {
              // Close the modal first
              const modalElement = document.getElementById('commonModal');
              if (modalElement) {
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                  modalInstance.hide();
                }
              }

              // Parse JSON response
              const data = await response.json();

              // Store the message for display after redirect
              const messageType = data.success ? 'success' : 'danger';
              storeFlashMessage(data.message, messageType);

              // Redirect to the specified URL
              window.location.href = data.redirect_url;
            } else {
              const errorText = await response.text();
              console.error('Error updating event:', errorText);
              alert('Error updating event. Please try again.');
            }
          } catch (error) {
            console.error('Error:', error);
            alert('Error updating event. Please try again.');
          }
        }
      });

      // Initialize enhanced LocationSelector after modal is shown
      setTimeout(() => {
        console.log('🔧 Initializing enhanced LocationSelector for edit event modal...');

        // Execute scripts from the loaded content
        const parser = new DOMParser();
        const doc = parser.parseFromString(formHtml, 'text/html');
        const scripts = doc.querySelectorAll('script');

        scripts.forEach((script, index) => {
          try {
            console.log(`📝 Executing script ${index + 1}/${scripts.length}`);

            if (script.src) {
              // External script
              const newScript = document.createElement('script');
              newScript.src = script.src;
              newScript.onload = () => console.log(`✅ External script loaded: ${script.src}`);
              newScript.onerror = () => console.error(`❌ Failed to load script: ${script.src}`);
              document.head.appendChild(newScript);

              // Remove after execution to prevent duplicates
              setTimeout(() => {
                if (newScript.parentNode) {
                  newScript.parentNode.removeChild(newScript);
                }
              }, 100);
            } else {
              // Inline script - wrap in function scope to prevent variable conflicts
              const scriptContent = script.textContent;
              if (scriptContent.trim()) {
                try {
                  // Execute in isolated scope to prevent variable conflicts
                  (function() {
                    eval(scriptContent);
                  })();
                  console.log(`✅ Inline script ${index + 1} executed successfully`);
                } catch (evalError) {
                  console.error(`❌ Error executing inline script ${index + 1}:`, evalError);
                }
              }
            }

          } catch (error) {
            console.error(`❌ Error executing script ${index + 1}:`, error);
          }
        });

        // Initialize EditEventLocationHandler after scripts execute
        setTimeout(() => {
          console.log('🔄 Initializing EditEventLocationHandler for edit event...');

          // Call the new initialization function if it exists
          if (typeof window.initializeEditEventLocationHandler === 'function') {
            try {
              const success = window.initializeEditEventLocationHandler();
              if (success) {
                console.log('✅ EditEventLocationHandler initialized via function call');
              } else {
                console.error('❌ EditEventLocationHandler initialization failed');
              }
            } catch (error) {
              console.error('❌ Error initializing EditEventLocationHandler:', error);
            }
          } else {
            console.warn('⚠️ initializeEditEventLocationHandler function not found, trying fallback...');

            // Fallback: try to initialize the old location search functionality
            if (typeof initializeEditModalLocationSearch === 'function') {
              try {
                initializeEditModalLocationSearch();
                console.log('✅ Fallback location search initialized');
              } catch (error) {
                console.error('❌ Error with fallback initialization:', error);
              }
            }
          }

          // Verify initialization
          setTimeout(() => {
            if (window.editEventLocationHandler) {
              console.log('✅ EditEventLocationHandler is ready for edit event');
            } else if (window.locationSelector) {
              console.log('✅ LocationSelector is ready for edit event (fallback)');
              console.log('Current state:', window.locationSelector.state);
            } else {
              console.error('❌ No location handler available after initialization');
            }
          }, 100);
        }, 300);
      }, 100);
    });
  });
});

// Image Gallery Handler
document.addEventListener('DOMContentLoaded', function() {
  // Handle image gallery clicks
  document.querySelectorAll('.event-image-button').forEach(button => {
    button.addEventListener('click', async function() {
      const eventId = this.getAttribute('event-id');
      try {
        const response = await fetch(`/event/image/${eventId}`);
        const html = await response.text();

        const modalResult = showModal('Event Images', html, {
          hideCloseButton: false,
          size: 'large'
        });

        // Configure modal for image gallery
        const modalDialog = document.querySelector('#commonModal .modal-dialog');
        if (modalDialog) {
          modalDialog.classList.add('modal-lg', 'modal-dialog-image-gallery');
        }

        if (modalResult && modalResult.modalInstance) {
          const modalElement = modalResult.modalInstance._element;
          initGallery(modalElement);
        } else {
          initGallery(document.getElementById('commonModal'));
        }
      } catch (error) {
        console.error('Error loading images:', error);
        alert('Error loading images. Please try again.');
      }
    });
  });

  // Handle manage images button - use enhanced image management system
  const manageImagesBtn = document.getElementById('manageImagesBtn');
  if (manageImagesBtn) {
    manageImagesBtn.addEventListener('click', function() {
      const eventContent = document.querySelector('.event-content');
      const eventId = parseInt(eventContent.getAttribute('data-event-id'));
      const isPremiumAccess = eventContent.getAttribute('data-premium-access') === 'true';
      const uploadUrl = eventContent.getAttribute('data-upload-url');
      const plansUrl = eventContent.getAttribute('data-plans-url');

      showEventImageManager(eventId, isPremiumAccess, uploadUrl, plansUrl);
    });
  }

  // Handle add first image button
  const addFirstImageBtn = document.getElementById('addFirstImageBtn');
  if (addFirstImageBtn) {
    addFirstImageBtn.addEventListener('click', function() {
      const eventContent = document.querySelector('.event-content');
      const eventId = parseInt(eventContent.getAttribute('data-event-id'));
      const isPremiumAccess = eventContent.getAttribute('data-premium-access') === 'true';
      const uploadUrl = eventContent.getAttribute('data-upload-url');

      showEventImageUploader(eventId, isPremiumAccess, uploadUrl);
    });
  }

  // Handle staff manage images button
  const staffManageImagesBtn = document.getElementById('staffManageImagesBtn');
  if (staffManageImagesBtn) {
    staffManageImagesBtn.addEventListener('click', function() {
      const eventContent = document.querySelector('.event-content');
      const eventId = parseInt(eventContent.getAttribute('data-event-id'));

      showStaffImageManager(eventId);
    });
  }
});

// Report comment functionality
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.report-comment-button').forEach(button => {
    button.addEventListener('click', async function() {
      const commentId = this.getAttribute('data-comment-id');

      const formHtml = `
        <form id="reportCommentForm" action="/event/comment/${commentId}/report" method="post">
          <input type="hidden" name="back" class="back-param-field" value="">
          <div class="mb-3">
            <label for="reportReason" class="modern-label">
              <i class="bi bi-flag"></i>
              Reason for reporting:
            </label>
            <textarea class="form-control report-input" id="reportReason" name="reason" required></textarea>
          </div>
        </form>
      `;

      showModal('Report Comment', formHtml, {
        actionText: 'Submit',
        onAction: function() {
          const form = document.getElementById('reportCommentForm');
          if (form.checkValidity()) {
            form.submit();
          } else {
            form.reportValidity();
          }
        }
      });

      // Populate the back parameter field in the dynamically created form
      setTimeout(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');
        if (backUrl) {
          const backField = document.querySelector('#reportCommentForm .back-param-field');
          if (backField) {
            backField.value = backUrl;
          }
        }
      }, 100); // Small delay to ensure modal is fully rendered
    });
  });

});

// Initialize location search functionality for edit modal content
function initializeEditModalLocationSearch() {
  console.log('🔧 Initializing edit modal location search functionality');

  // Check if the location input exists in the modal
  const locationInput = document.getElementById('location');
  if (!locationInput) {
    console.warn('⚠️ Location input not found in edit modal');
    return;
  }

  console.log('✅ Found location input, setting up edit search functionality');

  // Enhanced location search function for edit modal
  async function editModalSearchLocations() {
    console.log('🔍 editModalSearchLocations() called (database search)');
    let query = locationInput.value.trim();
    let suggestionBox = document.getElementById('locationSuggestions');

    if (!suggestionBox) {
      console.warn('⚠️ locationSuggestions element not found');
      return;
    }

    if (query.length < 2) {
      hideEditModalLocationDropdown();
      return;
    }

    try {
      console.log(`🔍 Searching database for: "${query}"`);
      let response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
      let data = await response.json();
      suggestionBox.innerHTML = '';

      if (data.length === 0) {
        hideEditModalLocationDropdown();
        console.log('🔍 No database results found');
        return;
      }

      console.log(`🔍 Building ${data.length} database dropdown items`);
      data.forEach((location) => {
        let item = document.createElement('div');
        item.classList.add('list-group-item', 'list-group-item-action');
        item.textContent = location.name;

        item.onclick = async () => {
          console.log(`✅ Selected database location: ${location.name}`);
          locationInput.value = location.name;

          // Auto-fill coordinates and map from database location
          await editModalLoadLocationCoordinates(location.name);

          hideEditModalLocationDropdown();

          // Add subtle animation feedback
          locationInput.style.transform = 'scale(1.02)';
          setTimeout(() => {
            locationInput.style.transform = 'scale(1)';
          }, 150);
        };

        suggestionBox.appendChild(item);
      });

      showEditModalLocationDropdown();
      console.log('✅ Database dropdown shown successfully');

    } catch (err) {
      console.error('❌ Error during location search:', err);
      hideEditModalLocationDropdown();
    }
  }

  // Load coordinates for a database location and update map
  async function editModalLoadLocationCoordinates(locationName) {
    try {
      console.log(`🔍 Loading coordinates for: ${locationName}`);
      const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
      const data = await response.json();

      if (data.lat && data.lng) {
        console.log(`✅ Found coordinates: ${data.lat}, ${data.lng}`);

        // Update coordinate fields
        document.getElementById('latitude').value = data.lat;
        document.getElementById('longitude').value = data.lng;

        // Update map search field
        const mapInput = document.getElementById('location-map');
        if (mapInput) {
          mapInput.value = locationName;
        }

        // Update map if available
        if (mymap && locationMarker) {
          mymap.setView([data.lat, data.lng], 13);
          locationMarker.setLatLng([data.lat, data.lng]);
        }
      } else {
        console.log(`⚠️ No coordinates found for: ${locationName}`);
      }
    } catch (error) {
      console.error('❌ Error loading location coordinates:', error);
    }
  }

  // Show/hide dropdown functions for edit modal
  function showEditModalLocationDropdown() {
    const suggestionBox = document.getElementById('locationSuggestions');
    if (!suggestionBox) return;

    console.log('📍 Showing edit modal location dropdown');
    suggestionBox.classList.remove('d-none');
    suggestionBox.classList.add('show');
    suggestionBox.style.display = 'block';
    suggestionBox.style.visibility = 'visible';
    suggestionBox.style.opacity = '1';
  }

  function hideEditModalLocationDropdown() {
    const suggestionBox = document.getElementById('locationSuggestions');
    if (suggestionBox) {
      console.log('📍 Hiding edit modal location dropdown');
      suggestionBox.classList.add('d-none');
      suggestionBox.classList.remove('show');
      suggestionBox.style.display = 'none';
      suggestionBox.style.visibility = 'hidden';
      suggestionBox.style.opacity = '0';
    }
  }

  // Remove any existing event listeners to prevent duplicates
  locationInput.removeEventListener('input', editModalSearchLocations);
  locationInput.removeEventListener('focus', editModalSearchLocations);

  // Attach event listeners
  locationInput.addEventListener('input', editModalSearchLocations);
  locationInput.addEventListener('focus', function() {
    if (this.value.trim().length >= 2) {
      editModalSearchLocations();
    }
  });

  // Add event listener for the search button
  const searchLocationBtn = document.getElementById('searchLocationBtn');
  if (searchLocationBtn) {
    console.log('✅ Found search button, attaching event listener');

    // Remove any existing event listener to prevent duplicates
    searchLocationBtn.removeEventListener('click', editModalSearchLocations);

    // Add click event listener
    searchLocationBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔍 Search button clicked');
      editModalSearchLocations();
    });
  } else {
    console.warn('⚠️ Search button (searchLocationBtn) not found in edit modal');
  }

  // Add Enter key support for the location input
  locationInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔍 Enter key pressed in location input');
      editModalSearchLocations();
    }
  });

  // Map search functionality for edit modal
  async function editModalSearchMapLocations() {
    const mapInput = document.getElementById('location-map');
    const mapSuggestionBox = document.getElementById('mapSuggestions');

    if (!mapInput || !mapSuggestionBox) return;

    const query = mapInput.value.trim();
    if (query.length < 3) {
      hideEditModalMapDropdown();
      return;
    }

    try {
      console.log(`🗺️ Searching map locations for: "${query}"`);
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1`);
      const data = await response.json();

      mapSuggestionBox.innerHTML = '';

      if (data.length === 0) {
        hideEditModalMapDropdown();
        return;
      }

      data.forEach((place) => {
        const item = document.createElement('div');
        item.classList.add('list-group-item', 'list-group-item-action');
        item.textContent = place.display_name;
        item.style.cursor = 'pointer';

        item.onclick = async () => {
          console.log(`✅ Selected map location: ${place.display_name}`);

          const lat = parseFloat(place.lat);
          const lng = parseFloat(place.lon);

          // Update coordinate fields
          document.getElementById('latitude').value = lat;
          document.getElementById('longitude').value = lng;

          // Update map search field
          mapInput.value = place.display_name;

          hideEditModalMapDropdown();

          // Check for location name conflicts and handle accordingly
          const locationNameInput = document.getElementById('location');
          if (!locationNameInput.value.trim()) {
            // Auto-fill location name field if empty
            const parts = place.display_name.split(',');
            let cleanName = parts[0].trim();
            if (/^\d+$/.test(cleanName) && parts.length > 1) {
              cleanName = `${cleanName} ${parts[1].trim()}`;
            }
            locationNameInput.value = cleanName;
          } else {
            // Check if existing location name has different coordinates
            await editModalCheckLocationConflict(locationNameInput.value, lat, lng, place.display_name);
          }

          // Update map if available
          if (mymap && locationMarker) {
            mymap.setView([lat, lng], 13);
            locationMarker.setLatLng([lat, lng]);
          }
        };

        mapSuggestionBox.appendChild(item);
      });

      showEditModalMapDropdown();

    } catch (error) {
      console.error('❌ Error searching map locations:', error);
      hideEditModalMapDropdown();
    }
  }

  // Show/hide map dropdown functions for edit modal
  function showEditModalMapDropdown() {
    const mapSuggestionBox = document.getElementById('mapSuggestions');
    if (!mapSuggestionBox) return;

    console.log('🗺️ Showing edit modal map dropdown');
    mapSuggestionBox.classList.remove('d-none');
    mapSuggestionBox.classList.add('show');
    mapSuggestionBox.style.display = 'block';
    mapSuggestionBox.style.visibility = 'visible';
    mapSuggestionBox.style.opacity = '1';
  }

  function hideEditModalMapDropdown() {
    const mapSuggestionBox = document.getElementById('mapSuggestions');
    if (mapSuggestionBox) {
      console.log('🗺️ Hiding edit modal map dropdown');
      mapSuggestionBox.classList.add('d-none');
      mapSuggestionBox.classList.remove('show');
      mapSuggestionBox.style.display = 'none';
      mapSuggestionBox.style.visibility = 'hidden';
      mapSuggestionBox.style.opacity = '0';
    }
  }

  // Check for location conflicts and show resolution dialog
  async function editModalCheckLocationConflict(locationName, newLat, newLng, mapDisplayName) {
    try {
      console.log(`🔍 Checking conflict for: ${locationName}`);
      const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
      const data = await response.json();

      if (data.lat && data.lng) {
        const existingLat = parseFloat(data.lat);
        const existingLng = parseFloat(data.lng);

        // Calculate distance between coordinates (simple approximation)
        const distance = Math.sqrt(
          Math.pow(existingLat - newLat, 2) + Math.pow(existingLng - newLng, 2)
        ) * 111000; // Convert to meters (rough approximation)

        // If coordinates are significantly different (more than 1km apart)
        if (distance > 1000) {
          console.log(`⚠️ Location conflict detected! Distance: ${distance.toFixed(0)}m`);
          showEditModalLocationConflictDialog(locationName, existingLat, existingLng, newLat, newLng, mapDisplayName);
        } else {
          console.log(`✅ Coordinates are close enough (${distance.toFixed(0)}m apart)`);
        }
      }
    } catch (error) {
      console.error('❌ Error checking location conflict:', error);
    }
  }

  // Show location conflict resolution dialog for edit modal
  function showEditModalLocationConflictDialog(locationName, existingLat, existingLng, newLat, newLng, mapDisplayName) {
    // Calculate distance for display
    const distance = Math.sqrt(
      Math.pow(existingLat - newLat, 2) + Math.pow(existingLng - newLng, 2)
    ) * 111000; // Convert to meters

    const conflictHtml = `
      <div class="location-conflict-dialog">
        <div class="alert alert-warning mb-3">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <strong>Location Conflict Detected</strong>
        </div>

        <p class="mb-3">
          The location name "<strong>${locationName}</strong>" already exists in the database
          but points to a different location than what you selected on the map
          <span class="badge bg-warning text-dark">${distance > 1000 ? (distance/1000).toFixed(1) + ' km' : distance.toFixed(0) + ' m'} apart</span>
        </p>

        <div class="row mb-3">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header bg-primary text-white">
                <i class="bi bi-database me-2"></i>Existing Database Location
              </div>
              <div class="card-body">
                <p><strong>Name:</strong> ${locationName}</p>
                <p><strong>Location:</strong> <small class="text-muted">Lat: ${existingLat.toFixed(4)}, Lng: ${existingLng.toFixed(4)}</small></p>
                <div class="mt-2">
                  <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.showConflictLocation(${existingLat}, ${existingLng}, 'existing')">
                    <i class="bi bi-geo-alt me-1"></i>Focus on Map
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header bg-success text-white">
                <i class="bi bi-geo-alt me-2"></i>Selected Map Location
              </div>
              <div class="card-body">
                <p><strong>Name:</strong> ${mapDisplayName}</p>
                <p><strong>Location:</strong> <small class="text-muted">Lat: ${newLat.toFixed(4)}, Lng: ${newLng.toFixed(4)}</small></p>
                <div class="mt-2">
                  <button type="button" class="btn btn-sm btn-outline-success" onclick="window.showConflictLocation(${newLat}, ${newLng}, 'new')">
                    <i class="bi bi-geo-alt me-1"></i>Focus on Map
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Interactive Map showing both locations -->
        <div class="mb-3">
          <div class="card">
            <div class="card-header bg-light">
              <i class="bi bi-map me-2"></i>Location Comparison Map
              <small class="text-muted ms-2">Blue marker = Existing location, Green marker = Selected location</small>
            </div>
            <div class="card-body p-0">
              <div id="conflictMap" style="height: 300px; width: 100%;"></div>
            </div>
          </div>
        </div>

        <p class="mb-3"><strong>What would you like to do?</strong></p>

        <div class="d-grid gap-2">
          <button type="button" class="btn btn-primary" onclick="window.editModalUseExistingLocation('${locationName}', ${existingLat}, ${existingLng})">
            <i class="bi bi-database me-2"></i>Keep "${locationName}" with its existing location
          </button>
          <button type="button" class="btn btn-success" onclick="window.editModalCreateNewLocation('${mapDisplayName}', ${newLat}, ${newLng})">
            <i class="bi bi-plus-circle me-2"></i>Use new map location (will suggest new name)
          </button>
          <button type="button" class="btn btn-outline-secondary" onclick="window.editModalCloseConflictDialog()">
            <i class="bi bi-x-circle me-2"></i>Cancel (no changes)
          </button>
        </div>
      </div>
    `;

    // Store the current modal content to restore later
    const currentModalBody = document.querySelector('#commonModal .modal-body').innerHTML;
    const currentModalTitle = document.querySelector('#commonModal .modal-title').textContent;

    // Store for restoration
    window.previousEditModalContent = {
      title: currentModalTitle,
      body: currentModalBody
    };

    // Show conflict dialog in the same modal
    const modal = document.getElementById('commonModal');
    const modalTitle = modal.querySelector('.modal-title');
    const modalBody = modal.querySelector('.modal-body');

    modalTitle.textContent = 'Location Conflict';
    modalBody.innerHTML = conflictHtml;

    // Hide action button and close button for conflict dialog
    const actionBtn = document.getElementById('modalActionBtn');
    const closeBtn = modal.querySelector('.close-button');
    if (actionBtn) actionBtn.style.display = 'none';
    if (closeBtn) closeBtn.style.display = 'none';

    // Initialize the conflict map after a short delay to ensure DOM is ready
    setTimeout(() => {
      window.initializeConflictMap(existingLat, existingLng, newLat, newLng, locationName, mapDisplayName);
    }, 200);
  }

  // Handle conflict resolution choices for edit modal - make them globally accessible
  window.editModalUseExistingLocation = function(locationName, lat, lng) {
    console.log(`✅ Using existing location: ${locationName}`);

    // Restore original modal content first
    window.editModalRestoreOriginalContent();

    // Then update the form fields in the restored content
    setTimeout(() => {
      // Update location name field to the existing location name
      const locationNameInput = document.getElementById('location');
      if (locationNameInput) {
        locationNameInput.value = locationName;
        console.log(`📍 Location name field updated to: ${locationName}`);
      }

      // Update coordinates to existing location
      const latInput = document.getElementById('latitude');
      const lngInput = document.getElementById('longitude');
      if (latInput && lngInput) {
        latInput.value = lat;
        lngInput.value = lng;
        console.log(`📍 Coordinates updated to: ${lat}, ${lng}`);
      }

      // Update map search field
      const mapInput = document.getElementById('location-map');
      if (mapInput) {
        mapInput.value = locationName;
        console.log(`🗺️ Map search field updated to: ${locationName}`);
      }

      // Update map if available
      if (mymap && locationMarker) {
        mymap.setView([lat, lng], 13);
        locationMarker.setLatLng([lat, lng]);
        console.log(`🗺️ Map updated to show: ${lat}, ${lng}`);
      }

      // Re-initialize the modal functionality
      initializeEditModalLocationSearch();

      // Show success feedback
      if (locationNameInput) {
        locationNameInput.style.backgroundColor = '#d4edda';
        setTimeout(() => {
          locationNameInput.style.backgroundColor = '';
        }, 1500);
      }
    }, 100);
  };

  window.editModalCreateNewLocation = function(mapDisplayName, lat, lng) {
    console.log(`✅ Creating new location: ${mapDisplayName}`);

    // Extract clean name from map display name as suggestion
    const parts = mapDisplayName.split(',');
    let suggestedName = parts[0].trim();
    if (/^\d+$/.test(suggestedName) && parts.length > 1) {
      suggestedName = `${suggestedName} ${parts[1].trim()}`;
    }

    // Restore original modal content first
    window.editModalRestoreOriginalContent();

    // Then update the form fields in the restored content
    setTimeout(() => {
      // Update coordinates to new map location
      const latInput = document.getElementById('latitude');
      const lngInput = document.getElementById('longitude');
      if (latInput && lngInput) {
        latInput.value = lat;
        lngInput.value = lng;
        console.log(`📍 Coordinates updated to new location: ${lat}, ${lng}`);
      }

      // Update map search field
      const mapInput = document.getElementById('location-map');
      if (mapInput) {
        mapInput.value = mapDisplayName;
        console.log(`🗺️ Map search field updated to: ${mapDisplayName}`);
      }

      // Update map if available
      if (mymap && locationMarker) {
        mymap.setView([lat, lng], 13);
        locationMarker.setLatLng([lat, lng]);
        console.log(`🗺️ Map updated to show new location: ${lat}, ${lng}`);
      }

      // Auto-fill location name field with suggested name and focus it for editing
      const locationNameInput = document.getElementById('location');
      if (locationNameInput) {
        locationNameInput.value = suggestedName;
        locationNameInput.focus();
        locationNameInput.select(); // Select all text so user can easily replace if needed

        // Add visual emphasis to indicate this is a suggested name
        locationNameInput.style.backgroundColor = '#e7f3ff';
        locationNameInput.style.borderColor = '#0066cc';

        // Add helper text
        const helpText = document.createElement('div');
        helpText.className = 'form-text text-info mt-1';
        helpText.innerHTML = `<i class="bi bi-info-circle me-1"></i>Suggested name auto-filled. You can edit it to make it unique if needed.`;
        helpText.id = 'edit-location-help-text';

        // Remove existing help text if any
        const existingHelp = document.getElementById('edit-location-help-text');
        if (existingHelp) {
          existingHelp.remove();
        }

        // Add help text after the input
        locationNameInput.parentNode.insertBefore(helpText, locationNameInput.nextSibling);

        // Remove styling and help text when user starts typing
        locationNameInput.addEventListener('input', function removeEditHelpers() {
          this.style.backgroundColor = '';
          this.style.borderColor = '';

          const helpElement = document.getElementById('edit-location-help-text');
          if (helpElement) {
            helpElement.remove();
          }

          // Remove this event listener after first use
          this.removeEventListener('input', removeEditHelpers);
        });

        console.log(`📍 Location name field auto-filled with suggested name: ${suggestedName}`);
      }

      // Re-initialize the modal functionality
      initializeEditModalLocationSearch();
    }, 100);
  };

  window.editModalCloseConflictDialog = function() {
    console.log('🚫 Canceling edit conflict resolution');

    // Restore original modal content
    window.editModalRestoreOriginalContent();

    // Re-initialize the modal functionality
    setTimeout(() => {
      initializeEditModalLocationSearch();
    }, 100);
  };

  // Function to restore original edit modal content
  window.editModalRestoreOriginalContent = function() {
    if (window.previousEditModalContent) {
      // Clean up conflict map if it exists
      if (window.conflictMap) {
        console.log('🗺️ Cleaning up conflict map');
        window.conflictMap.remove();
        window.conflictMap = null;
        window.existingMarker = null;
        window.newMarker = null;
      }

      const modalElement = document.getElementById('commonModal');
      const modalTitle = modalElement.querySelector('.modal-title');
      const modalBody = modalElement.querySelector('.modal-body');
      const actionBtn = document.getElementById('modalActionBtn');
      const closeBtn = modalElement.querySelector('.close-button');

      // Restore content
      modalTitle.textContent = window.previousEditModalContent.title;
      modalBody.innerHTML = window.previousEditModalContent.body;

      // Restore buttons
      if (actionBtn) actionBtn.style.display = 'block';
      if (closeBtn) closeBtn.style.display = 'block';

      // Clear stored content
      window.previousEditModalContent = null;

      console.log('✅ Original edit modal content restored');
    }
  };

  // Attach event listeners for map search field
  const mapInput = document.getElementById('location-map');
  if (mapInput) {
    mapInput.addEventListener('input', editModalSearchMapLocations);
    mapInput.addEventListener('focus', function() {
      if (this.value.trim().length >= 3) {
        editModalSearchMapLocations();
      }
    });
  }

  // Click outside handler for edit modal
  document.addEventListener('click', function(event) {
    const suggestionBox = document.getElementById('locationSuggestions');
    const mapSuggestionBox = document.getElementById('mapSuggestions');

    // Handle location name dropdown
    if (locationInput && suggestionBox &&
        event.target !== locationInput &&
        !suggestionBox.contains(event.target)) {
      hideEditModalLocationDropdown();
    }

    // Handle map search dropdown
    if (mapInput && mapSuggestionBox &&
        event.target !== mapInput &&
        !mapSuggestionBox.contains(event.target)) {
      hideEditModalMapDropdown();
    }
  });

  console.log('✅ Edit modal location search functionality initialized');
}

// Initialize the map
function initMap() {
  const mapElement = document.getElementById('map');
  if (!mapElement) {
    console.error("Map element not found after modal load");
    return;
  }

  console.log("Map element found:", mapElement);

  // Default coordinates of Christchurch
  let initialLat = -43.5321
  let initialLng = 172.6362;

  const latInput = document.getElementById('latitude');
  const lngInput = document.getElementById('longitude');
  const locationNameInput = document.getElementById('location');

  if (latInput && lngInput && latInput.value && lngInput.value) {
    const parsedLat = parseFloat(latInput.value);
    const parsedLng = parseFloat(lngInput.value);

    if (!isNaN(parsedLat) && !isNaN(parsedLng)) {
      initialLat = parsedLat;
      initialLng = parsedLng;
      console.log(`Initializing map with event coords: ${initialLat}, ${initialLng}`);

      if (locationNameInput) {
        initialLocationName = locationNameInput.value;
      }
    } else {
      console.warn("Invalid initial latitude or longitude values:", latInput.value, lngInput.value);
    }
  } else {
    console.log("No existing event coordinates found, defaulting to Christchurch.");
  }

  if (typeof L === 'undefined') {
    console.error("Leaflet not loaded");
    return;
  }

  mymap = L.map('map').setView([initialLat, initialLng], 13);
  console.log(`Map initialized with view: [${initialLat}, ${initialLng}]`);

  L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
  }).addTo(mymap);

  // Create custom red icon
  redIcon = L.icon({
    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  });

  // Check if map should be interactive (disabled for content managers who are not journey owners)
  const locationMapInput = document.getElementById('location-map');
  const isMapDisabled = locationMapInput && locationMapInput.disabled;

  locationMarker = L.marker([initialLat, initialLng], {
    icon: redIcon,
    draggable: !isMapDisabled
  }).addTo(mymap);

  // Only add interactive events if map is not disabled
  if (!isMapDisabled) {
    // Update form when marker is dragged
    locationMarker.on('dragend', function (e) {
      const position = e.target.getLatLng();
      updateCoordinates(position.lat, position.lng);
      reverseGeocode(position.lat, position.lng);
    });

    // Update marker when map is clicked - use smart location picker if available
    mymap.on('click', function (e) {
      if (window.handleMapClick && typeof window.handleMapClick === 'function') {
        // Use smart location picker
        window.handleMapClick(e);
      } else {
        // Fallback to basic functionality
        locationMarker.setLatLng(e.latlng);
        updateCoordinates(e.latlng.lat, e.latlng.lng);
        reverseGeocode(e.latlng.lat, e.latlng.lng);
      }
    });
  }

  setupMapSearch();
  invalidateMapSize();
}

// Update hidden coordinates fields
function updateCoordinates(lat, lng) {
  const latInput = document.getElementById('latitude');
  const lngInput = document.getElementById('longitude');

  if (latInput && lngInput) {
    latInput.value = lat;
    lngInput.value = lng;
  }
}

// Reverse geocode coordinates to address
async function reverseGeocode(lat, lng) {
  try {
    const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
    const data = await response.json();

    const locationMapInput = document.getElementById('location-map');
    if (locationMapInput) {
      locationMapInput.value = data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  } catch (error) {
    console.error('Error reverse geocoding:', error);
  }
}

// Geocode address to coordinates - use smart geocoding if available
async function geocode(address) {
  if (window.smartGeocode && typeof window.smartGeocode === 'function') {
    // Use smart geocoding with conflict resolution
    window.smartGeocode(address);
  } else {
    // Fallback to basic geocoding
    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`);
      const data = await response.json();

      if (data && data.length > 0) {
        const lat = parseFloat(data[0].lat);
        const lng = parseFloat(data[0].lon);

        mymap.setView([lat, lng], 13);
        locationMarker.setLatLng([lat, lng]);

        updateCoordinates(lat, lng);
      }
    } catch (error) {
      console.error('Error geocoding:', error);
    }
  }
}

// Handle map location search
function setupMapSearch() {
  const locationMapInput = document.getElementById('location-map');
  const mapSuggestions = document.getElementById('mapSuggestions');

  if (!locationMapInput || !mapSuggestions) return;

  // Don't set up search if input is disabled (for content managers)
  if (locationMapInput.disabled) return;

  let typingTimer;
  locationMapInput.addEventListener('input', function () {
    clearTimeout(typingTimer);
    const query = this.value.trim();

    if (query.length < 3) {
      mapSuggestions.classList.add('d-none');
      return;
    }

    typingTimer = setTimeout(function () {
      searchMapLocations(query);
    }, 500);
  });

  document.addEventListener('click', function (e) {
    if (!mapSuggestions.contains(e.target) && e.target !== locationMapInput) {
      mapSuggestions.classList.add('d-none');
    }
  });
}

// Search locations for the map input - use smart search if available
async function searchMapLocations(query) {
  if (window.handleLocationSearch && typeof window.handleLocationSearch === 'function') {
    // Use smart location search with conflict resolution
    window.handleLocationSearch(query);
  } else {
    // Fallback to basic search
    const mapSuggestions = document.getElementById('mapSuggestions');

    if (!mapSuggestions) return;

    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`);
      const data = await response.json();

      mapSuggestions.innerHTML = '';

      if (data.length === 0) {
        mapSuggestions.classList.add('d-none');
        return;
      }

      data.slice(0, 5).forEach(place => {
        const item = document.createElement('a');
        item.classList.add('list-group-item', 'list-group-item-action');
        item.textContent = place.display_name;
        item.href = '#';
        item.addEventListener('click', function (e) {
          e.preventDefault();
          selectMapLocation(place.display_name, place.lat, place.lon);
        });

        mapSuggestions.appendChild(item);
      });

      mapSuggestions.classList.remove('d-none');
    } catch (error) {
      console.error('Error searching locations:', error);
      mapSuggestions.classList.add('d-none');
    }
  }
}

// Select a location for the map
function selectMapLocation(placeName, lat, lng) {
  const locationMapInput = document.getElementById('location-map');
  const mapSuggestions = document.getElementById('mapSuggestions');

  if (locationMapInput) {
    locationMapInput.value = placeName;
  }

  if (mapSuggestions) {
    mapSuggestions.classList.add('d-none');
  }

  if (mymap && locationMarker) {
    lat = parseFloat(lat);
    lng = parseFloat(lng);

    mymap.setView([lat, lng], 13);
    locationMarker.setLatLng([lat, lng]);

    updateCoordinates(lat, lng);
  }
}

// Invalidate map size
function invalidateMapSize() {
  if (!mymap) return;

  if (typeof bootstrap !== 'undefined') {
    const modalElement = document.querySelector('.modal');
    if (modalElement) {
      modalElement.addEventListener('shown.bs.modal', function () {
        mymap.invalidateSize();
        console.log('Map size invalidated after modal shown event');
      });
    }
  }
}

// Location search functionality
async function searchLocations() {
  let query = document.getElementById('location').value.trim();
  let suggestionBox = document.getElementById('locationSuggestions');

  try {
    let response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
    let data = await response.json();
    suggestionBox.innerHTML = '';

    if (data.length === 0) {
      suggestionBox.classList.add('d-none');
      return;
    }

    data.forEach((location) => {
      let item = document.createElement('div');
      item.classList.add('list-group-item', 'list-group-item-action');
      item.textContent = location.name;

      item.onclick = async () => {
        document.getElementById('location').value = location.name;
        suggestionBox.innerHTML = '';
        suggestionBox.classList.add('d-none');

        console.log(`Selected location: ${location.name}`);
        try {
          const coordsRes = await fetch(`/api/location-coords?name=${encodeURIComponent(location.name)}`);
          const coords = await coordsRes.json();
          console.log("Coords response:", coords);

          if (coords.lat && coords.lng) {
            document.getElementById('latitude').value = coords.lat;
            document.getElementById('longitude').value = coords.lng;

            if (typeof mymap !== 'undefined' && typeof locationMarker !== 'undefined') {
              mymap.setView([coords.lat, coords.lng], 13);
              locationMarker.setLatLng([coords.lat, coords.lng]);

              if (typeof reverseGeocode === 'function') {
                reverseGeocode(coords.lat, coords.lng);
              }
            } else {
              console.warn("mymap or locationMarker is undefined.");
            }
          } else {
            console.warn("No lat/lng returned from API.");
          }
        } catch (coordError) {
          console.error("Error fetching coordinates:", coordError);
        }
      };

      suggestionBox.appendChild(item);
    });

    suggestionBox.classList.remove('d-none');
  } catch (err) {
    console.error("Error during location search:", err);
  }
}

// Enhanced image management system from original
function showEventImageManager(eventId, isPremiumAccess, uploadUrl, plansUrl) {
  const ALLOWED_EXTENSIONS = ['png', 'jpg', 'jpeg'];
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

  fetch(`/event/${eventId}/images-data`)
    .then(response => response.json())
    .then(data => {
      let imagesHtml = '';
      if (data.images && data.images.length > 0) {
        imagesHtml = `
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="btn-group">
              <button type="button" id="selectAllImages" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-check-square me-1"></i>Select All
              </button>
              <button type="button" id="deselectAllImages" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-square me-1"></i>Deselect All
              </button>
            </div>
            <button type="button" id="deleteSelectedImages" class="btn btn-sm btn-danger" disabled>
              <i class="bi bi-trash me-1"></i>Delete Selected
            </button>
            <span class="text-muted" id="selectedCount">0 selected</span>
          </div>
          <div class="row g-3">
            ${data.images.map((image, index) =>
              `<div class="col-md-4 col-6" id="image-item-${image.id}">
                <div class="card h-100 position-relative">
                  <div class="position-relative">
                    <div class="form-check position-absolute top-0 start-0 m-2">
                      <input class="form-check-input image-checkbox" type="checkbox" value="${image.id}"
                        id="image-check-${image.id}">
                      <label class="form-check-label" for="image-check-${image.id}"></label>
                    </div>
                    <img src="${image.url}" alt="Event image" class="card-img-top" style="height: 160px; object-fit: cover;">
                    ${image.is_primary ? '<div class="position-absolute top-0 end-0 m-2"><span class="badge bg-success">Primary</span></div>' : ''}
                  </div>
                </div>
              </div>`
            ).join('')}
          </div>
        `;
      } else {
        imagesHtml = `
          <div class="text-center p-4">
            <i class="bi bi-images fs-1 text-secondary"></i>
            <p class="text-muted mt-2">No images added yet</p>
          </div>
        `;
      }

      const premiumMessage = !isPremiumAccess && data.images.length >= 1 ?
        `<div class="alert alert-info" role="alert">
          <i class="bi bi-info-circle me-2"></i> Free users can upload only one image.
          <a href="/account/profile?active_tab=subscription" class="alert-link">Upgrade to Premium</a> to add more images.
        </div>` : '';

      const uploadDisabled = !isPremiumAccess && data.images.length >= 1;

      const formHtml = `
        <div class="image-manager">
          ${premiumMessage}
          <div class="current-images mb-4">
            <h5 class="border-bottom pb-2 mb-3">Current Images</h5>
            ${imagesHtml}
          </div>
          <div class="upload-section">
            <h5 class="border-bottom pb-2 mb-3">Add New Images</h5>
            <form id="uploadEventImageForm" action="${uploadUrl}" method="post" enctype="multipart/form-data">
              <div class="mb-3">
                <input type="file" class="form-control" id="eventImages" name="images[]" accept=".png,.jpg,.jpeg,.gif"
                  multiple ${uploadDisabled ? 'disabled' : ''}>
                <div class="form-text">
                  Maximum file size: 5MB per image. Allowed formats: PNG, JPG, JPEG, GIF.
                  ${uploadDisabled ? ' Free users can only upload one image.' : ''}
                </div>
                <div class="invalid-feedback" id="imageValidationFeedback">
                  Please select at least one valid image to upload.
                </div>
              </div>
              <div id="imagePreviewContainer" class="row g-2 mb-3">
              </div>
            </form>
          </div>
        </div>
      `;

      showModal('Manage Event Images', formHtml, {
        actionText: uploadDisabled ? 'Close' : 'Upload Images',
        size: 'large',
        onAction: function() {
          if (uploadDisabled) {
            return true;
          }

          const form = document.getElementById('uploadEventImageForm');
          const fileInput = document.getElementById('eventImages');

          if (!fileInput.files.length) {
            fileInput.classList.add('is-invalid');
            document.getElementById('imageValidationFeedback').textContent = 'Please select at least one image to upload.';
            return false;
          }

          // Validate files
          for (let file of fileInput.files) {
            if (file.size > MAX_FILE_SIZE) {
              fileInput.classList.add('is-invalid');
              document.getElementById('imageValidationFeedback').textContent = `File "${file.name}" exceeds 5MB limit.`;
              return false;
            }
          }

          form.submit();
          return true;
        }
      });

      // Setup event handlers for file input
      const eventImagesInput = document.getElementById('eventImages');
      if (eventImagesInput) {
        eventImagesInput.addEventListener('change', function() {
          this.classList.remove('is-invalid');

          validateAndPreviewUploadFiles(this);
        });
      }

      setupMultiImageSelect();
    })
    .catch(error => {
      console.error('Error loading image data:', error);
      alert('Error loading image management. Please try again.');
    });
}

function setupMultiImageSelect() {
  // Get references to buttons and checkboxes
  const selectAllBtn = document.getElementById('selectAllImages');
  const deselectAllBtn = document.getElementById('deselectAllImages');
  const deleteSelectedBtn = document.getElementById('deleteSelectedImages');
  const checkboxes = document.querySelectorAll('.image-checkbox');
  const selectedCountEl = document.getElementById('selectedCount');

  if (!checkboxes.length) return;

  // Function to update the selected count and delete button state
  function updateSelectionState() {
    const selectedCount = document.querySelectorAll('.image-checkbox:checked').length;
    selectedCountEl.textContent = `${selectedCount} selected`;
    deleteSelectedBtn.disabled = selectedCount === 0;
  }

  // Add change event listeners to all checkboxes
  checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectionState);

    // Make the entire card clickable to toggle checkbox
    const card = checkbox.closest('.card');
    if (card) {
      card.addEventListener('click', function (e) {
        // Don't toggle if the checkbox itself was clicked (it will toggle itself)
        if (e.target !== checkbox) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event('change'));
        }
      });
    }
  });

  // Select all images
  selectAllBtn?.addEventListener('click', function () {
    checkboxes.forEach(checkbox => {
      checkbox.checked = true;
    });
    updateSelectionState();
  });

  // Deselect all images
  deselectAllBtn?.addEventListener('click', function () {
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
    updateSelectionState();
  });

  // Delete selected images
  deleteSelectedBtn?.addEventListener('click', function () {
    const selectedImageIds = Array.from(document.querySelectorAll('.image-checkbox:checked'))
      .map(checkbox => checkbox.value);

    if (selectedImageIds.length === 0) return;

    deleteMultipleImages(selectedImageIds);
  });
}

// Function to delete a single image
function deleteSingleImage(imageId) {
  showModal('Delete Image', 'Are you sure you want to delete this image? This action cannot be undone.', {
    actionText: 'Delete',
    onAction: function() {
      performImageDelete([imageId]);
    }
  });
}

// Function to delete multiple images
function deleteMultipleImages(imageIds) {
  if (!imageIds || imageIds.length === 0) return;

  const numImages = imageIds.length;
  const message = `Are you sure you want to delete ${numImages} image${numImages > 1 ? 's' : ''}? This action cannot be undone.<br><br>
<div class="alert alert-info">
  <small><i class="bi bi-info-circle me-1"></i> Each image will be deleted individually.
    This may take a moment for multiple images.</small>
</div>`;

  showModal('Delete Images', message, {
    actionText: 'Delete',
    onAction: function() {
      console.log(`Initiating deletion of ${numImages} images`);
      performImageDelete(imageIds);
      return true;
    }
  });
}

// Function to handle the actual deletion API call
function performImageDelete(imageIds) {
  if (!imageIds || imageIds.length === 0) {
    console.error('No image IDs provided for deletion');
    return;
  }

  console.log('Attempting to delete images with IDs:', imageIds);

  // Show loading state for all images
  imageIds.forEach(id => {
    const imageItem = document.getElementById(`image-item-${id}`);
    if (imageItem) {
      imageItem.classList.add('opacity-50');
    }
  });

  // Display a single progress message that we'll update
  const progressToastId = 'delete-progress-' + Date.now();
  showFlashMessage(`Deleting ${imageIds.length > 1 ? 'images' : 'image'}...`, "info", progressToastId);

  // Track progress
  let successCount = 0;
  let failCount = 0;
  let completedRequests = 0;
  let errors = [];

  // Process each image deletion sequentially
  const deleteNextImage = (index) => {
    if (index >= imageIds.length) {
      // All images have been processed
      console.log(`Completed all image deletions. Success: ${successCount}, Failed: ${failCount}`);

      if (failCount === 0) {
        // All deletions were successful
        const message = imageIds.length > 1
          ? `${successCount} images deleted successfully!`
          : "Image deleted successfully!";

        // Store the success message for display after page reload
        storeFlashMessage(message, "success");

        // Reload the page immediately (no need for delay since we're not showing immediate feedback)
        window.location.reload();
      } else if (successCount === 0) {
        // All deletions failed - store message for after reload
        storeFlashMessage(`Failed to delete images. Errors occurred.`, "danger");
        window.location.reload();
      } else {
        // Mixed results - store message for after reload
        const mixedMessage = `Deleted ${successCount} images. Failed to delete ${failCount} images.`;
        storeFlashMessage(mixedMessage, "warning");
        window.location.reload();
      }
      return;
    }

    const imageId = imageIds[index];
    // Get event ID from the container
    const container = document.querySelector('.event-content');
    const eventId = container ? container.getAttribute('data-event-id') : null;

    if (!eventId) {
      console.error('Event ID not found');
      failCount++;
      errors.push(`Event ID not found for image ${imageId}`);
      deleteNextImage(index + 1);
      return;
    }

    const url = `/event/${eventId}/image/${imageId}/delete`;
    console.log(`Deleting image ${index + 1}/${imageIds.length}, ID: ${imageId}`);

    // Update the progress message only for multiple images
    if (imageIds.length > 1) {
      updateFlashMessage(progressToastId, `Deleting images... (${index + 1}/${imageIds.length})`, "info");
    }

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'same-origin'
    })
      .then(response => {
        if (!response.ok) {
          return response.text().then(text => {
            throw new Error(`Failed to delete image ${imageId}: ${response.status} ${response.statusText}`);
          });
        }
        return response.json();
      })
      .then(data => {
        completedRequests++;

        if (data.success) {
          successCount++;
          // Remove the image from the DOM
          const imageElement = document.getElementById(`image-item-${imageId}`);
          if (imageElement) {
            imageElement.remove();
          }
        } else {
          failCount++;
          errors.push(`Image ${imageId}: ${data.message || "Unknown error"}`);

          // Remove loading state for this image
          const imageItem = document.getElementById(`image-item-${imageId}`);
          if (imageItem) {
            imageItem.classList.remove('opacity-50');
          }
        }

        // Process the next image
        deleteNextImage(index + 1);
      })
      .catch(error => {
        console.error(`Error deleting image ${imageId}:`, error);
        completedRequests++;
        failCount++;
        errors.push(error.message);

        // Remove loading state for this image
        const imageItem = document.getElementById(`image-item-${imageId}`);
        if (imageItem) {
          imageItem.classList.remove('opacity-50');
        }

        // Process the next image
        deleteNextImage(index + 1);
      });
  };

  // Start the deletion process with the first image
  deleteNextImage(0);
}

// Helper function to update an existing flash message
function updateFlashMessage(toastId, message, type = "info") {
  const toast = document.getElementById(toastId);
  if (toast) {
    const toastBody = toast.querySelector('.toast-body');
    if (toastBody) {
      // Update the message
      toastBody.textContent = message;

      // Update the toast type/color
      toast.className = toast.className.replace(/bg-\w+/, `bg-${type}`);
    }
  } else {
    // If the toast doesn't exist, create a new one
    showFlashMessage(message, type, toastId);
  }
}

// Function to delete a single event image (for individual delete buttons)
function deleteEventImage(imageId) {
  showModal('Delete Image', 'Are you sure you want to delete this image? This action cannot be undone.', {
    actionText: 'Delete',
    onAction: function() {
      // Get event ID from the container
      const container = document.querySelector('.event-content');
      const eventId = container ? container.getAttribute('data-event-id') : null;

      if (!eventId) {
        console.error('Event ID not found');
        storeFlashMessage('Error: Event ID not found', 'danger');
        window.location.reload();
        return;
      }

      fetch(`/event/${eventId}/image/${imageId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Store success message for display after reload (no immediate toast)
            storeFlashMessage(data.message || "Image deleted successfully!", "success");
            // Reload immediately without showing immediate feedback
            window.location.reload();
          } else {
            // Store error message before reload
            storeFlashMessage("Failed to delete image: " + (data.message || "Unknown error"), "danger");
            window.location.reload();
          }
        })
        .catch(error => {
          console.error('Error deleting image:', error);
          storeFlashMessage("Error deleting image: " + error.message, "danger");
          window.location.reload();
        });
    }
  });
}

function showEventImageUploader(eventId, isPremiumAccess, uploadUrl) {
  const freeUserMessage = isPremiumAccess ? '' : 'Free users can upload only one image.';

  console.log('Showing event image uploader modal, upload URL:', uploadUrl);

  if (!uploadUrl || uploadUrl.trim() === '') {
    console.error('Upload URL is missing or empty');
    uploadUrl = `/event/${eventId}/upload-images`;
    console.log('Using fallback upload URL:', uploadUrl);
  }

  const formHtml = `
<form id="uploadEventImageForm" action="${uploadUrl}" method="post" enctype="multipart/form-data">
  <div class="mb-3">
    <label for="eventImages" class="modern-label">
      <i class="bi bi-images"></i>
      Select images to upload
    </label>
    <input type="file" class="form-control" id="eventImages" name="images[]" accept=".png,.jpg,.jpeg,.gif" multiple required
      onchange="validateAndPreviewUploadFiles(this)">
    <div class="invalid-feedback" id="imageValidationFeedback"></div>
    <div class="form-text">
      Maximum file size: 5MB per image.
      ${freeUserMessage}
    </div>
  </div>
  <div id="imagePreviewContainer" class="row g-2 mb-3">
  </div>
</form>
`;

  const modalResult = showModal('Add Event Images', formHtml, {
    actionText: 'Upload',
    onAction: function () {
      console.log('Upload button clicked');

      const form = document.getElementById('uploadEventImageForm');
      const fileInput = document.getElementById('eventImages');

      console.log('Form action:', form.action);
      console.log('Form method:', form.method);
      console.log('Files selected:', fileInput.files.length);

      if (!fileInput.files.length) {
        fileInput.classList.add('is-invalid');
        document.getElementById('imageValidationFeedback').textContent = 'Please select at least one file.';
        console.log('Validation failed: No files selected');
        return false;
      }

      const validationResult = validateFiles(fileInput);
      console.log('Validation result:', validationResult);

      if (!validationResult.valid) {
        fileInput.classList.add('is-invalid');
        document.getElementById('imageValidationFeedback').textContent = validationResult.message;
        console.log('Validation failed:', validationResult.message);
        return false;
      }

      console.log('Form validation passed');

      try {

        form.setAttribute('method', 'post');
        form.setAttribute('enctype', 'multipart/form-data');

        if (!form.action || form.action === '') {
          form.setAttribute('action', uploadUrl);
        }

        console.log('Submitting form to:', form.action);
        console.log('Form method:', form.method);
        console.log('Form enctype:', form.enctype);

        const uploadMsgId = 'upload-progress-' + Date.now();
        showFlashMessage('Uploading images, please wait...', 'info', uploadMsgId);

        const tempForm = document.createElement('form');
        tempForm.method = 'post';
        tempForm.action = form.action;
        tempForm.enctype = 'multipart/form-data';

        const tempFileInput = fileInput.cloneNode(true);
        tempForm.appendChild(tempFileInput);

        document.body.appendChild(tempForm);

        console.log('Submitting form via direct submit');
        tempForm.submit();

        console.log('Form submitted');

        return true;
      } catch (error) {
        console.error('Error submitting form:', error);
        showFlashMessage('Error uploading image: ' + error.message, 'danger');
        return false;
      }
    }
  });
}

function validateAndPreviewUploadFiles(input) {
  const ALLOWED_EXTENSIONS = ['png', 'jpg', 'jpeg'];
  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const container = document.querySelector('.event-content');
  const isPremiumAccess = container ? (container.getAttribute('data-premium-access') === 'true') : false;

  const previewContainer = document.getElementById('imagePreviewContainer');
  const feedback = document.getElementById('imageValidationFeedback');

  if (!previewContainer) return;

  previewContainer.innerHTML = '';
  input.classList.remove('is-invalid');

  if (!input.files || input.files.length === 0) {
    return;
  }

  const validationResult = validateFiles(input);
  if (!validationResult.valid) {
    input.classList.add('is-invalid');
    feedback.textContent = validationResult.message;
    return;
  }

  const maxFiles = isPremiumAccess ? 10 : 1;
  const filesToProcess = Math.min(input.files.length, maxFiles);

  for (let i = 0; i < filesToProcess; i++) {
    const file = input.files[i];
    const reader = new FileReader();
    reader.onload = function (e) {
      const preview = document.createElement('div');
      preview.className = 'col-md-4 col-6';
      preview.innerHTML = `
        <div class="card h-100">
          <div class="ratio ratio-4x3">
            <img src="${e.target.result}" class="card-img-top" alt="Preview" style="object-fit: cover;">
          </div>
          <div class="card-body p-2">
            <p class="small text-truncate mb-0">${file.name}</p>
            <small class="text-muted">${(file.size / (1024 * 1024)).toFixed(2)} MB</small>
          </div>
        </div>
      `;
      previewContainer.appendChild(preview);
    };

    reader.readAsDataURL(file);
  }
}

function validateFiles(input) {
  // Get the isPremiumAccess value from the data attribute since it's not in scope
  const container = document.querySelector('.event-content');
  const isPremiumAccess = container ? (container.getAttribute('data-premium-access') === 'true') : false;
  const maxFiles = isPremiumAccess ? 10 : 1;

  // Check if any files were selected
  if (!input.files || input.files.length === 0) {
    return { valid: false, message: 'Please select at least one file.' };
  }

  // Check premium access constraint
  if (!isPremiumAccess && input.files.length > 1) {
    return { valid: false, message: 'Free users can upload only one image.' };
  }

  // Use centralized file validation if available
  if (window.FileValidation) {
    const validation = window.FileValidation.validateFiles(input.files, maxFiles);
    return validation;
  } else {
    // Fallback validation if FileValidation is not loaded
    console.warn('FileValidation utility not available, using fallback validation');

    // Fallback constants
    const ALLOWED_EXTENSIONS = ['png', 'jpg', 'jpeg', 'gif'];
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

    // Check each file
    for (let i = 0; i < input.files.length; i++) {
      const file = input.files[i];

      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        return { valid: false, message: `File "${file.name}" exceeds the maximum size of 5MB.` };
      }

      // Check file extension
      const fileExt = file.name.split('.').pop().toLowerCase();
      if (!ALLOWED_EXTENSIONS.includes(fileExt)) {
        return { valid: false, message: `File "${file.name}" has an invalid extension. Allowed: PNG, JPG, JPEG, GIF.` };
      }
    }

    return { valid: true, message: '' };
  }
}

// Image gallery initialization function from original
function initGallery(modalElement) {
  const mainImage = document.getElementById('mainImage');
  const prevBtn = document.getElementById('prevImage');
  const nextBtn = document.getElementById('nextImage');
  const thumbnails = document.querySelectorAll('.thumbnail');
  const dots = document.querySelectorAll('.dot');

  if (!mainImage) {
    console.warn('Gallery not properly initialized - missing main image element');
    return;
  }

  let currentIndex = 0;

  const images = [];
  thumbnails.forEach(thumb => {
    images.push({
      src: thumb.querySelector('img').src,
      alt: thumb.querySelector('img').alt
    });
  });

  if (images.length === 0) {
    console.warn('No images found in gallery');
    return;
  }

  function handleThumbnailClick() {
    currentIndex = parseInt(this.getAttribute('data-index'));
    updateGallery();
  }

  function handleDotClick() {
    currentIndex = parseInt(this.getAttribute('data-index'));
    updateGallery();
  }

  function handlePrevClick() {
    currentIndex = (currentIndex - 1 + images.length) % images.length;
    updateGallery();
  }

  function handleNextClick() {
    currentIndex = (currentIndex + 1) % images.length;
    updateGallery();
  }

  function handleKeydown(e) {
    if (e.key === 'ArrowLeft') {
      currentIndex = (currentIndex - 1 + images.length) % images.length;
      updateGallery();
    } else if (e.key === 'ArrowRight') {
      currentIndex = (currentIndex + 1) % images.length;
      updateGallery();
    }
  }

  thumbnails.forEach(thumb => {
    thumb.addEventListener('click', handleThumbnailClick);
  });

  dots.forEach(dot => {
    dot.addEventListener('click', handleDotClick);
  });

  if (prevBtn) {
    prevBtn.addEventListener('click', handlePrevClick);
  }

  if (nextBtn) {
    nextBtn.addEventListener('click', handleNextClick);
  }

  document.addEventListener('keydown', handleKeydown);

  function cleanupGallery() {
    thumbnails.forEach(thumb => {
      thumb.removeEventListener('click', handleThumbnailClick);
    });

    dots.forEach(dot => {
      dot.removeEventListener('click', handleDotClick);
    });

    if (prevBtn) {
      prevBtn.removeEventListener('click', handlePrevClick);
    }

    if (nextBtn) {
      nextBtn.removeEventListener('click', handleNextClick);
    }

    document.removeEventListener('keydown', handleKeydown);

    if (modalElement) {
      modalElement.removeEventListener('hidden.bs.modal', cleanupGallery);
    }

    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.remove();
    }

    // Clean up modal classes for image gallery
    const modalDialog = document.querySelector('#commonModal .modal-dialog');
    if (modalDialog) {
      modalDialog.classList.remove('modal-lg', 'modal-dialog-image-gallery');
    }

    document.body.classList.remove('modal-open');
    document.body.style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');
  }

  function updateGallery() {
    if (mainImage && images.length > 0) {
      mainImage.src = images[currentIndex].src;
      mainImage.alt = images[currentIndex].alt;

      thumbnails.forEach((thumb) => {
        const thumbIndex = parseInt(thumb.getAttribute('data-index'));
        thumb.classList.toggle('active', thumbIndex === currentIndex);
      });

      dots.forEach((dot) => {
        const dotIndex = parseInt(dot.getAttribute('data-index'));
        dot.classList.toggle('active', dotIndex === currentIndex);
      });
    }
  }

  updateGallery();

  if (modalElement) {
    modalElement.addEventListener('hidden.bs.modal', cleanupGallery);
  }

  return cleanupGallery;
}
</script>
{% endblock %}
