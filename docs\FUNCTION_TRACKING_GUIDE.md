# Function Usage Tracking Guide

This guide explains how to use the function tracking tools to identify unused JavaScript functions and code in your event templates (`detail.html` and `edit.html`).

## 🎯 Purpose

The function tracking system helps you:
- **Identify unused JavaScript functions** in your templates
- **Track real user interactions** to see what code is actually used
- **Generate cleanup recommendations** for removing dead code
- **Optimize template performance** by removing unused scripts

## 🚀 Quick Start

### Method 1: Automated Testing (Recommended)

1. **Ensure Flask app is running:**
   ```bash
   python app.py
   ```

2. **Run the automated tracking:**
   ```bash
   python tests/frontend/run_function_tracking.py
   ```

3. **Check results:**
   - Reports saved in `test_reports/` folder
   - Look for `cleanup_recommendations_*.json`

### Method 2: Manual Browser Testing

1. **Start your Flask app**

2. **Visit the debug interface:**
   ```
   http://127.0.0.1:5000/debug/function-tracker
   ```

3. **Follow the on-screen instructions:**
   - Click "Start Tracking"
   - Navigate to event pages
   - Interact with buttons, forms, modals
   - Return and click "Stop & Report"
   - Download the report

### Method 3: Live Page Testing

1. **Add debug parameter to any page:**
   ```
   http://127.0.0.1:5000/journey/private/1?debug=true
   ```

2. **Use the floating tracker controls** that appear on the page

## 📊 Understanding Reports

### Report Structure

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "summary": {
    "total_functions_tracked": 25,
    "functions_used": 8,
    "functions_unused": 17,
    "usage_percentage": 32.0
  },
  "functions_used": ["smartBack", "likeEvent", "showModal"],
  "functions_unused": ["deleteEvent", "confirmDelete", "uploadImage"],
  "cleanup_actions": {
    "safe_to_remove": ["unusedFunction1", "unusedFunction2"],
    "files_to_review": ["templates/event/detail.html"]
  }
}
```

### Key Metrics

- **Usage Percentage**: How much of your JavaScript is actually used
- **Functions Used**: List of functions called during testing
- **Functions Unused**: Potential candidates for removal
- **Safe to Remove**: Functions that weren't called in any test scenario

## 🧹 Cleanup Process

### Step 1: Review Unused Functions

1. **Open the cleanup recommendations report**
2. **Check each unused function** in the list
3. **Search for the function** in your templates:
   ```bash
   grep -n "function unusedFunction" templates/event/detail.html
   ```

### Step 2: Verify Function Usage

Before removing, check if the function might be used in scenarios not covered by tests:

- **Edge cases** (error conditions, admin functions)
- **Conditional code** (staff-only features, premium features)
- **Event handlers** that might not trigger during automated tests

### Step 3: Safe Removal

1. **Comment out the function** first (don't delete immediately)
2. **Test your application** thoroughly
3. **Run the tracking again** to confirm it's not needed
4. **Delete the commented code** if everything works

### Step 4: Verify Cleanup

1. **Run tracking tests again**
2. **Check the new usage percentage**
3. **Ensure no functionality is broken**

## 🔧 Advanced Usage

### Custom Function Tracking

Add your own functions to track by modifying `static/js/function_tracker.js`:

```javascript
const FUNCTIONS_TO_TRACK = [
    // Add your custom functions here
    'myCustomFunction',
    'anotherFunction'
];
```

### API Endpoints

The system provides several API endpoints for integration:

- `GET /debug/reports` - List all tracking reports
- `GET /debug/report/<filename>` - Get specific report
- `GET /debug/cleanup-recommendations` - Get cleanup suggestions
- `GET /debug/extract-functions` - Extract functions from templates

### Playwright Integration

The system uses Playwright for automated testing. You can extend the tests in `tests/frontend/test_function_usage_tracking.py` to cover more user scenarios.

## 📁 File Structure

```
tests/frontend/
├── test_function_usage_tracking.py    # Main tracking tests
├── extract_js_functions.py            # Function extraction utility
└── run_function_tracking.py           # Test runner script

static/js/
└── function_tracker.js                # Client-side tracking script

templates/debug/
└── function_tracker.html              # Debug interface

routes/
└── debug_routes.py                     # Debug API routes

test_reports/                           # Generated reports
├── function_usage_report_*.json
├── function_analysis_*.json
└── cleanup_recommendations_*.json
```

## ⚠️ Important Notes

### What Gets Tracked

- **Function declarations** (`function myFunc()`)
- **Function calls** (`myFunc()`)
- **Event handlers** (`onclick="myFunc()"`)
- **jQuery methods** (`$().click()`)
- **Element interactions** (clicks, form submissions)

### What Doesn't Get Tracked

- **Inline anonymous functions**
- **Functions called only in error conditions**
- **Functions triggered by external events** (timers, websockets)
- **Dynamically generated function calls**

### Best Practices

1. **Test multiple user scenarios** before removing code
2. **Keep backups** of your templates before cleanup
3. **Test with different user roles** (admin, staff, regular users)
4. **Check mobile and desktop interactions**
5. **Verify edge cases** and error conditions

## 🐛 Troubleshooting

### Common Issues

**"No functions tracked"**
- Ensure JavaScript is enabled
- Check browser console for errors
- Verify function_tracker.js is loaded

**"Flask app not running"**
- Start your Flask app: `python app.py`
- Check the URL: `http://127.0.0.1:5000`

**"Playwright tests fail"**
- Install Playwright: `pip install playwright`
- Install browsers: `playwright install`

**"Reports not generated"**
- Check `test_reports/` folder exists
- Verify write permissions
- Check for Python errors in console

### Getting Help

1. **Check the debug interface**: `/debug/function-tracker`
2. **Review API endpoints**: `/debug/help`
3. **Check browser console** for JavaScript errors
4. **Verify Flask logs** for server-side issues

## 🎉 Expected Results

After using this system, you should be able to:

- **Reduce template size** by 20-40% by removing unused code
- **Improve page load times** with less JavaScript to parse
- **Simplify maintenance** with cleaner, more focused code
- **Identify dead code** that can be safely removed

The goal is to focus on identifying unused functions and scripts in `templates/event/detail.html` and `templates/event/edit.html` specifically, helping you clean up and optimize these important templates.
