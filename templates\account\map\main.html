<div class="location-container">
  <div id="map" style="height: 600px; width: 100%; border-radius: 8px;"></div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const unsortedLocations = {{ account['events'] | tojson | safe
  }};

  if (!unsortedLocations || unsortedLocations.length === 0) {
    console.warn("No valid locations provided.");
    return;
  }

  if (typeof L === 'undefined') {
    console.error("Leaflet library (L) is not loaded!");
    return;
  }

  const locations = unsortedLocations.sort((a, b) => {
    return new Date(a.event_start_datetime) - new Date(b.event_start_datetime);
  });

  const firstLat = parseFloat(locations[0].latitude);
  const firstLng = parseFloat(locations[0].longitude);

  const mymap = L.map('map').setView([firstLat, firstLng], 10);
  <PERSON><PERSON>tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
  }).addTo(mymap);

  // Create a custom red icon
  const redIcon = L.icon({
    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41]
  });

  let pathPoints = [];

  // Group locations by coordinates
  const locationMap = locations.reduce((acc, loc) => {
    const key = `${loc.latitude},${loc.longitude}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(loc);
    return acc;
  }, {});

  // Carousel navigation function - moved outside to be globally accessible
  window.moveSlide = function (btn, direction) {
    const container = btn.closest('.carousel-container');
    const slides = Array.from(container.querySelectorAll('.carousel-slide'));
    let currentIndex = slides.findIndex(slide => slide.style.display === 'block');

    slides[currentIndex].style.display = 'none';
    const nextIndex = (currentIndex + direction + slides.length) % slides.length;
    slides[nextIndex].style.display = 'block';

    // Update the indicator
    const popup = btn.closest('.location-popup');
    const currentSlideEl = popup.querySelector('.current-slide');
    if (currentSlideEl) {
      currentSlideEl.textContent = nextIndex + 1;
    }
  };

  Object.entries(locationMap).forEach(([coordKey, locsAtSamePlace]) => {
    const [lat, lng] = coordKey.split(',').map(parseFloat);
    pathPoints.push([lat, lng]);

    const popupContent = `
          <div class="location-popup">
            ${locsAtSamePlace[0].name ? `<h3>${locsAtSamePlace[0].name}</h3>` : ''}
            <div class="carousel-container">
              <button class="carousel-button prev" onclick="moveSlide(this, -1)">&#10094;</button>
              <div class="carousel-content">
                ${locsAtSamePlace.map((loc, index) => `
                  <div class="carousel-slide" style="display: ${index === 0 ? 'block' : 'none'};">
                    <h5>Event</h5>
                    <h5 class="info-title">Title</h5>
                    <p>${loc.title}</p>
                    <h5 class="info-title">Description</h5>
                    <p>${loc.description || 'No description provided.'}</p>
                    <h5 class="info-title">Start Datetime</h5>
                    <p>${loc.start_datetime}</p>
                    <div class="d-flex justify-content-center">
                      <a href="/event/${loc.id}/detail" class="btn btn-dark rounded-pill px-4 text-white">View</a>
                    </div>
                  </div>
                `).join('')}
              </div>
              <button class="carousel-button next" onclick="moveSlide(this, 1)">&#10095;</button>
            </div>
            <div class="carousel-indicator">
              <span class="current-slide">1</span>/<span class="total-slides">${locsAtSamePlace.length}</span>
            </div>
          </div>
        `;

    const marker = L.marker([lat, lng], { icon: redIcon }).addTo(mymap);
    marker.bindPopup(popupContent, { maxWidth: 300 });
  });

  if (pathPoints.length > 1) {

    // Zoom the map out to show all points
    const bounds = L.latLngBounds(pathPoints);
    mymap.fitBounds(bounds, {
      padding: [30, 30]
    });
  }
  });
</script>

<style>
  .location-popup h3 {
    margin-top: 0;
    margin-bottom: 8px;
    color: #2c3e50;
  }

  .location-popup p {
    margin: 5px 0;
  }

  .leaflet-popup-content {
    min-width: 200px;
  }

  .info-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
  }


  .location-popup h3 {
    margin-top: 0;
    margin-bottom: 10px;
  }

  .carousel-container {
    position: relative;
    width: 100%;
    margin: 0 auto;
  }

  .carousel-content {
    position: relative;
    min-height: 150px;
    padding: 10px 15px;
  }

  .carousel-slide {
    width: 100%;
  }

  .carousel-slide p {
    margin-top: 8px;
    margin-bottom: 5px;
  }

  .carousel-button {
    position: absolute;
    top: 50%;
    transform: translateY(-30%);
    background-color: rgba(240, 240, 240, 0.8);
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .carousel-button:hover {
    background-color: rgba(220, 220, 220, 1);
  }

  .carousel-button.prev {
    left: -15px;
  }

  .carousel-button.next {
    right: -15px;
  }

  .carousel-indicator {
    text-align: center;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
  }
</style>