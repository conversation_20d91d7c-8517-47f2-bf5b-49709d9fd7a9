{% extends "base.html" %}

{% block content %}

<div class="container py-2">
    <!-- Welcome Section - Greeting the user with their username -->
    <div class="card mb-4 bg-light border-0 shadow-sm">
        <div class="card-body p-4">
            <h1 class="h3 fw-bold mb-1">Welcome back, {{ session.username }}!</h1>
            <p class="text-muted mb-0 small">Ready to document your next adventure?</p>
        </div>
    </div>

    <!-- Announcements Section - Displays both read and unread announcements in tabs -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">New Announcements</h2>
            <div class="d-flex align-items-center">
                <a href="{{ url_for('announcement.get_user_unread_announcements') }}"
                    class="btn btn-outline-dark rounded-pill px-4">View All</a>
            </div>
        </div>

        <div class="tab-content" id="announcementTabsContent">
            <!-- Unread Announcements Tab -->
            <div class="tab-pane fade show active" id="unread-tab-pane" role="tabpanel" aria-labelledby="unread-tab"
                tabindex="0">
                {% if unread_announcements|length > 0 %}
                <div class="row g-4">
                    {% for announcement in unread_announcements[:3] %}
                    <div class="col-lg-4">
                        <div class="card h-100 border-0 shadow-sm rounded-4 announcement-card">
                            <div class="card-body p-4">
                                <span class="ms-auto text-muted small">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    {{ announcement['created_at'].strftime('%B %d, %Y') }}
                                </span>
                                <h3 class="h5 fw-bold mb-3">{{ announcement['title'] }}</h3>
                                <p class="text-muted mb-3 small announcement-content">{{ announcement['content'][:100]
                                    }}{% if
                                    announcement['content']|length > 100 %}...{% endif %}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="#" class="text-decoration-none read-more-link"
                                        data-title="{{ announcement['title'] }}"
                                        data-content="{{ announcement['content'] }}"
                                        data-date="{{ announcement['created_at'].strftime('%B %d, %Y') }}"
                                        data-id="{{ announcement['id'] }}"
                                        data-form-id="markAsReadForm_{{ announcement['id'] }}">
                                        Read more
                                    </a>
                                    <form method="POST"
                                        action="{{ url_for('announcement.mark_dashboard_announcement_as_read', announcement_id=announcement['id']) }}"
                                        id="markAsReadForm_{{ announcement['id'] }}" style="display: none;"></form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert rounded-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle-fill me-3 fs-4"></i>
                        <p class="mb-0">You're all caught up! No unread announcements at the moment.</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Public Journeys Section - Shows recent journeys with a carousel -->
    <div class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">Recent Discoveries</h2>
            <a href="{{ url_for('journey.get_public_journeys') }}" class="btn btn-outline-dark rounded-pill px-4">View
                All</a>
        </div>

        {% if public_journeys and public_journeys|length > 0 %}
        <!-- Bootstrap Carousel for Journeys -->
        <div id="journeyCarousel" class="carousel slide mb-4" data-bs-ride="carousel" data-bs-interval="5000">
            <div class="carousel-inner">
                {% set slides_per_group = 4 %}
                {% for i in range(0, public_journeys|length, slides_per_group) %}
                <div class="carousel-item {% if i == 0 %}active{% endif %}">
                    <div class="row">
                        {% for j in range(i, i + slides_per_group) %}
                        {% if j < public_journeys|length %} {% set journey=public_journeys[j] %} <div class="col-md-3">
                            <div class="card h-100 border-0 shadow-sm rounded-4 journey-card">
                                <div class="journey-image-container">
                                    {% if journey.event_image %}
                                    <img src="{{ url_for('static', filename='uploads/event_images/' + journey.event_image) }}"
                                        class="journey-image" alt="{{ journey.title }}"
                                        onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
                                    {% elif journey.image_url %}
                                    <img src="{{ journey.image_url }}" class="journey-image" alt="{{ journey.title }}"
                                        onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
                                    {% else %}
                                    <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                                        alt="Journey placeholder" class="journey-image">
                                    {% endif %}
                                </div>
                                <div class="card-body p-3">
                                    <div class="small text-muted mb-2">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ journey.start_date.strftime('%d/%m/%Y') }}
                                    </div>
                                    <h5 class="card-title journey-title">{{ journey.title }}</h5>
                                    <p class="card-text journey-desc">{{ journey.description }}</p>

                                    <div class="d-flex align-items-center mt-auto">
                                        <div class="author-avatar me-2">
                                            {% if journey.profile_image %}
                                            <img src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                                                alt="{{ journey.username }}" class="rounded-circle"
                                                onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                                            {% else %}
                                            <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                                                alt="{{ journey.username }}" class="rounded-circle">
                                            {% endif %}
                                        </div>
                                        <div>
                                            <div class="text-muted small">Written by</div>
                                            <div class="small fw-medium">{{ journey.username }}</div>
                                        </div>
                                        <a href="{{ url_for('journey.get_public_journey', journey_id=journey['id']) }}"
                                            class="ms-auto stretched-link"></a>
                                    </div>
                                </div>
                            </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Carousel controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#journeyCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon bg-dark rounded-circle p-3" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#journeyCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon bg-dark rounded-circle p-3" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
        </button>

        <!-- Carousel indicators -->
        <div class="carousel-indicators position-relative mt-3">
            {% for i in range((public_journeys|length / slides_per_group)|round(0, 'ceil')|int) %}
            <button type="button" data-bs-target="#journeyCarousel" data-bs-slide-to="{{ i }}"
                class="bg-dark {% if i == 0 %}active{% endif %}" aria-current="true"
                aria-label="Slide {{ i+1 }}"></button>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="alert alert-light rounded-4 text-center">
        <div class="d-flex align-items-center justify-content-center">
            <i class="bi bi-info-circle-fill me-3 fs-4 text-primary"></i>
            <p class="mb-0">No journeys published in the last 7 days. Check back soon for new adventures!</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Create New Journey Button -->
<div class="text-center mb-4">
    <a href="{{ url_for('journey.get_private_journeys') }}" class="btn btn-dark rounded-pill px-5 py-3">
        Go to My Journeys <i class="bi bi-arrow-right"></i>
    </a>
</div>
</div>

<style>
    /* Card hover effects */
    .announcement-card,
    .journey-card {
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .announcement-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1) !important;
    }

    .journey-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
        border-color: rgba(78, 107, 255, 0.3);
    }

    /* Custom tab styling with underline */
    .nav-tab-custom {
        background-color: transparent;
        color: var(--secondary-color);
        border: none;
        padding: 0.7rem 1.5rem;
        font-weight: 500;
        position: relative;
    }

    .nav-tab-custom.active {
        color: var(--dark-color);
        background-color: transparent;
        border: none;
    }

    .nav-tab-custom.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 20%;
        width: 60%;
        height: 3px;
        background-color: var(--primary-color);
        border-radius: 3px;
    }

    /* Journey card styling */
    .journey-card {
        height: var(--journey-card-height);
        position: relative;
    }

    .journey-image-container {
        height: var(--journey-image-height);
        overflow: hidden;
        border-radius: 0.75rem 0.75rem 0 0;
        background-color: #f8f9fa;
    }

    .journey-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .journey-no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
    }

    .journey-title {
        font-weight: bold;
        font-size: 1rem;
        line-height: 1.3;
        margin-bottom: 8px;
        max-height: 2.6rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-clamp: 2;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .journey-desc {
        color: var(--secondary-color);
        font-size: 0.8rem;
        line-height: 1.3;
        margin-bottom: 8px;
        max-height: 2.6rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-clamp: 2;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .author-avatar {
        width: 32px;
        height: 32px;
    }

    .author-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Fixed height for card body to maintain consistent layout */
    .journey-card .card-body {
        height: calc(var(--journey-card-height) - var(--journey-image-height));
        display: flex;
        flex-direction: column;
    }

    /* Custom carousel indicators */
    .carousel-indicators {
        margin-bottom: 0;
    }

    .carousel-indicators [data-bs-target] {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin: 0 5px;
    }

    /* Custom carousel controls */
    .carousel-control-prev,
    .carousel-control-next {
        width: 5%;
        opacity: 0.8;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 20px;
        height: 20px;
    }

    /* Ensure truncation of announcement content */
    .announcement-content {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
    }

    #journeyCarousel {
        position: relative;
    }

    #journeyCarousel .carousel-inner {
        padding: 20px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Initialize read more functionality for announcements
        initializeReadMore();
    });

    /**
     * Initialize read more functionality for announcements
     * Creates modal popups for viewing full announcement content
     */
    function initializeReadMore() {
        document.querySelectorAll('.read-more-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                const title = link.dataset.title;
                const content = link.dataset.content;
                const date = link.dataset.date;
                const formId = link.dataset.formId;

                const html = `
        <div class="announcement-detail">
          <div class="d-flex align-items-center mb-3">
            <i class="bi bi-calendar3 me-2"></i>
            <small class="text-muted">${date}</small>
          </div>
          <p class="mb-3">${content}</p>
        </div>
      `;

                showModal(title, html, {
                    actionText: 'Mark as Read',
                    onAction: () => {
                        document.getElementById(formId).submit();
                    }
                });
            });
        });
    }
</script>
{% endblock %}