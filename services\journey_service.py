"""
Journey Service Module

This module handles all journey-related operations including:
- Journey CRUD operations
- Journey privacy and visibility management
- Journey search and filtering
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import date
from services import subscription_service
from werkzeug.datastructures import FileStorage
from data import journey_data, user_data
from utils.logger import get_logger
from utils.file_utils import save_journey_cover_image, delete_file
import os
from werkzeug.utils import secure_filename


# Initialize logger
logger = get_logger(__name__)

# ===== Core Journey Operations =====

def create_journey(user_id: int, title: str, description: str,
                  start_date: date, visibility: str = 'private',
                  cover_image: Optional[FileStorage] = None,
                  no_edits: bool = False) -> Tuple[bool, str, Optional[int]]:
    """Create a new journey.

    Args:
        user_id: ID of the user creating the journey.
        title: Journey title.
        description: Journey description.
        start_date: Journey start date.
        visibility: Journey visibility ('private', 'public', 'published').
        cover_image: Optional cover image file.
        no_edits: Whether the journey should be protected from edits by staff.

    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and journey ID if created.
    """
    try:
        # Validate inputs
        if not title or not title.strip():
            logger.warning(f"User {user_id} attempted to create journey with empty title")
            return False, "Journey title cannot be empty", None

        if not description or not description.strip():
            logger.warning(f"User {user_id} attempted to create journey with empty description")
            return False, "Journey description cannot be empty", None

        # Check if user is blocked from sharing
        user = user_data.get_user_by_id(user_id)
        if not user:
            logger.warning(f"Attempt to create journey for non-existent user ID: {user_id}")
            return False, "User not found", None

        if visibility != 'private' and user['is_blocked']:
            logger.warning(f"Blocked user {user_id} attempted to create public journey")
            return False, "You have been blocked from sharing journeys", None

        # Check for premium features
        if visibility == 'published':
            can_publish = subscription_service.check_can_use_premium_features(user_id)
            if not can_publish:
                logger.warning(f"User {user_id} attempted to publish journey without premium subscription")
                return False, "Publishing journeys requires a premium subscription", None

        # Check for no_edits flag (premium feature)
        if no_edits:
            can_use_no_edits = subscription_service.check_can_use_premium_features(user_id)
            if not can_use_no_edits:
                logger.warning(f"User {user_id} attempted to set no_edits without premium subscription")
                return False, "Protecting journeys from edits requires a premium subscription", None

        # Process cover image if provided
        cover_filename = None
        if cover_image and hasattr(cover_image, 'filename') and cover_image.filename:
            # Sanitize filename to avoid issues with spaces or special characters
            cover_image.filename = secure_filename(cover_image.filename.replace(" ", "_"))

            # Check for premium features
            can_use_cover = subscription_service.check_can_use_premium_features(user_id)
            if not can_use_cover:
                logger.warning(f"User {user_id} attempted to add cover image without premium subscription")
                return False, "Adding cover images requires a premium subscription", None

            cover_filename = save_journey_cover_image(cover_image)
            if not cover_filename:
                logger.warning(f"Failed to save cover image for journey by user {user_id}")
                return False, "Failed to save cover image", None


        journey_id = journey_data.create_journey(
            user_id=user_id,
            title=title.strip(),
            description=description.strip(),
            start_date=start_date,
            visibility=visibility,
            cover_image=cover_filename,
            no_edits=no_edits
        )
        logger.info(f"User {user_id} created journey {journey_id}: '{title}'")
        return True, "Journey created successfully", journey_id
    except Exception as e:
        logger.error(f"Error creating journey for user {user_id}: {str(e)}")
        return False, f"Journey creation failed: {str(e)}", None


def get_journey(journey_id: int, user_id: Optional[int] = None) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
    """Get a journey by ID.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user requesting the journey (for permission check).

    Returns:
        Tuple[bool, str, Optional[Dict[str, Any]]]: Success flag, message, and journey data if found.
    """
    try:
        journey = journey_data.get_journey(journey_id)
        if not journey:
            logger.debug(f"Journey {journey_id} not found")
            return False, "Journey not found", None

        # Check if the journey is accessible to the user
        if not _can_access_journey(journey, user_id):
            logger.warning(f"User {user_id} attempted to access unauthorized journey {journey_id}")
            return False, "You do not have permission to view this journey", None

        logger.debug(f"Journey {journey_id} retrieved by user {user_id if user_id else 'anonymous'}")
        return True, "Journey retrieved successfully", journey
    except Exception as e:
        logger.error(f"Error retrieving journey {journey_id} for user {user_id}: {str(e)}")
        return False, f"Error retrieving journey: {str(e)}", None


def update_journey(journey_id: int, user_id: int, title: Optional[str] = None,
                  description: Optional[str] = None, start_date: Optional[date] = None,
                  visibility: Optional[str] = None, cover_image: Optional[FileStorage] = None,
                  no_edits: Optional[bool] = None, edit_reason: Optional[str] = None) -> Tuple[bool, str]:
    """Update a journey.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the update.
        title: New title (optional).
        description: New description (optional).
        start_date: New start date (optional).
        visibility: New visibility setting ('private', 'public', 'published').
        cover_image: New cover image (optional).
        no_edits: Flag to prevent editors from editing the journey.
        edit_reason: Reason for the edit (required for staff edits)

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Validate inputs if provided
        if title is not None and not title.strip():
            logger.warning(f"User {user_id} attempted to update journey {journey_id} with empty title")
            return False, "Journey title cannot be empty"

        if description is not None and not description.strip():
            logger.warning(f"User {user_id} attempted to update journey {journey_id} with empty description")
            return False, "Journey description cannot be empty"

        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message

        # Check if user is the owner or a content manager staff
        from utils.permissions import PermissionChecker
        is_owner = journey['user_id'] == user_id
        is_staff = PermissionChecker.can_manage_content()

        if not (is_owner or is_staff):
            logger.warning(f"User {user_id} attempted to update journey {journey_id} without permission")
            return False, "You do not have permission to update this journey"

        # If staff is editing someone else's journey, require edit reason
        if is_staff and not is_owner:
            if not edit_reason or not edit_reason.strip():
                logger.warning(f"Staff user {user_id} attempted to edit journey {journey_id} without providing a reason")
                return False, "Staff must provide a reason when editing a user's journey"

        # Check for no_edits flag - but allow staff to hide journeys
        if (journey['user_id'] != user_id and
            journey.get('no_edits', False) and
            not (is_staff and visibility is None and title is None and description is None and start_date is None and cover_image is None)):
            logger.warning(f"User {user_id} attempted to edit journey {journey_id} with no_edits flag")
            return False, "This journey is protected from edits by staff"

        # Check if user is blocked from sharing
        if visibility is not None and visibility != 'private':
            user = user_data.get_user_by_id(user_id)
            if user and user['is_blocked']:
                logger.warning(f"Blocked user {user_id} attempted to make journey {journey_id} public")
                return False, "You have been blocked from sharing journeys"

        # Check for premium features
        if visibility == 'published':
            can_publish = subscription_service.check_can_use_premium_features(user_id)
            if not can_publish:
                logger.warning(f"User {user_id} attempted to publish journey without premium subscription")
                return False, "Publishing journeys requires a premium subscription"

        # Process cover image if provided
        cover_filename = None
        if cover_image and hasattr(cover_image, 'filename') and cover_image.filename:
            can_use_cover = subscription_service.check_can_use_premium_features(user_id)
            if not can_use_cover:
                logger.warning(f"User {user_id} attempted to add cover image without premium subscription")
                return False, "Adding cover images requires a premium subscription"

            cover_filename = save_journey_cover_image(cover_image)
            if not cover_filename:
                logger.warning(f"Failed to save cover image for journey {journey_id}")
                return False, "Failed to save cover image"

            # Delete old cover image if exists
            if journey['cover_image']:
                try:
                    delete_file(os.path.join('journey_covers', journey['cover_image']))
                except Exception as e:
                    logger.warning(f"Failed to delete old cover image: {str(e)}")

        # Validate no_edits flag changes for journey owners
        if no_edits is not None and is_owner:
            current_no_edits = journey.get('no_edits', False)

            # If trying to enable no_edits (from False to True), check premium access
            if no_edits and not current_no_edits:
                can_use_no_edits = subscription_service.check_can_use_premium_features(user_id)
                if not can_use_no_edits:
                    logger.warning(f"User {user_id} attempted to enable no_edits without premium subscription")
                    return False, "Protecting journeys from edits requires a premium subscription"

            # If trying to disable no_edits (from True to False), allow it regardless of subscription
            # If maintaining existing no_edits flag, allow it regardless of subscription

        # Clean values if provided
        if title is not None:
            title = title.strip()
        if description is not None:
            description = description.strip()

        # Check for changes (for all users, not just staff)
        has_changes = False
        field_changes = {}

        # Check each field for changes
        if title is not None and title != journey['title']:
            has_changes = True
            if is_staff and not is_owner:
                field_changes['title'] = (journey['title'], title)

        if description is not None and description != journey['description']:
            has_changes = True
            if is_staff and not is_owner:
                field_changes['description'] = (journey['description'], description)

        if start_date is not None:
            # Convert both dates to string with the same format for comparison
            old_date_str = journey['start_date'].strftime('%Y-%m-%d')
            new_date_str = start_date.strftime('%Y-%m-%d') if isinstance(start_date, date) else str(start_date)
            if old_date_str != new_date_str:
                has_changes = True
                if is_staff and not is_owner:
                    field_changes['start_date'] = (str(journey['start_date']), str(start_date))

        if visibility is not None and visibility != journey['visibility']:
            has_changes = True
            if is_staff and not is_owner:
                field_changes['visibility'] = (journey['visibility'], visibility)

        if cover_filename is not None:
            has_changes = True
            if is_staff and not is_owner:
                field_changes['cover_image'] = (journey['cover_image'] or 'None', cover_filename)

        if no_edits is not None and no_edits != journey.get('no_edits', False):
            has_changes = True
            if is_staff and not is_owner:
                field_changes['no_edits'] = (str(journey.get('no_edits', False)), str(no_edits))

        # If there are no changes at all, return appropriate message
        if not has_changes:
            logger.info(f"No changes detected for journey {journey_id} by user {user_id}")
            return True, "No changes were made to the journey. The journey remains unchanged."

        rows_affected = journey_data.update_journey(
            journey_id=journey_id,
            title=title,
            description=description,
            start_date=start_date,
            visibility=visibility,
            cover_image=cover_filename,
            no_edits=no_edits
        )

        # If staff edited (not owner) and changes were made, record edit history
        if rows_affected > 0 and is_staff and not is_owner and field_changes:
            from services import edit_history_service
            success, message, edit_id = edit_history_service.record_edit(
                editor_id=user_id,
                content_type='journey',
                content_id=journey_id,
                field_changes=field_changes,
                reason=edit_reason.strip()
            )
            if not success:
                logger.warning(f"Failed to record edit history: {message}")
                # Continue anyway, as the journey was successfully updated

        if rows_affected > 0:
            logger.info(f"User {user_id} updated journey {journey_id}")
            return True, "Journey updated successfully"
        else:
            logger.info(f"No changes made to journey {journey_id} by user {user_id}")
            return True, "No changes were made to the journey"
    except Exception as e:
        logger.error(f"Error updating journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Journey update failed: {str(e)}"


def delete_journey(journey_id: int, user_id: int) -> Tuple[bool, str]:
    """Delete a journey.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the deletion.

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message

        # Only the owner can delete a journey
        if journey['user_id'] != user_id:
            logger.warning(f"User {user_id} attempted to delete journey {journey_id} owned by user {journey['user_id']}")
            return False, "Only the owner can delete a journey"

        # Delete cover image if exists
        if journey.get('cover_image'):
            try:
                delete_file(os.path.join('journey_covers', journey['cover_image']))
            except Exception as e:
                logger.warning(f"Failed to delete cover image: {str(e)}")

        rows_affected = journey_data.delete_journey(journey_id)

        if rows_affected > 0:
            logger.info(f"User {user_id} deleted journey {journey_id}")
            return True, "Journey deleted successfully"
        else:
            logger.warning(f"Failed to delete journey {journey_id} by user {user_id}")
            return False, "Failed to delete journey"
    except Exception as e:
        logger.error(f"Error deleting journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Journey deletion failed: {str(e)}"

# ===== Journey Status Management =====

def update_journey_visibility(journey_id: int, user_id: int, visibility: str) -> Tuple[bool, str]:
    """Update a journey's visibility setting.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the update.
        visibility: Journey visibility ('private', 'public', 'published').

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Validate visibility value
        if visibility not in ['private', 'public', 'published']:
            logger.warning(f"Invalid visibility value: {visibility}")
            return False, f"Invalid visibility value: {visibility}"

        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message

        # Only the owner can change visibility
        if journey['user_id'] != user_id:
            logger.warning(f"User {user_id} attempted to change visibility of journey {journey_id} owned by user {journey['user_id']}")
            return False, "Only the owner can change the visibility of a journey"

        # Check if user is blocked from sharing
        user = user_data.get_user_by_id(user_id)
        if visibility != 'private' and user and user['is_blocked']:
            logger.warning(f"Blocked user {user_id} attempted to make journey {journey_id} public/published")
            return False, "You have been blocked from sharing journeys"

        # Check for premium features
        if visibility == 'published':
            can_publish = subscription_service.check_can_use_premium_features(user_id)
            if not can_publish:
                logger.warning(f"User {user_id} attempted to publish journey without premium subscription")
                return False, "Publishing journeys requires a premium subscription"

        rows_affected = journey_data.update_journey_visibility(journey_id, visibility)

        if rows_affected > 0:
            logger.info(f"User {user_id} changed journey {journey_id} visibility to {visibility}")
            return True, f"Journey visibility set to {visibility} successfully"
        else:
            logger.info(f"No visibility change for journey {journey_id} by user {user_id}")
            return True, "No changes were made to the journey visibility"
    except Exception as e:
        logger.error(f"Error updating visibility for journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Visibility update failed: {str(e)}"


def update_journey_hidden_status(journey_id: int, user_id: int, is_hidden: bool) -> Tuple[bool, str]:
    """Update a journey's hidden status (editor/admin only).

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the update.
        is_hidden: Whether the journey should be hidden.

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists
        journey = journey_data.get_journey(journey_id)
        if not journey:
            logger.warning(f"Attempt to update hidden status of non-existent journey {journey_id}")
            return False, "Journey not found"

        # Check if user is an editor or admin
        if not _is_privileged_user(user_id):
            logger.warning(f"Non-privileged user {user_id} attempted to change hidden status of journey {journey_id}")
            return False, "Only editors and admins can hide journeys"

        # Store original status and owner info for notification
        original_hidden_status = journey.get('is_hidden', False)
        journey_owner_id = journey['user_id']
        journey_title = journey['title']

        rows_affected = journey_data.update_journey_hidden_status(journey_id, is_hidden)

        if rows_affected > 0:
            status = "hidden" if is_hidden else "visible"
            logger.info(f"Admin/editor {user_id} set journey {journey_id} to {status}")

            # Send notification to journey owner if status changed and it's not the owner making the change
            if (original_hidden_status != is_hidden and
                journey_owner_id != user_id and
                is_hidden):  # Only notify when hiding, not when unhiding

                try:
                    # Get staff member info for notification
                    staff_user = user_data.get_user_by_id(user_id)
                    staff_name = staff_user['username'] if staff_user else 'A staff member'

                    # Create notification content
                    notification_content = f"Your journey '{journey_title}' has been hidden by {staff_name}. It is no longer visible to other users."

                    # Send notification to journey owner
                    from services import notification_service
                    success, message, notification_id = notification_service.create_notification(
                        user_id=journey_owner_id,
                        notification_type='edit',
                        content=notification_content,
                        related_id=journey_id
                    )

                    if success:
                        logger.info(f"Notification sent to user {journey_owner_id} about journey {journey_id} being hidden")
                    else:
                        logger.warning(f"Failed to send notification to user {journey_owner_id}: {message}")

                except Exception as e:
                    logger.error(f"Error sending notification for hidden journey {journey_id}: {str(e)}")
                    # Don't fail the main operation if notification fails

            return True, f"Journey set to {status} successfully"
        else:
            logger.info(f"No visibility change for journey {journey_id} by user {user_id}")
            return True, "No changes were made to the journey visibility"
    except Exception as e:
        logger.error(f"Error updating hidden status for journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Status update failed: {str(e)}"

def update_journey_no_edits(journey_id: int, user_id: int, no_edits: bool) -> Tuple[bool, str]:
    """Update a journey's no_edits flag (owner or premium users only).

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the update.
        no_edits: Whether the journey should be protected from edits.

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message

        # Only the owner can set no_edits flag
        if journey['user_id'] != user_id:
            logger.warning(f"User {user_id} attempted to change no_edits flag of journey {journey_id} owned by user {journey['user_id']}")
            return False, "Only the owner can protect a journey from edits"

        # Check for premium features
        if no_edits:
            from services import subscription_service
            can_use_feature = subscription_service.check_can_use_premium_features(user_id)
            if not can_use_feature:
                logger.warning(f"User {user_id} attempted to set no_edits without premium subscription")
                return False, "Protecting journeys from edits requires a premium subscription"

        rows_affected = journey_data.update_journey_no_edits(journey_id, no_edits)

        if rows_affected > 0:
            status = "protected from edits" if no_edits else "open for edits"
            logger.info(f"User {user_id} set journey {journey_id} to {status}")
            return True, f"Journey is now {status}"
        else:
            logger.info(f"No change to no_edits flag for journey {journey_id} by user {user_id}")
            return True, "No changes were made to the edit protection"
    except Exception as e:
        logger.error(f"Error updating no_edits flag for journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Update failed: {str(e)}"

# ===== Journey Cover Image Management =====

def update_journey_cover_image(journey_id: int, user_id: int, cover_image: FileStorage) -> Tuple[bool, str]:
    """Update a journey's cover image.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the update.
        cover_image: New cover image file.

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message

        # Only the owner can change cover image
        if journey['user_id'] != user_id:
            logger.warning(f"User {user_id} attempted to change cover image of journey {journey_id}")
            return False, "Only the owner can change the cover image"

        # Check for premium features
        from services import subscription_service
        subscription_status = subscription_service.get_user_subscription_status(user_id)
        if not subscription_status.get('is_premium'):
            logger.warning(f"User {user_id} attempted to add cover image without premium subscription")
            return False, "Adding cover images requires a premium subscription"

        # Save new cover image
        cover_filename = save_journey_cover_image(cover_image)
        if not cover_filename:
            logger.warning(f"Failed to save cover image for journey {journey_id}")
            return False, "Failed to save cover image"

        # Delete old cover image if exists
        if journey.get('cover_image'):
            try:
                delete_file(journey['cover_image'])
                logger.debug(f"Deleted old cover image: {journey['cover_image']}")
            except Exception as e:
                logger.warning(f"Failed to delete old cover image: {str(e)}")

        # Update journey with new cover image
        rows_affected = journey_data.update_journey_cover_image(journey_id, cover_filename)

        if rows_affected == 0:
            logger.warning(f"No changes made to journey {journey_id} cover image")
            return False, "No changes were made to the cover image"

        logger.info(f"User {user_id} updated cover image for journey {journey_id}")
        return True, "Cover image updated successfully"
    except Exception as e:
        logger.error(f"Error updating cover image for journey {journey_id}: {str(e)}")
        return False, f"Cover image update failed: {str(e)}"

def upload_journey_cover(journey_id: int, user_id: int, file: FileStorage) -> Tuple[bool, str, Optional[str]]:
    """Upload a cover photo for a journey.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user uploading the cover.
        file: Cover image file.

    Returns:
        Tuple[bool, str, Optional[str]]: Success flag, message, and filename.
    """
    try:
        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message, None

        # Only the owner can upload cover image
        if journey['user_id'] != user_id:
            logger.warning(f"User {user_id} attempted to upload cover image for journey {journey_id}")
            return False, "Only the owner can upload a cover image", None

        # Check for premium features
        from services import subscription_service
        can_use_cover = subscription_service.check_can_use_premium_features(user_id)
        if not can_use_cover:
            logger.warning(f"User {user_id} attempted to upload cover image without premium subscription")
            return False, "Adding cover images requires a premium subscription", None

        # Save new cover image
        cover_filename = save_journey_cover_image(file)
        if not cover_filename:
            logger.warning(f"Failed to save cover image for journey {journey_id}")
            return False, "Failed to save cover image", None

        # Delete old cover image if exists
        if journey.get('cover_image'):
            try:
                delete_file(os.path.join('journey_covers', journey['cover_image']))
                logger.debug(f"Deleted old cover image: {journey['cover_image']}")
            except Exception as e:
                logger.warning(f"Failed to delete old cover image: {str(e)}")

        # Update journey with new cover image
        rows_affected = journey_data.update_journey_cover_image(journey_id, cover_filename)

        if rows_affected == 0:
            logger.warning(f"No changes made to journey {journey_id} cover image")
            return False, "No changes were made to the cover image", None

        logger.info(f"User {user_id} uploaded cover image for journey {journey_id}")
        return True, "Cover image uploaded successfully", cover_filename
    except Exception as e:
        logger.error(f"Error uploading cover image for journey {journey_id}: {str(e)}")
        return False, f"Cover image upload failed: {str(e)}", None

def remove_journey_cover_image(journey_id: int, user_id: int, edit_reason: str = None) -> Tuple[bool, str]:
    """Remove a journey's cover image.

    Args:
        journey_id: ID of the journey.
        user_id: ID of the user making the update.
        edit_reason: Reason for the edit (required for staff edits).

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists and user has permission
        success, message, journey = get_journey(journey_id, user_id)
        if not success:
            return False, message

        # Check if user is the owner or a privileged user (admin/editor)
        is_owner = journey['user_id'] == user_id
        is_privileged = _is_privileged_user(user_id)

        if not (is_owner or is_privileged):
            logger.warning(f"User {user_id} attempted to remove cover image of journey {journey_id}")
            return False, "Only the journey owner, editors, or administrators can remove the cover image"

        # Staff editing other users' content must provide a reason
        if is_privileged and not is_owner:
            if not edit_reason or not edit_reason.strip():
                logger.warning(f"Staff user {user_id} attempted to remove cover image without providing a reason")
                return False, "Staff must provide a reason when removing a user's cover image"

        # Check if journey has a cover image
        if not journey.get('cover_image'):
            logger.info(f"Journey {journey_id} has no cover image to remove")
            return False, "This journey does not have a cover image"

        # Store old cover image for edit logging
        old_cover_image = journey.get('cover_image')

        # Delete cover image file
        try:
            delete_file(journey['cover_image'])
            logger.debug(f"Deleted cover image: {journey['cover_image']}")
        except Exception as e:
            logger.warning(f"Failed to delete cover image: {str(e)}")

        # Update journey to remove cover image
        rows_affected = journey_data.update_journey_cover_image(journey_id, None)

        if rows_affected == 0:
            logger.warning(f"No changes made to journey {journey_id} cover image - treating as successful since desired state is achieved")
            # Treat as success since the desired end state (cover image removed) is already achieved

        # If staff edited (not owner) and changes were made, record edit history with meaningful status
        if rows_affected > 0 and is_privileged and not is_owner:
            from services import edit_history_service

            # Record meaningful status instead of filename
            old_status = "Has cover image" if old_cover_image else "No cover image"
            new_status = "No cover image"

            field_changes = {
                'cover_image': (old_status, new_status)
            }
            success_log, message_log, edit_id = edit_history_service.record_edit(
                editor_id=user_id,
                content_type='journey',
                content_id=journey_id,
                field_changes=field_changes,
                reason=edit_reason.strip()
            )
            if not success_log:
                logger.warning(f"Failed to record edit history: {message_log}")
                # Continue anyway, as the cover image was successfully removed

        user_type = "owner" if is_owner else "admin/editor"
        logger.info(f"{user_type} {user_id} removed cover image from journey {journey_id}")
        return True, "Cover image removed successfully"
    except Exception as e:
        logger.error(f"Error removing cover image for journey {journey_id}: {str(e)}")
        return False, f"Cover image removal failed: {str(e)}"

def get_private_journeys(user_id, limit=10, offset=0, search=''):
    """Get user's private journeys with pagination and search"""
    if search:
        return journey_data.search_my_journeys(user_id, search, limit, offset)
    return journey_data.get_private_journeys(user_id)[offset:offset + limit]


def get_private_journeys_count(user_id, search=''):
    """Get total count of user's private journeys with search"""
    if search:
        journeys = journey_data.search_my_journeys(user_id, search, 1000, 0)
    else:
        journeys = journey_data.get_private_journeys(user_id)
    return len(journeys)

# ===== Public Journeys (Discovery) =====

def get_public_journeys(user_id=None, limit=10, offset=0, search=''):
    """Get public journeys with optional user filter, pagination and search"""
    if search:
        if user_id:
            # 유저별 검색은 공개/비공개 모두 반환하므로, 후처리 필요할 수 있음
            journeys = journey_data.search_my_journeys(user_id, search, limit, offset)
            # 공개/발행만 필터링
            return [j for j in journeys if j.get('visibility') in ('public', 'published') and not j.get('is_hidden')]
        else:
            return journey_data.search_journeys(search, limit, offset)
    if user_id:
        return journey_data.get_user_public_journeys(user_id, limit, offset)
    return journey_data.get_public_journeys(limit, offset)


def get_public_journey_count():
    """Returns the total count of public journeys."""
    return journey_data.get_public_journey_count()


def get_public_journeys_count(search=''):
    """Get total count of public journeys with search"""

    journeys = get_public_journeys(limit=1000, offset=0, search=search)
    return len(journeys)

# ===== Admin Journey Management =====

def get_all_journeys(limit=10, offset=0, search=''):
    """Get all journeys with pagination and search (admin only)"""
    if search:
        return journey_data.search_journeys(search, limit, offset)
    return journey_data.get_all_journeys(limit, offset)


def get_all_journeys_count(search=''):
    """Get total count of all journeys with search (admin only)"""
    journeys = get_all_journeys(limit=1000, offset=0, search=search)
    return len(journeys)


def get_hidden_journeys(limit=10, offset=0, search=''):
    """Get hidden journeys with pagination and search (admin only)"""
    all_hidden = journey_data.get_hidden_journeys()
    if search:
        filtered = [j for j in all_hidden if search.lower() in j['title'].lower() or
                   (j['description'] and search.lower() in j['description'].lower())]
    else:
        filtered = all_hidden

    # Return all journeys if limit is 0
    if limit == 0:
        return filtered

    # Otherwise apply pagination
    start = offset
    end = offset + limit
    return filtered[start:end]


def get_hidden_journeys_count(search=''):
    """Get total count of hidden journeys with search (admin only)"""
    journeys = get_hidden_journeys(limit=1000, offset=0, search=search)
    return len(journeys)

# ===== Search Functions =====

def search_my_journeys(user_id, search_term, limit=50, offset=0):
    """Search user's own journeys"""
    return journey_data.search_my_journeys(user_id, search_term, limit, offset)


def search_journeys(search_term, limit=50, offset=0):
    """Search public journeys"""
    return journey_data.search_journeys(search_term, limit, offset)


def search_journeys_by_location(location_id, limit=50, offset=0):
    """Search journeys by location"""
    return journey_data.search_journeys_by_location(location_id, limit, offset)

# ===== Utility Functions =====

def count_journey_events(journey_id):
    """Count events in a journey"""
    return journey_data.count_journey_events(journey_id)


def _can_access_journey(journey, user_id):
    """
    Check if a user can access a journey.

    Args:
        journey: Journey object
        user_id: ID of the user

    Returns true if:
    - Journey is published and not hidden (accessible to anonymous users)
    - Journey is public and user is logged in (not hidden)
    - Journey is hidden public/published and user is owner or content manager
    - Journey is private and user is owner (content managers lose access to private journeys)
    - User is the owner (always has access to their own content)
    """
    # Owner always has access to their own journeys
    if user_id and journey['user_id'] == user_id:
        return True

    # Private journeys are only accessible to owners, regardless of hidden status
    if journey['visibility'] == 'private':
        return False

    # Check if user is content manager (for hidden public/published content access)
    is_content_manager = user_id and _is_content_manager_by_user_id(user_id)

    # Handle hidden public/published journeys - only owners and content managers can access
    if journey['is_hidden']:
        return bool(is_content_manager)

    # Anonymous access only to published journeys (not hidden)
    if user_id is None:
        return journey['visibility'] == 'published'

    # Logged-in users can access public and published journeys (not hidden)
    if journey['visibility'] in ('public', 'published'):
        return True

    return False


def _is_content_manager_by_user_id(user_id):
    """
    Check if a user is a content manager (editor, support_tech, or admin) by user ID.

    Args:
        user_id: ID of the user

    Returns:
        bool: Whether the user is a content manager
    """
    if not user_id:
        return False

    from utils.permissions import PermissionGroups
    user = user_data.get_user_by_id(user_id)
    return user and user['role'] in PermissionGroups.CONTENT_MANAGERS


def _is_privileged_user(user_id):
    """
    Check if a user is an editor, support_tech, or admin

    Args:
        user_id: ID of the user

    Returns:
        bool: Whether the user is an editor or admin
    """
    return _is_content_manager_by_user_id(user_id)

def organize_journeys_by_user(journeys):
    """
    Organize journeys by user for display in hidden journeys pages.

    Args:
        journeys (list): List of journey dictionaries with user information

    Returns:
        dict: Dictionary mapping user IDs to user info and their journeys
    """
    journeys_by_user = {}
    for journey in journeys:
        user_id = journey['user_id']

        if user_id not in journeys_by_user:
            journeys_by_user[user_id] = {
                'username': journey['username'],
                'first_name': journey.get('first_name'),
                'last_name': journey.get('last_name'),
                'profile_image': journey.get('profile_image'),
                'journeys': []
            }

        journeys_by_user[user_id]['journeys'].append(journey)

    return journeys_by_user

def get_published_journeys(limit=10, offset=0, search=''):
    return journey_data.get_published_journeys(limit, offset)

def get_published_journey(journey_id):
    return journey_data.get_published_journey(journey_id)

def get_published_journey_count():
    """Returns the total count of published journeys."""
    return journey_data.get_published_journey_count()
