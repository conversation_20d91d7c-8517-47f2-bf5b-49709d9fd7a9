{% block content %}
<div class="container-fluid pb-4">
  <div class="tab-container mb-4">
    <div class="tabs">
      <a class="tab-item {% if request.args.get('filter', 'user') == 'user' %}active{% endif %}"
        href="{{ url_for('departure_board.get_departure_list', tab='manage', filter='user') }}">
        Users
      </a>
      <a class="tab-item {% if request.args.get('filter') == 'journey' %}active{% endif %}"
        href="{{ url_for('departure_board.get_departure_list', tab='manage', filter='journey') }}">
        Journeys
      </a>
      <a class="tab-item {% if request.args.get('filter') == 'location' %}active{% endif %}"
        href="{{ url_for('departure_board.get_departure_list', tab='manage', filter='location') }}">
        Locations
      </a>
    </div>
  </div>
  <div class="card shadow-sm border-0 rounded-3">
    <div class="card-body">

      {% if grouped_events %}
      {% for group in grouped_events %}
      <div class="accordion mb-3" id="accordion{{ group.entity_id }}">
        <div class="accordion-item border rounded-3 shadow-sm">
          <h2 class="accordion-header" id="heading{{ group.entity_id }}">
            <div class="d-flex align-items-center justify-content-between px-3 py-2">
              <div class="d-flex align-items-center flex-grow-1">
                <button class="accordion-button collapsed w-100 text-start px-0" type="button" data-bs-toggle="collapse"
                  data-bs-target="#collapse{{ group.entity_id }}" aria-expanded="false"
                  aria-controls="collapse{{ group.entity_id }}">
                  <div class="d-flex align-items-center">
                    {% if group.entity_type == 'user' and group.profile_image %}
                    <img src="{{ url_for('static', filename='uploads/profile_images/' + group.profile_image) }}"
                      alt="{{ group.username }}" class="rounded-circle me-3"
                      style="width: 40px; height: 40px; object-fit: cover;">
                    {% endif %}
                    <span class="fw-semibold">
                      {% if group.entity_type == 'user' %}{{ group.username }}
                      {% elif group.entity_type == 'journey' %}{{ group.title }}
                      {% elif group.entity_type == 'location' %}{{ group.name }}
                      {% else %}{{ group.title }}{% endif %}
                    </span>
                  </div>
                </button>
              </div>
              <form method="POST" action="{% if group.entity_type == 'user' %}
                                      {{ url_for('departure_board.unfollow_user', followed_id=group.entity_id) }}
                                    {% elif group.entity_type == 'journey' %}
                                      {{ url_for('departure_board.unfollow_journey', journey_id=group.entity_id) }}
                                    {% elif group.entity_type == 'location' %}
                                      {{ url_for('departure_board.unfollow_location', location_id=group.entity_id) }}
                                    {% endif %}" class="ms-3">
                <button type="submit" class="btn btn-sm btn-dark rounded-pill">
                  Unfollow
                </button>
              </form>
            </div>

          </h2>
          <div id="collapse{{ group.entity_id }}" class="accordion-collapse collapse"
            aria-labelledby="heading{{ group.entity_id }}" data-bs-parent="#accordion{{ group.entity_id }}">
            <div class="accordion-body">
              {% for event in group.events %}
              <div class="p-4 border mb-2 rounded">
                <div class="d-flex">
                  <div class="me-4">
                    {% if event.event_image %}
                    <img src="{{ url_for('static', filename='uploads/event_images/' + event.event_image) }}"
                      alt="{{ event.event_title }}" class="rounded"
                      style="width: 100px; height: 80px; object-fit: cover;">
                    {% else %}
                    <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                      alt="Image placeholder" class="rounded" style="width: 100px; height: 80px; object-fit: cover;">
                    {% endif %}
                  </div>
                  <div class="flex-grow-1">
                    <h5 class="mb-1 fw-semibold">{{ event.event_title }}</h5>
                    <p class="mb-1 text-muted">{{ event.journey_title }}</p>
                    <div class="text-muted small">
                      <i class="bi bi-geo-alt me-1"></i>{{ event.location_name }}
                    </div>
                    {% if event.updated_at %}
                    <div class="text-muted small">
                      Updated {{ event.updated_at.strftime('%d/%m/%Y') }}
                    </div>
                    {% endif %}
                  </div>
                </div>
              </div>
              {% endfor %}
              {% if group.events|length == 0 %}
              <p>No events found.</p>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
      {% else %}
      <div class="alert rounded-4"
        style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2);">
        <div class="d-flex align-items-center">
          <i class="bi bi-info-circle-fill me-3 fs-4"></i>
          {% if request.args.get('filter') %}
          <p class="mb-0">You are not following any {{request.args.get('filter')}}s</p>
          {% else %}
          <p class="mb-0">You are not following any users</p>
          {% endif %}
        </div>
      </div>
      {% endif %}

    </div>
  </div>
</div>

<style>
  /* Segmented Control Styling */
  .tab-container {
    display: block;
  }

  .tabs {
    display: flex;
    width: 400px;
    max-width: 100%;
    border: 1px solid gray;
    border-radius: 10px;
    overflow: hidden;
  }

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 10px 20px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    background-color: #fff;
    border-right: 1px solid gray;
    transition: background-color 0.3s;
  }

  .tab-item:last-child {
    border-right: none;
  }

  .tab-item:hover {
    background-color: #e9e9e9;
    text-decoration: none;
    color: #333;
  }

  .tab-item.active {
    background-color: #f5f5f5;
    color: #000;
    font-weight: 600;
  }
</style>

{% endblock %}