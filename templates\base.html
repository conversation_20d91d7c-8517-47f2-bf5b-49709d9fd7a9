<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Footprints{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" />
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <style>
        .container {
            flex: none !important;
        }

        /* Fixed navigation */
        .navbar-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            padding: 15px 0;
            background-color: var(--white-opacity-95);
            backdrop-filter: blur(10px);
        }

        /* Body padding for fixed navbar */
        body {
            padding-top: 90px;
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        /* Adjust navbar position when blocked user banner is present */
        body.has-blocked-banner .navbar-wrapper {
            top: 42px;
            /* Height of the compact banner */
        }

        body.has-blocked-banner {
            padding-top: 132px;
            /* 90px navbar + 42px banner */
        }

        /* Toast styles */
        .toast-container {
            z-index: 9999 !important;
            position: fixed !important;
            top: 60px !important;
            /* Position below navbar */
            right: 20px !important;

        }

        /* Adjust toast position when blocked user banner is present */
        body.has-blocked-banner .toast-container {
            top: 102px !important;
            /* 60px + 42px banner height */
        }

        .toast {
            transition: all 0.3s ease;
            min-width: 300px;
        }

        .toast:hover {
            transform: translateY(-3px);
        }

        /* Navigation styles */
        .nav-link {
            border-radius: 20px;
            transition: all 0.3s ease;
            padding-left: 10px !important;
            padding-right: 10px !important;
            white-space: nowrap;
        }

        .nav-link:hover,
        .nav-link.active {
            background-color: var(--dark-color) !important;
            color: var(--light-color) !important;
        }

        .nav-link.active {
            font-weight: 500;
        }

        .dropdown-item.active {
            background-color: var(--dark-color);
            color: var(--light-color);
        }

        /* Custom navbar style */
        .custom-navbar {
            border-radius: 50px !important;
            background-color: var(--light-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        /* Footer copyright mobile styles */
        @media (max-width: 576px) {
            .footer-copyright {
                font-size: 0.7rem !important;
                text-align: right;
                padding-right: 0;
            }
        }

        /* Mobile menu styling */
        @media (max-width: 1199px) {
            .navbar-nav {
                text-align: center;
            }

            .navbar-nav .nav-item {
                margin: 0 auto;
            }

            .navbar-nav .nav-link {
                padding-left: 20px !important;
                padding-right: 20px !important;
                margin: 5px auto !important;
                display: inline-block;
            }

            .dropdown-menu {
                text-align: center;
                border: none;
                box-shadow: none;
                background-color: rgba(0, 0, 0, 0.03);
                border-radius: 10px;
                margin-top: 5px;
            }

            .dropdown-item {
                text-align: center;
                padding: 8px 20px;
            }

            .mobile-user-menu {
                text-align: center;
                border-top: 1px solid #eee;
                margin-top: 15px;
                padding-top: 15px;
            }

            .mobile-user-menu .mobile-menu-item {
                display: block;
                text-align: center;
                padding: 8px 0;
                margin: 5px auto;
                max-width: 200px;
                border-radius: 20px;
                transition: all 0.3s ease;
                color: var(--dark-color);
            }
        }

        @media (min-width: 1200px) {
            .mobile-user-menu {
                display: none;
            }
        }

        /* Button styles with CSS variables */
        .btn-primary {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: var(--light-color) !important;
        }

        .btn-primary:hover {
            background-color: var(--primary-darker) !important;
            border-color: var(--primary-darker) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .unread-dot {
            display: block;
            width: 10px;
            height: 10px;
            background: #1976d2;
            border-radius: 50%;
            margin-right: 10px;
            margin-top: 10px;
            flex-shrink: 0;
        }

        #notificationList {
            width: 100%;
        }

        .dropdown-menu[aria-labelledby="notificationDropdown"] {
            max-height: 350px !important;
            overflow-y: auto !important;
            scrollbar-width: thin;
            /* Firefox */
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
            /* Firefox */
            width: 350px;
            /* Set fixed width for consistency */
            border-radius: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.10);
        }

        /* Webkit (Chrome/Safari/Edge) scrollbar styling */
        .dropdown-menu[aria-labelledby="notificationDropdown"]::-webkit-scrollbar {
            width: 8px;
        }

        .dropdown-menu[aria-labelledby="notificationDropdown"]::-webkit-scrollbar-track {
            background: transparent;
        }

        .dropdown-menu[aria-labelledby="notificationDropdown"]::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
        }

        .dropdown-menu[aria-labelledby="notificationDropdown"]::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.3);
        }

        .notification-item:hover {
            background-color: rgba(0, 0, 0, 0.03);
            cursor: pointer;
        }

        .navbar .user-dropdown-username {
            max-width: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            vertical-align: middle;
        }

        /* Smart back button styling - Global component */
        .back-button {
            transition: all 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
        }

        .back-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
            transform: translateX(-2px);
            color: #4e6bff !important;
        }

        .back-button i {
            transition: transform 0.2s ease;
        }

        .back-button:hover i {
            transform: translateX(-2px);
        }
    </style>

    {% block head %}{% endblock %}
</head>

<!-- Include Modal Component -->
{% include 'components/modal.html' %}

<body>
    <!-- Flask Server-Side Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <!-- Use data attributes to pass flash messages to JavaScript safely -->
    <div id="flask-messages" style="display: none;"
        data-messages='[{% for category, message in messages %}{"message": "{{ message|e }}", "category": "{{ category }}"}{% if not loop.last %},{% endif %}{% endfor %}]'>
    </div>
    <script>
        // Process Flash messages when DOM is loaded
        document.addEventListener('DOMContentLoaded', function () {
            const messagesElement = document.getElementById('flask-messages');
            if (messagesElement) {
                try {
                    const messages = JSON.parse(messagesElement.dataset.messages);
                    messages.forEach(function (msg) {
                        setTimeout(function () {
                            showFlashMessage(msg.message, msg.category);
                        }, 100);
                    });
                } catch (e) {
                    console.error('Error processing flash messages:', e);
                }
            }
        });
    </script>
    {% endif %}
    {% endwith %}

    <!-- Blocked User Banner -->
    {% if session.user_id and g.current_user and g.current_user.is_blocked %}
    <div id="blockedUserBanner" class="alert alert-warning alert-dismissible fade show m-0 rounded-0 border-0"
        role="alert" style="position: fixed; top: 0; left: 0; right: 0; z-index: 1040; padding: 8px 0;">
        <div class="container">
            <div class="d-flex align-items-center">
                <i class="bi bi-exclamation-triangle-fill me-2" style="font-size: 0.9rem;"></i>
                <div class="flex-grow-1 me-2">
                    <strong>Account Sharing Restricted</strong>
                    <span class="d-none d-md-inline ms-2">
                        - Your account is currently blocked from sharing journeys publicly.
                    </span>
                </div>
                <div class="me-3">
                    <a href="{{ url_for('helpdesk.blocked_user_appeal') }}" class="btn btn-warning btn-sm py-1 px-2"
                        style="font-size: 0.8rem;">
                        <i class="bi bi-flag me-1 d-none d-sm-inline"></i>Appeal
                    </a>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"
                    style="font-size: 0.8rem;"></button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Navigation -->
    <div class="navbar-wrapper">
        <div class="container">
            {% if session.user_id %}
            <!-- Navigation after login -->
            <nav class="navbar navbar-expand-xl custom-navbar">
                <div class="container px-4">
                    <a class="navbar-brand d-flex align-items-center" href="{{ url_for('main.get_admin_dashboard') if can_change_user_roles()
                                else url_for('main.get_editor_dashboard') if can_manage_content()
                                else url_for('main.get_user_dashboard') }}">
                        <img src="{{ url_for('static', filename='images/footprints_logo.png') }}" alt="Footprints"
                            height="30" class="me-2">
                        <span class="fw-bold">Footprints</span>
                    </a>

                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav mx-auto">
                            {% if session.get('role') == 'traveller' %}
                            {% if session.is_premium %}
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-departure-board"
                                    href="{{ url_for('departure_board.get_departure_list') }}"
                                    data-path="departure_board">
                                    Departure Board
                                </a>
                            </li>
                            {% endif %}
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-dashboard"
                                    href="{{ url_for('main.get_landing_page') }}" data-path="dashboard">
                                    Dashboard
                                </a>
                            </li>
                            {% else %}
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-dashboard"
                                    href="{{ url_for('main.get_landing_page') }}" data-path="dashboard">
                                    Dashboard
                                </a>
                            </li>
                            {% if session.is_premium %}
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-departure-board"
                                    href="{{ url_for('departure_board.get_departure_list') }}"
                                    data-path="departure_board">
                                    Departure Board
                                </a>
                            </li>
                            {% endif %}
                            {% endif %}
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-discovery"
                                    href="{{ url_for('journey.get_public_journeys') }}" data-path="journey/public">
                                    Discovery
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-journeys" id="myJourneysNavbar"
                                    href="{{ url_for('journey.get_private_journeys') }}" data-path="journey/private">
                                    My Journeys
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link px-3 mx-1 nav-announcements"
                                    href="{{ url_for('announcement.get_announcements') }}" data-path="announcement">
                                    Announcements
                                </a>
                            </li>
                            {% if has_role(['moderator']) %}
                            <li class="nav-item dropdown">
                                <a class="nav-link px-3 mx-1 dropdown-toggle nav-management" href="#"
                                    id="managementDropdown" role="button" data-bs-toggle="dropdown"
                                    aria-expanded="false" data-paths="management admin users locations">
                                    Management
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item nav-admin-announcements"
                                            href="{{ url_for('report.get_reports') }}" data-path="report/manage"><i
                                                class="bi bi-flag-fill me-2"></i>Reports</a></li>
                                </ul>
                            </li>
                            {% endif %}
                            {% if can_manage_content() %}
                            <li class="nav-item dropdown">
                                <a class="nav-link px-3 mx-1 dropdown-toggle nav-management" href="#"
                                    id="managementDropdown" role="button" data-bs-toggle="dropdown"
                                    aria-expanded="false" data-paths="management admin users locations">
                                    Management
                                </a>
                                <ul class="dropdown-menu">
                                    {% if can_administrate() %}
                                    <li><a class="dropdown-item nav-users" href="{{ url_for('user.manage_users') }}"
                                            data-path="user/manage"><i class="bi bi-people-fill me-2"></i>Users</a></li>
                                    {% endif %}
                                    <li><a class="dropdown-item nav-locations"
                                            href="{{ url_for('location.get_locations') }}"
                                            data-path="location/manage"><i
                                                class="bi bi-geo-alt-fill me-2"></i>Locations</a></li>
                                    <li><a class="dropdown-item nav-admin-journeys"
                                            href="{{ url_for('journey.admin_hidden_journeys') }}"
                                            data-path="journey/manage"><i class="bi bi-eye-slash-fill me-2"></i>Hidden
                                            Journeys</a></li>
                                    <li><a class="dropdown-item nav-admin-announcements"
                                            href="{{ url_for('announcement.get_all_announcements') }}"
                                            data-path="announcement/manage"><i
                                                class="bi bi-megaphone-fill me-2"></i>Announcements</a></li>
                                    <li><a class="dropdown-item nav-admin-announcements"
                                            href="{{ url_for('report.get_reports') }}" data-path="report/manage"><i
                                                class="bi bi-flag-fill me-2"></i>Reports</a></li>
                                    <li><a class="dropdown-item nav-admin-announcements"
                                            href="{{ url_for('helpdesk.get_tickets') }}" data-path="helpdesk/manage"><i
                                                class="bi bi-gear-fill me-2"></i>Helpdesk Requests</a></li>
                                </ul>
                            </li>
                            {% endif %}
                        </ul>

                        <!-- Messenger Icon -->
                        <div class="dropdown d-none d-xl-flex align-items-center position-relative">
                            <a href="#" class="text-dark position-relative" id="messengerDropdown" role="button"
                                data-bs-toggle="dropdown" aria-expanded="false" style="text-decoration: none;">
                                <span
                                    style="display:inline-block; border-radius:50%; width:44px; height:44px; display:flex; align-items:center; justify-content:center;">
                                    <i class="bi bi-chat-dots" style="font-size: 1.5rem; color: #222;"></i>
                                </span>
                                <span id="unreadMessagesCount" style="
                                    position: absolute;
                                    top: -4px;
                                    right: -2px;
                                    background: #d1d5fa;
                                    border-radius: 50%;
                                    padding: 1px 7px;
                                    font-size: 13px;
                                    font-weight: bold;
                                    min-width: 20px;
                                    text-align: center;
                                    border: 2px solid #fff;
                                    {% if not g.unread_messages_count or g.unread_messages_count == 0 %}display: none;{% endif %}
                                ">{{ g.unread_messages_count if g.unread_messages_count else 0 }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end p-0" aria-labelledby="messengerDropdown"
                                style="min-width: 350px; border-radius: 20px; box-shadow: 0 4px 16px rgba(0,0,0,0.10);">
                                <li>
                                    <div
                                        class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
                                        <span class="fw-bold">Messages</span>
                                        <a href="{{ url_for('message.get_conversations') }}"
                                            class="small text-primary text-decoration-none">View All</a>
                                    </div>
                                </li>
                                <div id="messagesLoading" class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status"
                                        style="width: 1.5rem; height: 1.5rem;">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div id="messagesList">
                                    <!-- Messages will be loaded here -->
                                </div>
                                <li class="text-center py-2 border-top">
                                    <a href="{{ url_for('message.get_conversations') }}"
                                        class="small text-primary text-decoration-none">
                                        View All Messages
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- Notification Bell -->
                        <div class="dropdown d-none d-xl-flex align-items-center mx-3 position-relative">
                            <a href="#" class="text-dark position-relative" id="notificationDropdown" role="button"
                                data-bs-toggle="dropdown" aria-expanded="false" style="text-decoration: none;">
                                <i class="bi bi-bell" style="font-size: 1.5rem; color: #222;"></i>
                                {% if unread_notifications_count and unread_notifications_count > 0 %}
                                <span id="notificationCount" style="
                                    position: absolute;
                                    top: -4px;
                                    right: -6px;
                                    background: #d1d5fa;
                                    color: #222;
                                    border-radius: 50%;
                                    padding: 1px 7px;
                                    font-size: 13px;
                                    font-weight: bold;
                                    min-width: 20px;
                                    text-align: center;
                                ">{{ unread_notifications_count }}</span>
                                {% endif %}
                            </a>
                            <div class="dropdown-menu dropdown-menu-end p-0" aria-labelledby="notificationDropdown"
                                data-bs-popper="static" tabindex="-1">
                                <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
                                    <span class="fw-bold">Notification</span>
                                    <a href="#" class="small text-primary text-decoration-none" id="markAllReadBtn">Mark
                                        As All Read</a>
                                </div>
                                <div id="notificationLoading" class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status"
                                        style="width: 1.5rem; height: 1.5rem;">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div id="notificationList" tabindex="0">
                                </div>
                                <div class="text-center py-2 border-top">
                                    <a href="{{ url_for('main.view_all_notifications') }}"
                                        class="small text-primary text-decoration-none">
                                        View All Notifications
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- User dropdown for desktop only -->
                        <div class="d-none d-xl-flex">
                            <div class="dropdown">
                                <a class="btn fw-medium px-4 py-2 dropdown-toggle d-flex align-items-center gap-2 border"
                                    style="border-radius: 50px; background-color: var(--light-color); color: var(--dark-color);"
                                    href="#" id="userDropdown" role="button" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    {% if session.profile_image %}
                                    <img src="{{ url_for('static', filename='uploads/profile_images/' + session.profile_image) }}"
                                        alt="Profile"
                                        style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"
                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
                                    <i class="bi bi-person-circle" style="font-size: 1.4rem; display: none;"></i>
                                    {% else %}
                                    <i class="bi bi-person-circle" style="font-size: 1.4rem;"></i>
                                    {% endif %}
                                    <span class="user-dropdown-username">{{ session.username }}</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end"
                                    style="border-radius: 15px; margin-top: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                    <li><a class="dropdown-item d-flex align-items-center gap-2"
                                            href="{{ url_for('account.get_profile', active_tab='profile') }}">
                                            <i class="bi bi-person-circle me-1"></i>Account</a>
                                    </li>
                                    <li><a class="dropdown-item d-flex align-items-center gap-2"
                                            href="{{ url_for('message.get_conversations') }}" data-path="messages">
                                            <i class="bi bi-chat-dots me-1"></i>Messages
                                            <!-- {% if g.unread_messages_count and g.unread_messages_count > 0 %}
                                            <span class="badge rounded-pill bg-danger">{{ g.unread_messages_count
                                                }}</span>
                                            {% endif %} -->
                                        </a>
                                    </li>
                                    <li><a class="dropdown-item d-flex align-items-center gap-2"
                                            href="{{ url_for('report.get_my_reports') }}" data-path="report">
                                            <i class="bi bi-flag me-1"></i>My Reports</a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item d-flex align-items-center gap-2"
                                            href="{{ url_for('helpdesk.get_user_tickets') }}" data-path="helpdesk">
                                            <i class="bi bi-headset me-1"></i>My Helpdesk
                                        </a>
                                    </li>
                                    <li>
                                        <hr class="dropdown-divider">
                                    </li>
                                    <li><a class="dropdown-item d-flex align-items-center gap-2"
                                            href="{{ url_for('auth.logout') }}">
                                            <i class="bi bi-box-arrow-right me-1"></i>Logout</a></li>
                                </ul>
                            </div>
                        </div>

                        <!-- User profile menu for mobile only -->
                        <div class="d-xl-none mobile-user-menu">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                {% if session.profile_image %}
                                <img src="{{ url_for('static', filename='uploads/profile_images/' + session.profile_image) }}"
                                    alt="Profile" class="rounded-circle me-2"
                                    style="width: 48px; height: 48px; object-fit: cover;"
                                    onerror="this.style.display='none'; document.getElementById('defaultAvatar').style.display='inline-block';">
                                <i id="defaultAvatar" class="bi bi-person-circle"
                                    style="font-size: 2rem; display: none; margin-right: 10px;"></i>
                                {% else %}
                                <i class="bi bi-person-circle me-2" style="font-size: 2rem;"></i>
                                {% endif %}
                                <div>
                                    <h6 class="mb-0">{{ session.username }}</h6>
                                    <small class="text-muted">{{ session.get('role', 'traveller').title() }}</small>
                                </div>
                            </div>
                            <a href="{{ url_for('account.get_profile', active_tab='profile') }}"
                                class="mobile-menu-item">
                                <i class="bi bi-person-circle me-2"></i>Account
                            </a>
                            <a href="{{ url_for('message.get_conversations') }}" class="mobile-menu-item">
                                <i class="bi bi-chat-dots me-2"></i>Messages
                            </a>
                            <a href="{{ url_for('report.get_my_reports') }}" class="mobile-menu-item">
                                <i class="bi bi-flag me-2"></i>My Reports
                            </a>
                            <a href="{{ url_for('helpdesk.get_user_tickets') }}" class="mobile-menu-item">
                                <i class="bi bi-headset me-2"></i>My Helpdesk
                            </a>
                            <a href="{{ url_for('auth.logout') }}" class="mobile-menu-item">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
            {% else %}
            <!-- Navigation before login (with hamburger menu) -->
            <nav class="navbar navbar-expand-md custom-navbar" style="padding: 8px 20px;">
                <div class="container-fluid">
                    <a class="navbar-brand d-flex align-items-center" href="{{ url_for('main.get_landing_page') }}">
                        <img src="{{ url_for('static', filename='images/footprints_logo.png') }}" alt="Footprints"
                            height="30" class="me-2">
                        <span class="fw-bold">Footprints</span>
                    </a>

                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarLogin"
                        aria-controls="navbarLogin" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <div class="collapse navbar-collapse" id="navbarLogin">
                        <ul class="navbar-nav ms-auto mb-2 mb-md-0">
                            <li class="nav-item d-md-none">
                                <a class="nav-link px-3 mx-1" href="{{ url_for('auth.login') }}">Login</a>
                            </li>
                            <li class="nav-item d-md-none">
                                <a class="nav-link px-3 mx-1" href="{{ url_for('auth.register') }}">Register</a>
                            </li>
                        </ul>

                        <!-- Desktop auth buttons -->
                        <div class="d-none d-md-flex ms-3 gap-2">
                            <a href="{{ url_for('auth.login') }}" class="btn fw-medium px-4 py-2 border"
                                style="border-radius: 50px; color: var(--dark-color); background-color: var(--light-color);">Login</a>
                            <a href="{{ url_for('auth.register') }}" class="btn fw-medium px-4 py-2"
                                style="border-radius: 50px; background-color: var(--dark-color); color: var(--light-color);">Register</a>
                        </div>
                    </div>
                </div>
            </nav>
            {% endif %}
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-white py-2 mt-auto border-top">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <img src="{{ url_for('static', filename='images/footprints_logo.png') }}" alt="Footprints"
                        height="30">
                    <h6 class="mb-0 ms-2">Footprints</h6>
                </div>
                <div>
                    <small class="text-muted footer-copyright">©Copyright Footprints 2025. Design by BHE</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>

    <!-- Flash Messages JS -->
    <script src="{{ url_for('static', filename='js/flash-messages.js') }}"></script>

    <!-- File Validation JS -->
    <script src="{{ url_for('static', filename='js/file-validation.js') }}"></script>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <!-- Notification & Navigation -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Handle blocked user banner positioning
            const blockedBanner = document.getElementById('blockedUserBanner');
            if (blockedBanner) {
                document.body.classList.add('has-blocked-banner');

                // Remove class when banner is dismissed
                blockedBanner.addEventListener('closed.bs.alert', function () {
                    document.body.classList.remove('has-blocked-banner');
                });
            }
            // Initialize toasts
            document.querySelectorAll('.toast').forEach(toastEl => {
                const toast = new bootstrap.Toast(toastEl, {
                    animation: true,
                    autohide: true,
                    delay: 3000
                });
                toast.show();
            });

            // Handle active navigation items
            const currentPath = window.location.pathname;

            // Check nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                const pathData = link.getAttribute('data-path');
                if (pathData) {
                    if (currentPath === '/announcement/manage') {
                        return;
                    }

                    if (currentPath === '/report/manage') {
                        return;
                    }

                    if (currentPath === '/helpdesk/manage') {
                        return;
                    }

                    if (currentPath === '/' + pathData || currentPath.startsWith('/' + pathData + '/')) {
                        link.classList.add('active');
                    }
                }

                // Check for management dropdown with multiple paths
                const pathsData = link.getAttribute('data-paths');
                if (pathsData) {
                    const paths = pathsData.split(' ');
                    for (const path of paths) {
                        if (currentPath === '/' + path || currentPath.startsWith('/' + path + '/')) {
                            link.classList.add('active');
                            break;
                        }
                    }
                }
            });

            // Check dropdown items
            document.querySelectorAll('.dropdown-item').forEach(item => {
                const pathData = item.getAttribute('data-path');
                if (pathData) {
                    if (currentPath === '/' + pathData) {
                        item.classList.add('active');
                    }
                }
            });
        });

        let notificationsOffset = 0;
        const notificationsLimit = 5;
        let notificationsLoading = false;
        let notificationsEnd = false;

        // Messages functionality
        let messagesLoading = false;

        // Function to update notification count in navbar
        function updateNotificationCount() {
            fetch('/api/notifications/unread-count')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('notificationCount').innerText = data.count;
                });
        }

        // Function to update message count in navbar
        function updateMessageCount() {
            fetch('/api/messages/unread-count')
                .then(response => response.json())
                .then(data => {
                    const countElement = document.getElementById('unreadMessagesCount');
                    if (countElement) {
                        if (data.count > 0) {
                            countElement.innerText = data.count;
                            countElement.style.display = 'block';
                        } else {
                            countElement.style.display = 'none';
                        }
                    }
                })
                .catch(error => console.error('Error updating message count:', error));
        }

        // Function to fetch recent conversations for navbar dropdown
        function fetchRecentMessages() {
            if (messagesLoading) return;
            messagesLoading = true;

            const messagesList = document.getElementById('messagesList');
            const messagesLoadingElement = document.getElementById('messagesLoading');

            if (!messagesList || !messagesLoadingElement) return;

            messagesLoadingElement.style.display = 'block';
            messagesList.innerHTML = '';

            fetch('/api/messages/conversations?limit=3')
                .then(response => response.json())
                .then(data => {
                    messagesLoadingElement.style.display = 'none';
                    messagesLoading = false;

                    if (data && data.length > 0) {
                        data.forEach(conversation => {
                            const item = document.createElement('li');
                            item.innerHTML = `
                                <a href="/messages?user=${conversation.other_user_id}" class="d-flex align-items-start gap-2 px-3 py-2 border-bottom text-decoration-none text-dark" style="transition: background-color 0.2s;">
                                    ${conversation.other_user.profile_image
                                    ? `<img src="/static/uploads/profile_images/${conversation.other_user.profile_image}" alt="${conversation.other_user.username}" style="width:32px; height:32px; border-radius:50%; object-fit:cover;">`
                                    : `<div style="width:32px; height:32px; border-radius:50%; background-color:#6c757d; display:flex; align-items:center; justify-content:center; color:white; font-weight:600; font-size:0.9rem;">${conversation.other_user.username.charAt(0).toUpperCase()}</div>`
                                }
                                    <div style="flex: 1; min-width: 0;">
                                        <div class="fw-medium ${conversation.unread_count > 0 ? 'fw-bold' : ''}" style="font-size: 0.9rem;">${conversation.other_user.username}</div>
                                        <div class="text-muted small text-truncate" style="max-width: 200px;">${conversation.last_message || 'No messages yet'}</div>
                                        <div class="text-muted small">${formatTimeAgo(new Date(conversation.last_message_time))}</div>
                                    </div>
                                    ${conversation.unread_count > 0
                                    ? `<span class="badge bg-primary rounded-pill" style="font-size: 0.7rem;">${conversation.unread_count}</span>`
                                    : ''
                                }
                                </a>
                            `;

                            // Add hover effect
                            const link = item.querySelector('a');
                            link.addEventListener('mouseenter', function () {
                                this.style.backgroundColor = '#f8f9fa';
                            });
                            link.addEventListener('mouseleave', function () {
                                this.style.backgroundColor = 'transparent';
                            });

                            messagesList.appendChild(item);
                        });
                    } else {
                        messagesList.innerHTML = '<li><div class="text-center py-3 text-muted">No messages yet.</div></li>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching messages:', error);
                    messagesLoadingElement.style.display = 'none';
                    messagesLoading = false;
                    messagesList.innerHTML = '<li><div class="text-center py-3 text-danger">Failed to load messages.</div></li>';
                });
        }

        // Helper function to format time ago
        function formatTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffSec = Math.round(diffMs / 1000);
            const diffMin = Math.round(diffSec / 60);
            const diffHour = Math.round(diffMin / 60);
            const diffDay = Math.round(diffHour / 24);

            if (diffSec < 60) return 'just now';
            if (diffMin < 60) return `${diffMin}m ago`;
            if (diffHour < 24) return `${diffHour}h ago`;
            if (diffDay < 7) return `${diffDay}d ago`;

            return date.toLocaleDateString();
        }

        function fetchNotifications(append = false) {
            if (notificationsLoading || notificationsEnd) return;
            notificationsLoading = true;
            const notificationList = document.getElementById('notificationList');
            const notificationLoading = document.getElementById('notificationLoading');
            if (!notificationList || !notificationLoading) return;
            if (!append) notificationList.innerHTML = '';
            notificationLoading.style.display = 'block';

            fetch(`/notifications?limit=${notificationsLimit}&offset=${notificationsOffset}`)
                .then(response => response.json())
                .then(data => {
                    notificationLoading.style.display = 'none';
                    notificationsLoading = false;
                    if (data.success && data.notifications.length > 0) {
                        data.notifications.forEach(noti => {
                            const item = document.createElement('div');
                            item.className = 'd-flex align-items-start gap-2 px-3 py-2 border-bottom notification-item';
                            item.style.cursor = 'pointer';
                            item.innerHTML = `
                                ${!noti.is_read ? '<span class="unread-dot"></span>' : '<span style="width:10px;display:inline-block;"></span>'}
                                <div>
                                    <div class="fw-medium">${(noti.notification_type || 'Notification').charAt(0).toUpperCase() + (noti.notification_type || 'notification').slice(1).toLowerCase()}</div>
                                    <div class="text-muted small">${noti.content || ''}</div>
                                    <div class="text-muted small">${noti.formatted_time || ''}</div>
                                </div>
                            `;

                            // Add click handler with navigation to related content
                            item.addEventListener('click', function (e) {
                                e.stopPropagation();
                                if (!noti.is_read) {
                                    fetch(`/notifications/${noti.id}/read`, { method: 'POST' })
                                        .then(() => {
                                            const dot = item.querySelector('.unread-dot');
                                            if (dot) dot.style.display = 'none';
                                            noti.is_read = 1;

                                            // Update the notification count
                                            updateNotificationCount();

                                            // Navigate to related content if available
                                            if (noti.related_id) {
                                                let targetUrl = '';

                                                // Handle notifications by type first for accuracy
                                                if (noti.notification_type === 'report') {
                                                    targetUrl = `/report/detail/${noti.related_id}`;
                                                } else if (noti.notification_type === 'helpdesk' || noti.notification_type === 'appeal') {
                                                    targetUrl = `/helpdesk/query?ticket_id=${noti.related_id}&back=${encodeURIComponent('/helpdesk')}`;
                                                } else if (noti.notification_type === 'message') {
                                                    targetUrl = `/messages?user=${noti.related_id}`;
                                                } else if (noti.notification_type === 'edit') {
                                                    // For edit notifications, check content to determine target
                                                    if (noti.content.includes('event')) {
                                                        // Event edit: related_id is event_id
                                                        targetUrl = `/event/${noti.related_id}/detail`;
                                                    } else if (noti.content.includes('journey')) {
                                                        // Journey edit: related_id is journey_id
                                                        // Route to private by default - the journey service will handle access permissions
                                                        // and redirect appropriately if needed
                                                        targetUrl = `/journey/private/${noti.related_id}`;
                                                    }
                                                } else if (noti.notification_type === 'like') {
                                                    // Like notifications: related_id is always event_id
                                                    targetUrl = `/event/${noti.related_id}/detail`;
                                                } else if (noti.notification_type === 'comment') {
                                                    // All comment notifications: related_id is event_id
                                                    // This includes regular comments and hidden comment notifications
                                                    targetUrl = `/event/${noti.related_id}/detail`;
                                                } else if (noti.notification_type === 'subscription') {
                                                    window.location.href = '/account/profile?active_tab=subscription';
                                                    return;
                                                }

                                                if (targetUrl) {
                                                    window.location.href = targetUrl;
                                                }
                                            }
                                        });
                                } else {
                                    // For read notifications, navigate directly
                                    let targetUrl = '';

                                    // Handle notifications by type first for accuracy
                                    if (noti.notification_type === 'report') {
                                        targetUrl = `/report/detail/${noti.related_id}`;
                                    } else if (noti.notification_type === 'helpdesk' || noti.notification_type === 'appeal') {
                                        targetUrl = `/helpdesk/query?ticket_id=${noti.related_id}&back=${encodeURIComponent('/helpdesk')}`;
                                    } else if (noti.notification_type === 'message') {
                                        targetUrl = `/messages?user=${noti.related_id}`;
                                    } else if (noti.notification_type === 'edit') {
                                        // For edit notifications, check content to determine target
                                        if (noti.content.includes('event')) {
                                            // Event edit: related_id is event_id
                                            targetUrl = `/event/${noti.related_id}/detail`;
                                        } else if (noti.content.includes('journey')) {
                                            // Journey edit: related_id is journey_id
                                            // Route to private by default - the journey service will handle access permissions
                                            // and redirect appropriately if needed
                                            targetUrl = `/journey/private/${noti.related_id}`;
                                        }
                                    } else if (noti.notification_type === 'like') {
                                        // Like notifications: related_id is always event_id
                                        targetUrl = `/event/${noti.related_id}/detail`;
                                    } else if (noti.notification_type === 'comment') {
                                        // All comment notifications: related_id is event_id
                                        // This includes regular comments and hidden comment notifications
                                        targetUrl = `/event/${noti.related_id}/detail`;
                                    } else if (noti.notification_type === 'subscription') {
                                        window.location.href = '/account/profile?active_tab=subscription';
                                        return;
                                    }

                                    if (targetUrl) {
                                        window.location.href = targetUrl;
                                    }
                                }
                            });
                            notificationList.appendChild(item);
                        });
                        notificationsOffset += data.notifications.length;
                        if (data.notifications.length < notificationsLimit) {
                            notificationsEnd = true;
                        }
                    } else {
                        if (!append || notificationsOffset === 0) {
                            notificationList.innerHTML = '<div class="text-center py-3 text-muted">No notifications.</div>';
                        }
                        notificationsEnd = true;
                    }
                })
                .catch(() => {
                    notificationLoading.style.display = 'none';
                    notificationsLoading = false;
                    if (!append) notificationList.innerHTML = '<div class="text-center py-3 text-danger">Failed to load notifications.</div>';
                });
        }

        // Initialize messenger dropdown
        (function () {
            const messengerDropdown = document.getElementById('messengerDropdown');
            if (messengerDropdown) {
                messengerDropdown.addEventListener('show.bs.dropdown', function () {
                    fetchRecentMessages();
                });
            }
        })();

        // Initialize notification dropdown
        (function () {
            const dropdown = document.getElementById('notificationDropdown');
            if (dropdown) {
                dropdown.addEventListener('show.bs.dropdown', function () {
                    notificationsOffset = 0;
                    notificationsEnd = false;
                    fetchNotifications(false);

                    setTimeout(() => {
                        const notificationList = document.getElementById('notificationList');
                        if (notificationList) notificationList.focus();
                    }, 100);

                    // Get the dropdown menu element
                    const dropdownMenu = document.querySelector('.dropdown-menu[aria-labelledby="notificationDropdown"]');

                    // Event binding prevention
                    if (!dropdownMenu.dataset.scrollBound) {
                        // Add scroll event to the dropdown menu container
                        dropdownMenu.addEventListener('scroll', function (e) {
                            if (this.scrollTop + this.clientHeight >= this.scrollHeight - 20) {
                                fetchNotifications(true);
                            }
                        });

                        // Mark as bound to prevent multiple event bindings
                        dropdownMenu.dataset.scrollBound = '1';
                    }
                });

                // Mark all as read button
                document.getElementById('markAllReadBtn').addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    fetch('/notifications/mark-all-read', {
                        method: 'POST'
                    }).then(response => response.json())
                        .then(data => {
                            // Update the notification count to zero
                            document.getElementById('notificationCount').innerText = '0';

                            // Refresh the notification list
                            notificationsOffset = 0;
                            notificationsEnd = false;
                            fetchNotifications(false);
                        });
                });
            }
        })();
    </script>

    <!-- 하위 템플릿에서 scripts block을 덮어쓸 수 있도록 -->
    {% block scripts %}{% endblock %}
</body>

</html>