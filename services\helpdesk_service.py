from typing import Tuple, Optional, Dict, Any
from data import helpdesk_data, user_data
from utils.logger import get_logger
logger = get_logger(__name__)

def get_tickets(user_id=None, limit=10, offset=0, search='',status_filter = None,  type_filter = None):
    """Get public journeys with optional user filter, pagination and search"""
    if search:
        if user_id:
            # print(f'try to helpdesk_data.search_user_requests({user_id}, {search}, {limit}, {offset})')
            return helpdesk_data.search_user_requests(user_id, search, limit, offset)
        else:
            # print(f'try to helpdesk_data.search_open_requests({search},{limit}, {offset},{status_filter},{type_filter})')
            return helpdesk_data.search_open_requests(search, limit, offset,status_filter,type_filter)
    if user_id:
        # print(f'try to helpdesk_data.get_user_requests({user_id},{limit}, {offset})')
        return helpdesk_data.get_user_requests(user_id, limit, offset)
    
    # print(f'try to helpdesk_data.get_open_requests({limit}, {offset},{status_filter},{type_filter})')
    return helpdesk_data.get_open_requests(limit, offset,status_filter,type_filter)

def get_tickets_count(user_id=None, search='',status_filter = None,  type_filter = None):
    if user_id:
        return helpdesk_data.get_user_requests_count(user_id)
    if search:
        return helpdesk_data.search_open_requests_count(search,status_filter,type_filter)
    return helpdesk_data.get_open_requests_count(status_filter,type_filter)

def get_my_tickets(user_id, limit=10, offset=0, search='', status_filter=None, type_filter=None):
    if search:
        return helpdesk_data.search_user_requests(user_id, search, limit, offset)
    return helpdesk_data.get_user_requests(user_id, limit, offset)

def get_my_tickets_count(user_id, search='', status_filter=None, type_filter=None):
    return helpdesk_data.get_user_requests_count(user_id)

def get_assigned_tickets(staff_id, limit=10, offset=0, search='', status_filter=None, type_filter=None):
    """Get tickets assigned to a specific staff member (excluding completed tickets)"""
    # Exclude completed tickets (approved, rejected, resolved) by default
    if status_filter is None:
        # Only show active tickets: new, open, stalled
        active_statuses = ['new', 'open', 'stalled']
        status_filter = active_statuses

    if search:
        return helpdesk_data.search_assigned_requests(staff_id, search, limit, offset, status_filter)
    return helpdesk_data.get_assigned_requests(staff_id, limit, offset, status_filter)

def get_assigned_tickets_count(staff_id, search='', status_filter=None, type_filter=None):
    """Get count of tickets assigned to a specific staff member (excluding completed tickets)"""
    # Exclude completed tickets (approved, rejected, resolved) by default
    if status_filter is None:
        # Only show active tickets: new, open, stalled
        active_statuses = ['new', 'open', 'stalled']
        status_filter = active_statuses

    if search:
        return helpdesk_data.search_assigned_requests_count(staff_id, search, status_filter)
    return helpdesk_data.get_assigned_requests_count(staff_id, status_filter)

def get_unassigned_tickets(limit=10, offset=0, search='', status_filter=None, type_filter=None):
    """Get new tickets (status = 'new')"""
    # Filter for tickets with status = 'new'
    new_statuses = ['new']
    if search:
        return helpdesk_data.search_open_requests(search, limit, offset, new_statuses, type_filter)
    return helpdesk_data.get_open_requests(limit, offset, new_statuses, type_filter)

def get_unassigned_tickets_count(search='', status_filter=None, type_filter=None):
    """Get count of new tickets"""
    new_statuses = ['new']
    if search:
        return helpdesk_data.search_open_requests_count(search, new_statuses, type_filter)
    return helpdesk_data.get_open_requests_count(new_statuses, type_filter)

def get_active_tickets(limit=10, offset=0, search='', status_filter=None, type_filter=None):
    """Get active tickets (open and stalled status)"""
    active_statuses = ['open', 'stalled']
    if search:
        return helpdesk_data.search_open_requests(search, limit, offset, active_statuses, type_filter)
    return helpdesk_data.get_open_requests(limit, offset, active_statuses, type_filter)

def get_active_tickets_count(search='', status_filter=None, type_filter=None):
    """Get count of active tickets"""
    active_statuses = ['open', 'stalled']
    if search:
        return helpdesk_data.search_open_requests_count(search, active_statuses, type_filter)
    return helpdesk_data.get_open_requests_count(active_statuses, type_filter)

def get_past_tickets(limit=10, offset=0, search='', status_filter=None, type_filter=None):
    """Get past tickets (resolved, approved, rejected)"""
    past_statuses = ['resolved', 'approved', 'rejected']
    if search:
        return helpdesk_data.search_open_requests(search, limit, offset, past_statuses, type_filter)
    return helpdesk_data.get_open_requests(limit, offset, past_statuses, type_filter)

def get_past_tickets_count(search='', status_filter=None, type_filter=None):
    """Get count of past tickets"""
    past_statuses = ['resolved', 'approved', 'rejected']
    if search:
        return helpdesk_data.search_open_requests_count(search, past_statuses, type_filter)
    return helpdesk_data.get_open_requests_count(past_statuses, type_filter)

def add_ticket(user_id: int, subject: str, category: str, description: str, username: str = None):
    try:
        # Validate inputs
        if not subject or not subject.strip():
            logger.warning(f"User {user_id} attempted to create ticket with empty subject")
            return False, "Ticket subject cannot be empty", None

        if not description or not description.strip():
            logger.warning(f"User {user_id} attempted to create ticket with empty content")
            return False, "Ticket content cannot be empty", None

        # Special handling for ban appeals
        if category == 'appeal':
            if not username or not username.strip():
                return False, "Username is required for ban appeals", None

            # Validate that the username exists and is banned
            user = user_data.get_user_by_username(username.strip())
            if not user:
                return False, "Username not found", None

            if not user.get('is_banned', False):
                return False, "This account is not currently banned", None

            # Check for existing pending ban appeal
            if helpdesk_data.has_pending_ban_appeal(user['id']):
                return False, "There is already a pending ban appeal for this account", None

            # Create ban appeal ticket
            appeal_subject = f"Ban Appeal for Account: {username.strip()}"
            appeal_description = f"Username: {username.strip()}\nUser ID: {user['id']}\n\nAppeal Reason:\n{description.strip()}"

            ticket_id = helpdesk_data.create_request(
                user_id=user['id'],  # Set the banned user as the ticket owner
                subject=appeal_subject,
                description=appeal_description,
                request_type='appeal',
                appeal_type='ban',
                related_id=user['id']
            )

            logger.info(f"Ban appeal created for user {user['id']} ({username}): ticket {ticket_id}")
            return True, "Ban appeal submitted successfully", ticket_id

        # Allow guest user (None or -1) to skip user check for regular tickets
        if user_id not in (None, -1):
            user = user_data.get_user_by_id(user_id)
            if not user:
                logger.warning(f"Attempt to create ticket for non-existent user ID: {user_id}")
                return False, "User not found", None

        # Create regular ticket
        ticket_id = helpdesk_data.create_request(
            user_id=None if user_id in (None, -1) else user_id,
            subject=subject.strip(),
            description=description.strip(),
            request_type=category.strip()
        )

        logger.info(f"User {user_id} created ticket {ticket_id}: '{subject}'")
        return True, "Ticket created successfully", ticket_id

    except Exception as e:
        logger.error(f"Error creating ticket for user {user_id}: {str(e)}")
        return False, f"Ticket creation failed: {str(e)}", None

    

def query_ticket_by_id(ticket_id):
    try:
        # Validate inputs
        if not ticket_id:
            return False, "Ticket id cannot be empty", None
        
        ticket = helpdesk_data.get_request_by_id(ticket_id)

        staff = helpdesk_data.get_staff_by_request_id(ticket_id)
        if staff:
            ticket['staff_name']=staff['username']

        replies=helpdesk_data.get_request_replies(ticket_id,True)

        if replies:
            ticket['replies']=replies
        
        
    except Exception as e:
        logger.error(f"Error view ticket by id {ticket_id}: {str(e)}")
        return False, f"Ticket query failed: {str(e)}", None
    
    return True, "Ticket query successfully", ticket

def query_staff():

    staff_list = user_data.get_staff_accounts()

    return staff_list


def add_reply_to_ticket(userId,ticket_id,content):
    try:
        # Validate inputs
        if not userId:
            return False, "User id cannot be empty", None
        
        if not ticket_id:
            return False, "Ticket id cannot be empty", None
        
        if not content or not content.strip():
            return False, "Content cannot be empty", None
        
        reply_id = helpdesk_data.add_reply(ticket_id,userId,content,True)

    except Exception as e:
        logger.error(f"Error add reply to ticket {ticket_id}: {str(e)}")
        return False, f"Reply add failed: {str(e)}", None
    
    return True, "Reply add successfully", reply_id

def assign_ticket(ticket_id, staff_id):
    try:
        # Validate inputs
        if not staff_id:
            return False, "Staff id cannot be empty", None
        
        if not ticket_id:
            return False, "Ticket id cannot be empty", None
        
        rows_affected = helpdesk_data.assign_request(ticket_id,staff_id)

    except Exception as e:
        logger.error(f"Error assign ticket {ticket_id}: {str(e)}")
        return False, f"Ticket assign failed: {str(e)}", None
    
    return True, "Ticket assign successfully", rows_affected

def abandon_ticket(ticket_id):
    try:
        # Validate inputs
        if not ticket_id:
            return False, "Ticket id cannot be empty", None

        rows_affected = helpdesk_data.unassign_request(ticket_id)

        rows_affected = helpdesk_data.update_request_status(ticket_id,'new')

    except Exception as e:
        logger.error(f"Error abandon ticket {ticket_id}: {str(e)}")
        return False, f"Ticket abandon failed: {str(e)}", None

    return True, "Ticket abandon successfully", rows_affected

def take_ticket(ticket_id, staff_id):
    """Allow staff to take an unassigned ticket"""
    try:
        # Validate inputs
        if not ticket_id:
            return False, "Ticket id cannot be empty", None

        if not staff_id:
            return False, "Staff id cannot be empty", None

        # Check if ticket is unassigned
        ticket = helpdesk_data.get_request_by_id(ticket_id)
        if not ticket:
            return False, "Ticket not found", None

        if ticket.get('assigned_to'):
            return False, "Ticket is already assigned", None

        rows_affected = helpdesk_data.assign_request(ticket_id, staff_id)

    except Exception as e:
        logger.error(f"Error taking ticket {ticket_id}: {str(e)}")
        return False, f"Ticket take failed: {str(e)}", None

    return True, "Ticket taken successfully", rows_affected

def drop_ticket(ticket_id):
    """Allow staff to drop an assigned ticket back to the queue"""
    try:
        # Validate inputs
        if not ticket_id:
            return False, "Ticket id cannot be empty", None

        # Check if ticket is assigned
        ticket = helpdesk_data.get_request_by_id(ticket_id)
        if not ticket:
            return False, "Ticket not found", None

        if not ticket.get('assigned_to'):
            return False, "Ticket is not assigned", None

        rows_affected = helpdesk_data.unassign_request(ticket_id)

        # Set status back to new if it was open
        if ticket.get('status') == 'open':
            helpdesk_data.update_request_status(ticket_id, 'new')

    except Exception as e:
        logger.error(f"Error dropping ticket {ticket_id}: {str(e)}")
        return False, f"Ticket drop failed: {str(e)}", None

    return True, "Ticket dropped successfully", rows_affected

def change_ticket_status(ticket_id, status):

    try:
        # Validate inputs
        if not ticket_id:
            return False, "Ticket id cannot be empty", None
        
        if not status:
            return False, "Status cannot be empty", None
        
        rows_affected = helpdesk_data.update_request_status(ticket_id,status)

    except Exception as e:
        logger.error(f"Error change ticket status {ticket_id}: {str(e)}")
        return False, f"Ticket status change failed: {str(e)}", None
    
    return True, "Ticket status change successfully", rows_affected

def get_request_type_enum_values() -> list:
    return helpdesk_data.get_enum_values('helpdesk_requests', 'request_type')

# ===== Ban Appeal Functions =====

def process_ban_appeal(appeal_id: int, staff_id: int, action: str, response: str = None) -> Tuple[bool, str]:
    """Process a ban appeal (approve or reject).

    Args:
        appeal_id: ID of the appeal
        staff_id: ID of the staff member processing
        action: 'approve' or 'reject'
        response: Optional response message

    Returns:
        Tuple of (success, message)
    """
    try:
        # Validate inputs
        if action not in ['approve', 'reject']:
            return False, "Invalid action. Must be 'approve' or 'reject'"

        # Get the appeal
        appeal = helpdesk_data.get_appeal_request(appeal_id)
        if not appeal:
            return False, "Appeal not found"

        if appeal['appeal_type'] != 'ban':
            return False, "This is not a ban appeal"

        if appeal['status'] not in ['new', 'open']:
            return False, "Appeal has already been processed"

        # Check if appeal is assigned and if current staff is the assignee
        if not appeal.get('assigned_to'):
            return False, "Appeal must be assigned to a staff member before it can be processed"

        if appeal['assigned_to'] != staff_id:
            return False, "Only the assigned staff member can process this appeal"

        # Get user details
        user_id = appeal['related_id']
        from data import user_data
        user = user_data.get_user_by_id(user_id)
        if not user:
            return False, "Associated user not found"

        # Process the appeal
        if action == 'approve':
            # Unban the user
            rows_affected = user_data.update_user_ban_status(user_id, False)
            if rows_affected == 0:
                return False, "Failed to unban user"

            # Update appeal status
            status = 'approved'
            admin_response = response or "Your ban appeal has been approved and your account has been unbanned."

        else:  # reject
            status = 'rejected'
            admin_response = response or "Your ban appeal has been reviewed and rejected."

        # Update appeal status
        helpdesk_data.update_appeal_request_status(appeal_id, staff_id, status, admin_response)

        # Send notification to user (if they can receive it)
        try:
            from services import notification_service
            action_text = "approved" if action == 'approve' else "rejected"
            notification_content = f"Your ban appeal has been {action_text}."

            # Only send notification if user is unbanned (for approved appeals)
            if action == 'approve':
                success, message, notification_id = notification_service.create_notification(
                    user_id=appeal['user_id'],
                    notification_type='appeal',
                    content=notification_content,
                    related_id=appeal_id
                )

                if not success:
                    logger.warning(f"Failed to send notification for ban appeal {appeal_id}: {message}")

        except Exception as e:
            logger.error(f"Error sending notification for ban appeal {appeal_id}: {str(e)}")
            # Don't fail the main operation if notification fails

        logger.info(f"Staff {staff_id} {action}ed ban appeal {appeal_id} for user {user_id}")
        return True, f"Ban appeal {action}ed successfully"

    except Exception as e:
        logger.error(f"Error processing ban appeal {appeal_id}: {str(e)}")
        return False, f"Ban appeal processing failed: {str(e)}"

def create_banned_user_appeal(user_id: int, reason: str) -> Tuple[bool, str, int]:
    """Create a ban appeal for a banned user.

    Args:
        user_id: ID of the banned user
        reason: Reason for the appeal

    Returns:
        Tuple of (success, message, appeal_id)
    """
    try:
        # Validate inputs
        if not reason or not reason.strip():
            return False, "Appeal reason cannot be empty", None

        # Get user details
        from data import user_data
        user = user_data.get_user_by_id(user_id)
        if not user:
            return False, "User not found", None

        if not user.get('is_banned', False):
            return False, "This account is not currently banned", None

        # Check for existing pending ban appeal
        if helpdesk_data.has_pending_ban_appeal(user_id):
            return False, "There is already a pending ban appeal for this account", None

        # Create ban appeal ticket
        appeal_subject = f"Ban Appeal for Account: {user['username']}"
        appeal_description = f"Username: {user['username']}\nUser ID: {user_id}\n\nAppeal Reason:\n{reason.strip()}"

        ticket_id = helpdesk_data.create_request(
            user_id=user_id,
            subject=appeal_subject,
            description=appeal_description,
            request_type='appeal',
            appeal_type='ban',
            related_id=user_id
        )

        logger.info(f"Ban appeal created for user {user_id} ({user['username']}): ticket {ticket_id}")
        return True, "Ban appeal submitted successfully", ticket_id

    except Exception as e:
        logger.error(f"Error creating ban appeal for user {user_id}: {str(e)}")
        return False, f"Ban appeal creation failed: {str(e)}", None

# ===== Journey Appeal Functions =====

def create_journey_appeal(user_id: int, journey_id: int, reason: str) -> Tuple[bool, str, Optional[int]]:
    """Create an appeal for a hidden journey.

    Args:
        user_id: ID of the user creating the appeal
        journey_id: ID of the hidden journey
        reason: Reason for the appeal

    Returns:
        Tuple of (success, message, appeal_id)
    """
    try:
        # Validate inputs
        if not user_id:
            return False, "User ID is required", None

        if not journey_id:
            return False, "Journey ID is required", None

        if not reason or not reason.strip():
            return False, "Appeal reason is required", None

        # Check if journey exists and is hidden
        from data import journey_data
        journey = journey_data.get_journey(journey_id)
        if not journey:
            return False, "Journey not found", None

        if not journey.get('is_hidden', False):
            return False, "Journey is not hidden", None

        # Check if user is the journey owner
        if journey['user_id'] != user_id:
            return False, "You can only appeal your own journeys", None

        # Check for existing pending appeal
        if helpdesk_data.has_pending_journey_appeal(user_id, journey_id):
            return False, "You already have a pending appeal for this journey", None

        # Create the appeal
        subject = f"Appeal for Hidden Journey: {journey['title']}"
        description = f"Journey ID: {journey_id}\nJourney Title: {journey['title']}\n\nReason for Appeal:\n{reason.strip()}"

        appeal_id = helpdesk_data.create_appeal_request(
            user_id=user_id,
            appeal_type='hidden_journey',
            related_id=journey_id,
            subject=subject,
            description=description
        )

        logger.info(f"User {user_id} created appeal {appeal_id} for journey {journey_id}")
        return True, "Appeal submitted successfully", appeal_id

    except Exception as e:
        logger.error(f"Error creating journey appeal for user {user_id}, journey {journey_id}: {str(e)}")
        return False, f"Appeal creation failed: {str(e)}", None


def create_blocked_user_appeal(user_id: int, reason: str) -> Tuple[bool, str, Optional[int]]:
    """Create an appeal for a blocked user.

    Args:
        user_id: ID of the user creating the appeal
        reason: Reason for the appeal

    Returns:
        Tuple of (success, message, appeal_id)
    """
    try:
        # Validate inputs
        if not user_id:
            return False, "User ID is required", None

        if not reason or not reason.strip():
            return False, "Appeal reason is required", None

        # Check if user exists and is blocked
        from data import user_data
        user = user_data.get_user_by_id(user_id)
        if not user:
            return False, "User not found", None

        if not user.get('is_blocked', False):
            return False, "User is not blocked from sharing", None

        # Check for existing pending appeal
        if helpdesk_data.has_pending_blocked_user_appeal(user_id):
            return False, "You already have a pending appeal for your blocked status", None

        # Create the appeal
        subject = f"Appeal for Blocked User Status: {user['username']}"
        description = f"User ID: {user_id}\nUsername: {user['username']}\n\nReason for Appeal:\n{reason.strip()}"

        appeal_id = helpdesk_data.create_appeal_request(
            user_id=user_id,
            appeal_type='sharing_block',
            related_id=user_id,
            subject=subject,
            description=description
        )

        logger.info(f"User {user_id} created blocked user appeal {appeal_id}")
        return True, "Appeal submitted successfully", appeal_id

    except Exception as e:
        logger.error(f"Error creating blocked user appeal for user {user_id}: {str(e)}")
        return False, f"Appeal creation failed: {str(e)}", None

def get_journey_appeal_status(user_id: int, journey_id: int) -> Optional[Dict[str, Any]]:
    """Get the appeal status for a user's journey.

    Args:
        user_id: ID of the user
        journey_id: ID of the journey

    Returns:
        Dict with appeal status or None if no appeal exists
    """
    try:
        return helpdesk_data.get_journey_appeal_status(user_id, journey_id)
    except Exception as e:
        logger.error(f"Error getting appeal status for user {user_id}, journey {journey_id}: {str(e)}")
        return None

def process_journey_appeal(appeal_id: int, staff_id: int, action: str, response: str = None) -> Tuple[bool, str]:
    """Process a journey appeal (approve or reject).

    Args:
        appeal_id: ID of the appeal
        staff_id: ID of the staff member processing
        action: 'approve' or 'reject'
        response: Optional response message

    Returns:
        Tuple of (success, message)
    """
    try:
        # Validate inputs
        if action not in ['approve', 'reject']:
            return False, "Invalid action. Must be 'approve' or 'reject'"

        # Get the appeal
        appeal = helpdesk_data.get_appeal_request(appeal_id)
        if not appeal:
            return False, "Appeal not found"

        if appeal['appeal_type'] != 'hidden_journey':
            return False, "This is not a journey appeal"

        if appeal['status'] not in ['new', 'open']:
            return False, "Appeal has already been processed"

        # Check if appeal is assigned and if current staff is the assignee
        if not appeal.get('assigned_to'):
            return False, "Appeal must be assigned to a staff member before it can be processed"

        if appeal['assigned_to'] != staff_id:
            return False, "Only the assigned staff member can process this appeal"

        # Get journey details
        journey_id = appeal['related_id']
        from data import journey_data
        journey = journey_data.get_journey(journey_id)
        if not journey:
            return False, "Associated journey not found"

        # Process the appeal
        if action == 'approve':
            # Unhide the journey
            rows_affected = helpdesk_data.unhide_journey(journey_id)
            if rows_affected == 0:
                return False, "Failed to unhide journey"

            # Update appeal status
            status = 'approved'
            admin_response = response or "Your appeal has been approved and your journey has been made visible again."

        else:  # reject
            status = 'rejected'
            admin_response = response or "Your appeal has been reviewed and rejected."

        # Update appeal status
        helpdesk_data.update_appeal_request_status(appeal_id, staff_id, status, admin_response)

        # Send notification to user
        try:
            from services import notification_service
            action_text = "approved" if action == 'approve' else "rejected"
            notification_content = f"Your appeal for journey '{journey['title']}' has been {action_text}."

            success, message, notification_id = notification_service.create_notification(
                user_id=appeal['user_id'],
                notification_type='appeal',
                content=notification_content,
                related_id=appeal_id
            )

            if not success:
                logger.warning(f"Failed to send notification for appeal {appeal_id}: {message}")

        except Exception as e:
            logger.error(f"Error sending notification for appeal {appeal_id}: {str(e)}")
            # Don't fail the main operation if notification fails

        logger.info(f"Staff {staff_id} {action}ed appeal {appeal_id} for journey {journey_id}")
        return True, f"Appeal {action}ed successfully"

    except Exception as e:
        logger.error(f"Error processing appeal {appeal_id}: {str(e)}")
        return False, f"Appeal processing failed: {str(e)}"


def process_blocked_user_appeal(appeal_id: int, staff_id: int, action: str, response: str = None) -> Tuple[bool, str]:
    """Process a blocked user appeal (approve or reject).

    Args:
        appeal_id: ID of the appeal
        staff_id: ID of the staff member processing
        action: 'approve' or 'reject'
        response: Optional response message

    Returns:
        Tuple of (success, message)
    """
    try:
        # Validate inputs
        if action not in ['approve', 'reject']:
            return False, "Invalid action. Must be 'approve' or 'reject'"

        # Get the appeal
        appeal = helpdesk_data.get_appeal_request(appeal_id)
        if not appeal:
            return False, "Appeal not found"

        if appeal['appeal_type'] != 'sharing_block':
            return False, "This is not a blocked user appeal"

        if appeal['status'] not in ['new', 'open']:
            return False, "Appeal has already been processed"

        # Check if appeal is assigned and if current staff is the assignee
        if not appeal.get('assigned_to'):
            return False, "Appeal must be assigned to a staff member before it can be processed"

        if appeal['assigned_to'] != staff_id:
            return False, "Only the assigned staff member can process this appeal"

        # Get user details
        user_id = appeal['related_id']
        from data import user_data
        user = user_data.get_user_by_id(user_id)
        if not user:
            return False, "Associated user not found"

        # Process the appeal
        if action == 'approve':
            # Unblock the user
            rows_affected = helpdesk_data.unblock_user_sharing(user_id)
            if rows_affected == 0:
                return False, "Failed to unblock user"

            # Update appeal status
            status = 'approved'
            admin_response = response or "Your appeal has been approved and you can now share journeys again."

        else:  # reject
            status = 'rejected'
            admin_response = response or "Your appeal has been reviewed and rejected."

        # Update appeal status
        helpdesk_data.update_appeal_request_status(appeal_id, staff_id, status, admin_response)

        # Send notification to user
        try:
            from services import notification_service
            action_text = "approved" if action == 'approve' else "rejected"
            notification_content = f"Your appeal for blocked user status has been {action_text}."

            success, message, notification_id = notification_service.create_notification(
                user_id=appeal['user_id'],
                notification_type='appeal',
                content=notification_content,
                related_id=appeal_id
            )

            if not success:
                logger.warning(f"Failed to send notification for blocked user appeal {appeal_id}: {message}")

        except Exception as e:
            logger.error(f"Error sending notification for blocked user appeal {appeal_id}: {str(e)}")
            # Don't fail the main operation if notification fails

        logger.info(f"Staff {staff_id} {action}ed blocked user appeal {appeal_id} for user {user_id}")
        return True, f"Appeal {action}ed successfully"

    except Exception as e:
        logger.error(f"Error processing blocked user appeal {appeal_id}: {str(e)}")
        return False, f"Appeal processing failed: {str(e)}"


def get_journey_appeal_status(user_id: int, journey_id: int) -> Optional[Dict[str, Any]]:
    """Get the latest appeal status for a journey by a user.

    Args:
        user_id: ID of the user who made the appeal
        journey_id: ID of the journey being appealed

    Returns:
        Dict with appeal status information or None if no appeal found
    """
    try:
        # Get the most recent appeal for this journey by this user
        appeal = helpdesk_data.get_journey_appeal_status(user_id, journey_id)
        return appeal
    except Exception as e:
        logger.error(f"Error getting appeal status for user {user_id}, journey {journey_id}: {str(e)}")
        return None