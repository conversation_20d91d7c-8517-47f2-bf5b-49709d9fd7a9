#!/usr/bin/env python3
"""
Template Function Analyzer

This script analyzes the actual JavaScript functions in your event templates
and provides specific recommendations for cleanup.
"""

import re
import os
from typing import Dict, List, Set


def find_javascript_functions_in_template(file_path: str) -> Dict[str, List[str]]:
    """Find actual JavaScript functions in a template file"""
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    results = {
        'function_declarations': [],
        'onclick_functions': [],
        'event_handlers': [],
        'script_blocks': [],
        'inline_scripts': []
    }
    
    # Find script blocks
    script_blocks = re.findall(r'<script[^>]*>(.*?)</script>', content, re.DOTALL | re.IGNORECASE)
    results['script_blocks'] = script_blocks
    
    # Find function declarations in script blocks
    for script in script_blocks:
        # Function declarations: function functionName() or const functionName = function()
        func_declarations = re.findall(r'function\s+(\w+)\s*\(', script)
        const_func_declarations = re.findall(r'(?:const|let|var)\s+(\w+)\s*=\s*function', script)
        arrow_func_declarations = re.findall(r'(?:const|let|var)\s+(\w+)\s*=\s*\([^)]*\)\s*=>', script)
        
        results['function_declarations'].extend(func_declarations)
        results['function_declarations'].extend(const_func_declarations)
        results['function_declarations'].extend(arrow_func_declarations)
    
    # Find onclick handlers
    onclick_handlers = re.findall(r'onclick=["\']([^"\']*)["\']', content, re.IGNORECASE)
    for handler in onclick_handlers:
        # Extract function name from onclick
        func_match = re.search(r'(\w+)\s*\(', handler)
        if func_match:
            results['onclick_functions'].append(func_match.group(1))
    
    # Find other event handlers
    event_patterns = [
        r'onchange=["\']([^"\']*)["\']',
        r'onsubmit=["\']([^"\']*)["\']',
        r'onload=["\']([^"\']*)["\']',
        r'onfocus=["\']([^"\']*)["\']',
        r'onblur=["\']([^"\']*)["\']'
    ]
    
    for pattern in event_patterns:
        handlers = re.findall(pattern, content, re.IGNORECASE)
        for handler in handlers:
            func_match = re.search(r'(\w+)\s*\(', handler)
            if func_match:
                results['event_handlers'].append(func_match.group(1))
    
    return results


def analyze_event_templates():
    """Analyze event templates for JavaScript functions"""
    
    templates = {
        'detail': 'templates/event/detail.html',
        'edit': 'templates/event/edit.html'
    }
    
    print("🔍 Analyzing Event Templates for JavaScript Functions")
    print("=" * 60)
    
    all_functions = set()
    template_analysis = {}
    
    for template_name, template_path in templates.items():
        print(f"\n📄 Analyzing {template_name}.html...")
        
        functions = find_javascript_functions_in_template(template_path)
        template_analysis[template_name] = functions
        
        print(f"   Function declarations: {len(functions['function_declarations'])}")
        print(f"   Onclick functions: {len(functions['onclick_functions'])}")
        print(f"   Event handlers: {len(functions['event_handlers'])}")
        print(f"   Script blocks: {len(functions['script_blocks'])}")
        
        # Collect all unique functions
        all_functions.update(functions['function_declarations'])
        all_functions.update(functions['onclick_functions'])
        all_functions.update(functions['event_handlers'])
        
        # Show specific functions found
        if functions['function_declarations']:
            print(f"   📝 Function declarations: {', '.join(functions['function_declarations'][:10])}")
            if len(functions['function_declarations']) > 10:
                print(f"      ... and {len(functions['function_declarations']) - 10} more")
        
        if functions['onclick_functions']:
            print(f"   🖱️  Onclick functions: {', '.join(set(functions['onclick_functions']))}")
    
    return template_analysis, all_functions


def generate_specific_recommendations(template_analysis: Dict, all_functions: Set[str]):
    """Generate specific cleanup recommendations"""
    
    print(f"\n📊 ANALYSIS SUMMARY")
    print("=" * 40)
    print(f"Total unique functions found: {len(all_functions)}")
    
    # Common functions that are likely to be used
    likely_used = {
        'smartBack', 'confirmDeleteEvent', 'confirmDeleteComment',
        'showModal', 'hideModal', 'closeModal'
    }
    
    # Functions that might be unused
    potentially_unused = all_functions - likely_used
    
    print(f"\n🎯 CLEANUP RECOMMENDATIONS")
    print("=" * 40)
    
    print(f"\n✅ Functions likely in use ({len(likely_used & all_functions)} found):")
    for func in sorted(likely_used & all_functions):
        print(f"   - {func}")
    
    print(f"\n🔍 Functions to investigate ({len(potentially_unused)} found):")
    for func in sorted(potentially_unused):
        print(f"   - {func}")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Use the browser debug tool at: http://127.0.0.1:5000/debug/function-tracker")
    print("2. Navigate to event pages and interact with all features")
    print("3. Check which functions are actually called")
    print("4. Search for unused functions in templates:")
    
    for func in sorted(list(potentially_unused)[:10]):
        print(f"   grep -n 'function {func}\\|{func}(' templates/event/")
    
    print(f"\n💡 MANUAL TESTING SUGGESTIONS:")
    print("- Test event detail page: like buttons, comments, image gallery")
    print("- Test event edit modal: form validation, location changes")
    print("- Test with different user roles: owner, staff, visitor")
    print("- Test error conditions and edge cases")
    
    return {
        'total_functions': len(all_functions),
        'likely_used': list(likely_used & all_functions),
        'potentially_unused': list(potentially_unused),
        'recommendations': [
            'Use browser debug tool for real-time tracking',
            'Test all user interactions manually',
            'Search for unused functions in templates',
            'Remove unused code gradually with testing'
        ]
    }


def search_for_function_usage(function_name: str, template_paths: List[str]):
    """Search for specific function usage in templates"""
    
    print(f"\n🔍 Searching for '{function_name}' usage:")
    
    patterns = [
        f'function {function_name}\\(',
        f'{function_name}\\(',
        f'onclick="{function_name}\\(',
        f"onclick='{function_name}\\(",
        f'"{function_name}"',
        f"'{function_name}'"
    ]
    
    for template_path in template_paths:
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            found_lines = []
            for i, line in enumerate(lines, 1):
                for pattern in patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        found_lines.append((i, line.strip()))
                        break
            
            if found_lines:
                print(f"   📄 {template_path}:")
                for line_num, line_content in found_lines[:5]:  # Show first 5 matches
                    print(f"      Line {line_num}: {line_content[:80]}...")
            else:
                print(f"   📄 {template_path}: Not found")


if __name__ == "__main__":
    # Analyze templates
    template_analysis, all_functions = analyze_event_templates()
    
    # Generate recommendations
    recommendations = generate_specific_recommendations(template_analysis, all_functions)
    
    # Example: Search for specific function
    print(f"\n" + "=" * 60)
    print("EXAMPLE: Searching for specific functions...")
    
    template_paths = ['templates/event/detail.html', 'templates/event/edit.html']
    
    # Search for a few example functions
    example_functions = ['smartBack', 'confirmDeleteEvent', 'showModal']
    for func in example_functions:
        if func in all_functions:
            search_for_function_usage(func, template_paths)
    
    print(f"\n✅ Analysis complete! Use the debug tool for real-time tracking.")
    print(f"🌐 Debug tool: http://127.0.0.1:5000/debug/function-tracker")
