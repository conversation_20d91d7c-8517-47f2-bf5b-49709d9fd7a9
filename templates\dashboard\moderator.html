{% extends "base.html" %}

{% block title %}Moderator Dashboard - Footprints{% endblock %}

{% block content %}
<div class="container py-2">
    <!-- Welcome Section -->
    <div class="mb-4 p-4"
        style="border-radius: 20px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);">
        <div class="d-flex align-items-center">
            <div>
                <h1 class="h3 fw-bold mb-1">Welcome back, {{ session.username }}!</h1>
                <p class="text-muted mb-0 small">Ready to manage your moderator tasks?</p>
            </div>
        </div>
    </div>

    <!-- Announcements Section - Displays both read and unread announcements in tabs -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">New Announcements</h2>
            <div class="d-flex align-items-center">
                <a href="{{ url_for('announcement.get_user_unread_announcements') }}"
                    class="btn btn-outline-dark rounded-pill px-4">View All</a>
            </div>
        </div>

        <div class="tab-content" id="announcementTabsContent">
            <!-- Unread Announcements Tab -->
            <div class="tab-pane fade show active" id="unread-tab-pane" role="tabpanel" aria-labelledby="unread-tab"
                tabindex="0">
                {% if unread_announcements|length > 0 %}
                <div class="row g-4">
                    {% for announcement in unread_announcements[:3] %}
                    <div class="col-lg-4">
                        <div class="card h-100 border-0 shadow-sm rounded-4 announcement-card">
                            <div class="card-body p-4">
                                <span class="ms-auto text-muted small">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    {{ announcement['created_at'].strftime('%B %d, %Y') }}
                                </span>
                                <h3 class="h5 fw-bold mb-3">{{ announcement['title'] }}</h3>
                                <p class="text-muted mb-3 small announcement-content">{{ announcement['content'][:100]
                                    }}{% if
                                    announcement['content']|length > 100 %}...{% endif %}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="#" class="text-decoration-none read-more-link"
                                        data-title="{{ announcement['title'] }}"
                                        data-content="{{ announcement['content'] }}"
                                        data-date="{{ announcement['created_at'].strftime('%B %d, %Y') }}"
                                        data-id="{{ announcement['id'] }}"
                                        data-form-id="markAsReadForm_{{ announcement['id'] }}">
                                        Read more
                                    </a>
                                    <form method="POST"
                                        action="/announcement/{{ announcement['id'] }}/mark-dashboard-read"
                                        id="markAsReadForm_{{ announcement['id'] }}" style="display: none;"></form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert rounded-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle-fill me-3 fs-4"></i>
                        <p class="mb-0">You're all caught up! No unread announcements at the moment.</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Comment Reports Section -->
    <div class="section-header">
        <div class="d-flex align-items-center gap-2 mb-0">
            <h2>
                Comment Reports </h2>
            {% if comment_reports|length > 0 %}
            <span class="badge-sm">{{ comment_reports|length }} new</span>
            {% endif %}

        </div>
        <a href="{{ url_for('report.get_reports') }}" class="btn btn-outline-dark rounded-pill px-4">View All
        </a>
    </div>

    <div class="reports-section mb-4">

        {% if comment_reports|length > 0 %}
        <div class="reports-grid" style="max-height: 400px; overflow-y: auto;">
            {% for report in comment_reports %}
            <div class="report-card">
                <div class="report-content">
                    <div class="comment-section">
                        <h5>Comment</h5>
                        <div class="comment-header">
                            {% if report.commenter_profile_image %}
                            <img src="{{ url_for('static', filename='uploads/profile_images/' + report.commenter_profile_image) }}"
                                class="comment-avatar" alt="User" />
                            {% else %}
                            <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                                class="comment-avatar" alt="User" />
                            {% endif %}
                            <div>
                                <div class="comment-username">{{ report.commenter_username }}</div>
                                <div class="comment-timestamp">
                                    <i class="bi bi-clock"></i>
                                    {{ report.comment_created_at }}
                                </div>
                            </div>
                        </div>
                        <p class="comment-text">{{ report.comment_content }}</p>
                        <div class="comment-stats">
                            <span class="comment-stat">
                                <i class="bi bi-hand-thumbs-up"></i>
                                {{ report.comment_like_count|default(0) }}
                            </span>
                            <span class="comment-stat">
                                <i class="bi bi-hand-thumbs-down"></i>
                                {{ report.comment_dislike_count|default(0) }}
                            </span>
                        </div>
                        <a href="{{ url_for('report.get_report_details', report_id=report.id) }}"
                            class="view-comment-btn">
                            View Details
                        </a>
                    </div>

                    <div class="report-section">
                        <h5>Report</h5>
                        <div class="reporter-info">
                            <div class="reporter-name">@{{ report.reporter_username }}</div>
                            <div class="report-timestamp">
                                <i class="bi bi-clock"></i>
                                {{ report.created_at.strftime('%b %d, %Y • %I:%M %p') }}
                            </div>
                        </div>

                        <div class="report-reason">
                            <div class="reason-label">
                                <i class="bi bi-chat-quote text-primary"></i>
                                Report Reason:
                            </div>
                            <p class="reason-text">{{ report.reason }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <i class="bi bi-check-circle-fill empty-icon"></i>
            <h3 class="empty-title">No New Reports Found</h3>
            <p class="empty-description">Great job! There are no new comment reports to review at the moment.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
    .badge-sm {
        font-size: 1rem;
        padding: 0.35em 0.6em;
        background: black;
        color: white;
        border-radius: 5rem;
    }

    .btn-dark:hover {
        background-color: transparent !important;
        color: #212529 !important;
        border: 1px solid #212529 !important;
    }

    .announcement-card:hover {
        transform: translateY(-3px);
        transition: transform 0.3s ease;
    }

    .announcement-card {
        background: linear-gradient(135deg, #f0f4ff, #e0eaff);
        border: 1px solid #dce3f0;
    }

    /* Comment Reports Styling */
    .reports-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid #e9ecef;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #212529;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }


    /* Report Cards */
    .reports-grid {
        display: grid;
        gap: 1.5rem;
    }

    .report-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .report-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #dee2e6;
    }

    .report-content {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 1.5rem;
    }

    /* Comment Section */
    .comment-section {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f7f9ff, #fafcff, #ffffff);
        border-right: 1px solid #e9ecef;
    }

    .comment-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .comment-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .comment-username {
        font-weight: 600;
        color: #212529;
        font-size: 0.875rem;
    }

    .comment-timestamp {
        color: #6c757d;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .comment-text {
        color: #495057;
        font-size: 0.875rem;
        line-height: 1.5;
        margin: 0.75rem 0;
    }

    .comment-stats {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .comment-stat {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: #6c757d;
        font-size: 0.75rem;
        background: white;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .view-comment-btn {
        background: #212529;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-top: 1rem;
        text-decoration: none;
        display: inline-block;
    }

    .view-comment-btn:hover {
        background: #495057;
        color: white;
        transform: translateY(-1px);
    }

    /* Report Section */
    .report-section {
        padding: 1.5rem;
        background: white;
        min-width: 400px;
    }

    .report-badges {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
    }

    .report-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .badge-escalated {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .badge-hidden {
        background: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    .reporter-info {
        margin-bottom: 1rem;
    }

    .reporter-name {
        font-weight: 600;
        color: #212529;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .report-timestamp {
        color: #6c757d;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .report-reason {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .reason-label {
        color: #495057;
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .reason-text {
        color: #495057;
        font-size: 0.875rem;
        line-height: 1.4;
        margin: 0;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        background: white;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .empty-icon {
        font-size: 3rem;
        color: #28a745;
        margin-bottom: 1rem;
    }

    .empty-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.5rem;
    }

    .empty-description {
        color: #6c757d;
        font-size: 0.875rem;
        margin: 0;
    }


    @media (max-width: 768px) {
        .report-content {
            grid-template-columns: 1fr;
        }

        .comment-section {
            border-right: none;
            border-bottom: 1px solid #e9ecef;
        }

        .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .section-title {
            font-size: 1.25rem;
        }

        .report-section {
            min-width: unset;
        }

        .reports-section {
            padding: 1.5rem;
            border-radius: 8px;
        }
    }

    @media (max-width: 576px) {
        .comment-header {
            flex-wrap: wrap;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}