{% extends "base.html" %}
{% from "components/password_input.html" import password_input %}

{% block title %}Login - Footprints{% endblock %}

{% block content %}
<div class="container login-container">
    <div class="row justify-content-center align-items-center full-height">
        <div class="col-md-10">
            <div class="row g-0 login-wrapper">
                <!-- right-side: image -->
                <div class="col-md-6 d-none d-md-block p-0">
                    <div class="login-image-wrapper">
                        <img src="{{ url_for('static', filename='images/login_img.jpg') }}" alt="Login Image"
                            class="login-image">
                    </div>
                </div>

                <!-- left-side: login form -->
                <div class="col-md-6 login-form-section">
                    <div class="p-5 d-flex flex-column justify-content-center h-s">
                        <h1 class="display-6 fw-bold mb-3">Login</h1>

                        <form method="post" action="{{ url_for('auth.login') }}" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" required
                                    value="{{ username or '' }}" oninput="validateUsername(this)">
                                <div class="invalid-feedback">Please enter your username</div>
                            </div>

                            {{ password_input(
                            id="password",
                            name="password",
                            label="Password",
                            required=true
                            ) }}

                            <div class="mb-4 mt-4">
                                <button type="submit" class="login-button btn btn-outline-dark w-100 py-2">
                                    Login
                                </button>
                            </div>
                        </form>

                        <p class="text-center mb-0">
                            Don't have an account?
                            <a href="{{ url_for('auth.register') }}" class="fw-medium text-decoration-none">Sign
                                up</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% include 'components/chatbot.html' %}

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/password_toggle.js') }}"></script>
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Set default error messages
        const passwordFeedback = document.querySelector('#password')
            .closest('.input-group').querySelector('.invalid-feedback');
        if (passwordFeedback) {
            passwordFeedback.textContent = 'Please enter your password';
        }
    });
</script>
{% endblock %}