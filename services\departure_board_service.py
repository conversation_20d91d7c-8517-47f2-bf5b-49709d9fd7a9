"""
Departure Board Service Module

This module handles operations related to the departure board including:
- Following journeys, users, and locations
- Getting departure board events
"""

from typing import Dict, List, Optional, Any, Tuple
from data import departure_board_data, user_data, journey_data, location_data
from services import subscription_service
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# ===== Journey Following =====

def follow_journey(user_id: int, journey_id: int) -> Tuple[bool, str]:
    """Follow a journey.
    
    Args:
        user_id: ID of the user.
        journey_id: ID of the journey.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists
        journey = journey_data.get_journey(journey_id)
        if not journey:
            logger.warning(f"Attempt to follow non-existent journey {journey_id}")
            return False, "Journey not found"
        
        # Check if journey is public or published (not hidden)
        if journey['visibility'] == 'private' or journey['is_hidden']:
            logger.warning(f"User {user_id} attempted to follow private/hidden journey {journey_id}")
            return False, "You can only follow public journeys"
        
        # Check for premium features
        can_use_feature = subscription_service.check_can_use_premium_features(user_id)
        if not can_use_feature:
            logger.warning(f"User {user_id} attempted to follow journey without premium subscription")
            return False, "Following journeys requires a premium subscription"
        
        # Check if already following
        already_following = departure_board_data.check_following_journey(user_id, journey_id)
        if already_following:
            logger.info(f"User {user_id} is already following journey {journey_id}")
            return False, "You are already following this journey"
        
        # Follow journey
        follow_id = departure_board_data.follow_journey(user_id, journey_id)
        
        logger.info(f"User {user_id} followed journey {journey_id}")
        return True, "Journey followed successfully"
    except Exception as e:
        logger.error(f"Error following journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Error following journey: {str(e)}"

def unfollow_journey(user_id: int, journey_id: int) -> Tuple[bool, str]:
    """Unfollow a journey.
    
    Args:
        user_id: ID of the user.
        journey_id: ID of the journey.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if journey exists
        journey = journey_data.get_journey(journey_id)
        if not journey:
            logger.warning(f"Attempt to unfollow non-existent journey {journey_id}")
            return False, "Journey not found"
        
        # Check if following
        is_following = departure_board_data.check_following_journey(user_id, journey_id)
        if not is_following:
            logger.info(f"User {user_id} is not following journey {journey_id}")
            return False, "You are not following this journey"
        
        # Unfollow journey
        rows_affected = departure_board_data.unfollow_journey(user_id, journey_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} unfollowed journey {journey_id}")
            return True, "Journey unfollowed successfully"
        else:
            logger.warning(f"Failed to unfollow journey {journey_id} for user {user_id}")
            return False, "Failed to unfollow journey"
    except Exception as e:
        logger.error(f"Error unfollowing journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Error unfollowing journey: {str(e)}"

def get_followed_journeys(user_id: int) -> List[Dict[str, Any]]:
    """Get all journeys followed by a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        List[Dict[str, Any]]: List of followed journey records.
    """
    try:
        return departure_board_data.get_followed_journeys(user_id)
    except Exception as e:
        logger.error(f"Error getting followed journeys for user {user_id}: {str(e)}")
        return []

def check_following_journey(user_id: int, journey_id: int) -> Tuple[bool, str]:
    """Check if a user is following a journey.
    
    Args:
        user_id: ID of the user.
        journey_id: ID of the journey.
        
    Returns:
        Tuple[bool, str]: Success flag and message indicating following status or error.
    """
    try:
        # Check if journey exists
        journey = journey_data.get_journey(journey_id)
        if not journey:
            logger.warning(f"Attempt to check following status for non-existent journey {journey_id}")
            return False, "Journey not found"
        
        # Check if following
        is_following = departure_board_data.check_following_journey(user_id, journey_id)
        
        if is_following:
            logger.info(f"User {user_id} is following journey {journey_id}")
            return True, "User is following the journey"
        else:
            logger.info(f"User {user_id} is not following journey {journey_id}")
            return False, "User is not following the journey"
    except Exception as e:
        logger.error(f"Error checking following status for journey {journey_id} by user {user_id}: {str(e)}")
        return False, f"Error checking following status: {str(e)}"

# ===== User Following =====

def follow_user(follower_id: int, followed_id: int) -> Tuple[bool, str]:
    """Follow a user.
    
    Args:
        follower_id: ID of the user doing the following.
        followed_id: ID of the user to follow.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if followed user exists
        followed_user = user_data.get_user_by_id(followed_id)
        if not followed_user:
            logger.warning(f"Attempt to follow non-existent user {followed_id}")
            return False, "User not found"
        
        # Check if user is trying to follow themselves
        if follower_id == followed_id:
            logger.warning(f"User {follower_id} attempted to follow themselves")
            return False, "You cannot follow yourself"
        
        # Check for premium features
        can_use_feature = subscription_service.check_can_use_premium_features(follower_id)
        if not can_use_feature:
            logger.warning(f"User {follower_id} attempted to follow user without premium subscription")
            return False, "Following users requires a premium subscription"
        
        # Check if already following
        already_following = departure_board_data.check_following_user(follower_id, followed_id)
        if already_following:
            logger.info(f"User {follower_id} is already following user {followed_id}")
            return False, "You are already following this user"
        
        # Follow user
        follow_id = departure_board_data.follow_user(follower_id, followed_id)
        
        logger.info(f"User {follower_id} followed user {followed_id}")
        return True, "User followed successfully"
    except Exception as e:
        logger.error(f"Error following user {followed_id} by user {follower_id}: {str(e)}")
        return False, f"Error following user: {str(e)}"

def unfollow_user(follower_id: int, followed_id: int) -> Tuple[bool, str]:
    """Unfollow a user.
    
    Args:
        follower_id: ID of the user doing the unfollowing.
        followed_id: ID of the user to unfollow.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if followed user exists
        followed_user = user_data.get_user_by_id(followed_id)
        if not followed_user:
            logger.warning(f"Attempt to unfollow non-existent user {followed_id}")
            return False, "User not found"
        
        # Check if following
        is_following = departure_board_data.check_following_user(follower_id, followed_id)
        if not is_following:
            logger.info(f"User {follower_id} is not following user {followed_id}")
            return False, "You are not following this user"
        
        # Unfollow user
        rows_affected = departure_board_data.unfollow_user(follower_id, followed_id)
        
        if rows_affected > 0:
            logger.info(f"User {follower_id} unfollowed user {followed_id}")
            return True, "User unfollowed successfully"
        else:
            logger.warning(f"Failed to unfollow user {followed_id} for user {follower_id}")
            return False, "Failed to unfollow user"
    except Exception as e:
        logger.error(f"Error unfollowing user {followed_id} by user {follower_id}: {str(e)}")
        return False, f"Error unfollowing user: {str(e)}"

def get_followed_users(user_id: int) -> List[Dict[str, Any]]:
    """Get all users followed by a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        List[Dict[str, Any]]: List of followed user records.
    """
    try:
        return departure_board_data.get_followed_users(user_id)
    except Exception as e:
        logger.error(f"Error getting followed users for user {user_id}: {str(e)}")
        return []

def get_followers(user_id: int) -> List[Dict[str, Any]]:
    """Get all followers of a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        List[Dict[str, Any]]: List of follower records.
    """
    try:
        return departure_board_data.get_followers(user_id)
    except Exception as e:
        logger.error(f"Error getting followers for user {user_id}: {str(e)}")
        return []
    

def check_following_user(follower_id: int, followed_id: int) -> Tuple[bool, str]:
    """Check if a user is following another user.

    Args:
        follower_id: ID of the user who may be following.
        followed_id: ID of the user being followed.

    Returns:
        Tuple[bool, str]: Success flag and message indicating following status or error.
    """
    try:
        # Check if followed user exists
        followed_user = user_data.get_user_by_id(followed_id)
        if not followed_user:
            logger.warning(f"Attempt to check following status for non-existent user {followed_id}")
            return False, "User not found"

        # Check if following
        is_following = departure_board_data.check_following_user(follower_id, followed_id)

        if is_following:
            
            logger.info(f"User {follower_id} is following user {followed_id}")
            return True, "User is following the user"
        else:
            logger.info(f"User {follower_id} is NOT following user {followed_id}")
            return False, "User is not following the user"
    except Exception as e:
        logger.error(f"Error checking following status for user {followed_id} by user {follower_id}: {str(e)}")
        return False, f"Error checking following status: {str(e)}"



# ===== Location Following =====

def follow_location(user_id: int, location_id: int) -> Tuple[bool, str]:
    """Follow a location.
    
    Args:
        user_id: ID of the user.
        location_id: ID of the location.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if location exists
        location = location_data.get_location(location_id)
        if not location:
            logger.warning(f"Attempt to follow non-existent location {location_id}")
            return False, "Location not found"
        
        # Check for premium features
        can_use_feature = subscription_service.check_can_use_premium_features(user_id)
        if not can_use_feature:
            logger.warning(f"User {user_id} attempted to follow location without premium subscription")
            return False, "Following locations requires a premium subscription"
        
        # Check if already following
        already_following = departure_board_data.check_following_location(user_id, location_id)
        if already_following:
            logger.info(f"User {user_id} is already following location {location_id}")
            return False, "You are already following this location"
        
        # Follow location
        follow_id = departure_board_data.follow_location(user_id, location_id)
        
        logger.info(f"User {user_id} followed location {location_id}")
        return True, "Location followed successfully"
    except Exception as e:
        logger.error(f"Error following location {location_id} by user {user_id}: {str(e)}")
        return False, f"Error following location: {str(e)}"

def unfollow_location(user_id: int, location_id: int) -> Tuple[bool, str]:
    """Unfollow a location.
    
    Args:
        user_id: ID of the user.
        location_id: ID of the location.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if location exists
        location = location_data.get_location(location_id)
        if not location:
            logger.warning(f"Attempt to unfollow non-existent location {location_id}")
            return False, "Location not found"
        
        # Check if following
        is_following = departure_board_data.check_following_location(user_id, location_id)
        if not is_following:
            logger.info(f"User {user_id} is not following location {location_id}")
            return False, "You are not following this location"
        
        # Unfollow location
        rows_affected = departure_board_data.unfollow_location(user_id, location_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} unfollowed location {location_id}")
            return True, "Location unfollowed successfully"
        else:
            logger.warning(f"Failed to unfollow location {location_id} for user {user_id}")
            return False, "Failed to unfollow location"
    except Exception as e:
        logger.error(f"Error unfollowing location {location_id} by user {user_id}: {str(e)}")
        return False, f"Error unfollowing location: {str(e)}"

def get_followed_locations(user_id: int) -> List[Dict[str, Any]]:
    """Get all locations followed by a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        List[Dict[str, Any]]: List of followed location records.
    """
    try:
        return departure_board_data.get_followed_locations(user_id)
    except Exception as e:
        logger.error(f"Error getting followed locations for user {user_id}: {str(e)}")
        return []
    
def check_following_location(user_id: int, location_id: int) -> Tuple[bool, str]:
    """Check if a user is following a location.
    
    Args:
        user_id: ID of the user.
        location_id: ID of the location.
        
    Returns:
        Tuple[bool, str]: Success flag and message indicating following status or error.
    """
    try:
        # Check if location exists
        location = location_data.get_location(location_id)
        if not location:
            logger.warning(f"Attempt to check following status for non-existent location {location_id}")
            return False, "Location not found"
        
        # Check if following
        is_following = departure_board_data.check_following_location(user_id, location_id)
        
        if is_following:
            logger.info(f"User {user_id} is following location {location_id}")
            return True, "User is following the location"
        else:
            logger.info(f"User {user_id} is not following location {location_id}")
            return False, "User is not following the location"
    except Exception as e:
        logger.error(f"Error checking following status for location {location_id} by user {user_id}: {str(e)}")
        return False, f"Error checking following status: {str(e)}"


# ===== Departure Board =====

def get_departure_board(user_id: int, limit: int = 50, offset: int = 0, search='') -> Tuple[bool, str, Optional[List[Dict[str, Any]]]]:
    """Get departure board events for a user.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of events to return.
        offset: Number of events to skip.
        
    Returns:
        Tuple[bool, str, Optional[List[Dict[str, Any]]]]: Success flag, message, and list of departure board events.
    """
    try:
        if search:
            events = departure_board_data.search_departure_board_events(user_id, search, limit, offset)
        else:
            events = departure_board_data.get_departure_board_events(user_id, limit, offset)
        
        logger.info(f"Retrieved {len(events)} departure board events for user {user_id}")
        return True, "Departure board retrieved successfully", events
    except Exception as e:
        logger.error(f"Error getting departure board for user {user_id}: {str(e)}")
        return False, f"Error getting departure board: {str(e)}", None
    

def get_departure_board_count(user_id, search=''):
    """Get total count of user's private journeys with search"""
    if search:
        events = departure_board_data.search_departure_board_events(user_id, search, 1000, 0)
    else:
        events = departure_board_data.get_departure_board_events(user_id)
    return len({event["event_id"] for event in events})   

def get_events_grouped_by(user_id: int, group_by: str = None) -> List[Dict[str, Any]]:
    logger.debug(f"Getting follows for user ID: {user_id}, grouped by: {group_by}")
    
    if not group_by:
        return False, "No group_by parameter provided", []

    follows = departure_board_data.get_follows(user_id=user_id, follow_type=group_by)
    events = departure_board_data.get_departure_board_events(user_id)

    # Group events by follow type
    events_by_key = {}
    for event in events:
        if event.get('follow_type') != group_by:
            continue

        if group_by == 'user':
            key = f"user_{event['user_id']}"
        elif group_by == 'journey':
            key = f"journey_{event['journey_id']}"
        elif group_by == 'location':
            key = f"location_{event['location_id']}"
        else:
            continue

        events_by_key.setdefault(key, []).append(event)

    grouped_results = []

    for follow in follows:
        if group_by == 'user':
            key = f"user_{follow['followed_id']}"
            grouped_results.append({
                'entity_type': 'user',
                'entity_id': follow['followed_id'],
                'username': follow['username'],
                'profile_image': follow.get('profile_image', 'default.png'),
                'follow_type': 'user',
                'events': events_by_key.get(key, [])
            })

        elif group_by == 'journey':
            key = f"journey_{follow['journey_id']}"
            grouped_results.append({
                'entity_type': 'journey',
                'entity_id': follow['journey_id'],
                'title': follow['journey_title'],
                'follow_type': 'journey',
                'events': events_by_key.get(key, [])
            })

        elif group_by == 'location':
            key = f"location_{follow['location_id']}"
            grouped_results.append({
                'entity_type': 'location',
                'entity_id': follow['location_id'],
                'name': follow['location_name'],
                'follow_type': 'location',
                'events': events_by_key.get(key, [])
            })

    return True, "Grouped follows retrieved", grouped_results
