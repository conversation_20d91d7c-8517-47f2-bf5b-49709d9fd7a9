{% block content %}
<div class="create-journey-modal" id="createJourneyModal">
  <form method="post" action="{{ url_for('journey.create_journey') }}" enctype="multipart/form-data"
    novalidate id="create-journey-form" class="needs-validation modern-form">

    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Grid Layout -->
      <div class="desktop-grid{% if not premium_access %} single-column{% endif %}">

        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-info-circle section-icon"></i>
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-journal-text"></i>
                  Journey Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title" value="{{ request.form.get('title', '') }}"
                  required minlength="5" maxlength="50" placeholder="Enter journey title" />
                <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required
                  minlength="5" maxlength="250" rows="4" placeholder="Describe your journey...">{{ request.form.get('description', '') }}</textarea>
                <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
              </div>
            </div>
          </div>

          <!-- Journey Settings Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-gear section-icon"></i>
              <span class="section-title">Journey Settings</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="start_date" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date *
                </label>
                <input type="date" class="modern-input date-input" id="start_date" name="start_date"
                  value="{{ request.form.get('start_date', '') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              {% if user_blocked %}
              <div class="form-group">
                <div class="blocked-alert modern-alert">
                  <div class="alert-content">
                    <i class="bi bi-info-circle-fill alert-icon"></i>
                    <div class="alert-text">
                      <strong>Account Restricted</strong>
                      <p>You have been blocked from sharing journeys publicly, so your journey will remain private.</p>
                    </div>
                  </div>
                </div>
                <input type="hidden" name="visibility" value="private" />
              </div>
              {% else %}
              <div class="form-group">
                <label for="visibility" class="modern-label">
                  <i class="bi bi-eye"></i>
                  Visibility
                </label>
                <select id="visibility" name="visibility" class="modern-select">
                  <option value="private" selected>Private</option>
                  <option value="public">Public</option>
                  {% if premium_access %}
                  <option value="published">Published</option>
                  {% endif %}
                </select>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  <div class="visibility-help">
                    <strong>Private:</strong> Only visible to you<br>
                    <strong>Public:</strong> Visible to all users<br>
                    {% if premium_access %}
                    <strong>Published:</strong> Visible to everyone, including non-logged in users
                    {% endif %}
                  </div>
                </div>
              </div>
              {% endif %}

              {% if premium_access %}
              <div class="form-group">
                <div class="modern-checkbox">
                  <input type="checkbox" class="modern-checkbox-input" id="no_edits" name="no_edits">
                  <label class="modern-checkbox-label" for="no_edits">
                    <i class="bi bi-shield-lock"></i>
                    Prevent editors from editing this journey
                  </label>
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    When checked, editors and admins cannot edit your journey content, but they can still hide it if needed.
                  </div>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>

        {% if premium_access %}
        <!-- Right Column (Premium Only) -->
        <div class="right-column">
          <!-- Cover Image Section -->
          <div class="form-section image-section">
            <div class="section-header">
              <i class="bi bi-image section-icon"></i>
              <span class="section-title">Cover Image</span>
            </div>

            <div class="image-content">
              <!-- Image Preview Container -->
              <div id="image-preview-container" class="image-preview-area">
                <div class="placeholder-container">
                  <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                    alt="Journey placeholder" class="placeholder-image" id="current-image-preview">
                </div>
              </div>

              <!-- Image Upload -->
              <div class="form-group">
                <label for="image" class="modern-label">
                  <i class="bi bi-camera"></i>
                  Add Cover Image (optional)
                </label>
                <input type="file" class="modern-input" id="image" name="image"
                  accept="{{ file_config.allowedExtensionsHtml }}" data-premium="false" />
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Maximum {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{ file_config.allowedFormatsText }}.
                </div>
                <div class="invalid-feedback" id="imageFeedback">
                  Image cannot exceed {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{ file_config.allowedFormatsText }}.
                </div>
              </div>
            </div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </form>

<style>
/* Modern Create Journey Modal Styles */
.create-journey-modal {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.modern-form {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Form Content */
.form-content {
  padding: 24px;
  background: #fafbfc;
}

/* Desktop Grid Layout */
.desktop-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.desktop-grid.single-column {
  grid-template-columns: 1fr;
  max-width: 600px;
  margin: 0 auto;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Sections */
.form-section {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 0;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  height: fit-content;
}

.form-section.compact {
  padding: 18px;
}

.form-section:hover {
  border-color: #667eea;
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
}

.form-section.image-section {
  height: fit-content;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f1f3f4;
}

.section-icon {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  flex: 1;
}

/* Form Grid */
.form-grid {
  display: grid;
  gap: 16px;
}

.form-grid.two-columns {
  grid-template-columns: 1fr 1fr;
}

.form-grid .full-width {
  grid-column: 1 / -1;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Modern Labels */
.modern-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.modern-label i {
  color: #667eea;
  font-size: 16px;
}

/* Modern Inputs */
.modern-input,
.modern-textarea,
.modern-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #2d3748;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
}

.modern-input:focus,
.modern-textarea:focus,
.modern-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.modern-input:disabled,
.modern-textarea:disabled,
.modern-select:disabled {
  background: #f7fafc;
  color: #a0aec0;
  border-color: #e2e8f0;
  cursor: not-allowed;
}

.modern-input::placeholder,
.modern-textarea::placeholder {
  color: #a0aec0;
}

/* Date Inputs */
.date-input {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modern Select */
.modern-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Modern Checkbox */
.modern-checkbox {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modern-checkbox-input {
  display: none;
}

.modern-checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  cursor: pointer;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
  position: relative;
}

.modern-checkbox-label::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  background: #ffffff;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.modern-checkbox-input:checked + .modern-checkbox-label::before {
  background: #667eea;
  border-color: #667eea;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
}

.modern-checkbox-label:hover {
  border-color: #667eea;
  background: #f7faff;
}

.modern-checkbox-input:checked + .modern-checkbox-label {
  border-color: #667eea;
  background: #f0f4ff;
}

.modern-checkbox-label i {
  color: #667eea;
  font-size: 16px;
}

/* Modern Alert */
.modern-alert {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(102, 126, 234, 0.05);
}

.blocked-alert {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.alert-icon {
  color: #ef4444;
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-text {
  flex: 1;
}

.alert-text strong {
  color: #dc2626;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.alert-text p {
  color: #991b1b;
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

/* Image Preview Styles */
.image-preview-area {
  margin-bottom: 16px;
}

.placeholder-container {
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.placeholder-container:hover {
  border-color: #667eea;
}

.placeholder-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.placeholder-image:hover {
  transform: scale(1.02);
}

/* Help Text */
.input-help {
  font-size: 12px;
  color: #718096;
  margin-top: 4px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.input-help i {
  color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.visibility-help {
  line-height: 1.4;
}

.visibility-help strong {
  color: #4a5568;
}

/* Validation Styles */
.modern-input.is-invalid,
.modern-textarea.is-invalid,
.modern-select.is-invalid,
.was-validated .modern-input:invalid,
.was-validated .modern-textarea:invalid,
.was-validated .modern-select:invalid {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.modern-input.is-valid,
.modern-textarea.is-valid,
.modern-select.is-valid,
.was-validated .modern-input:valid,
.was-validated .modern-textarea:valid,
.was-validated .modern-select:valid {
  border-color: #38a169;
  box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.invalid-feedback {
  color: #e53e3e;
  font-size: 12px;
  margin-top: 4px;
  display: none; /* Hidden by default */
  align-items: center;
  gap: 6px;
}

/* Show invalid feedback only when input is invalid or form was validated */
.modern-input.is-invalid + .invalid-feedback,
.modern-textarea.is-invalid + .invalid-feedback,
.modern-select.is-invalid + .invalid-feedback,
.is-invalid ~ .invalid-feedback,
.was-validated .modern-input:invalid + .invalid-feedback,
.was-validated .modern-textarea:invalid + .invalid-feedback,
.was-validated .modern-select:invalid + .invalid-feedback,
.was-validated .is-invalid ~ .invalid-feedback {
  display: flex;
}

.invalid-feedback::before {
  content: "⚠";
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 992px) {
  .desktop-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .desktop-grid.single-column {
    max-width: 100%;
  }

  .create-journey-modal {
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .create-journey-modal {
    margin: 0;
    border-radius: 0;
  }

  .form-content {
    padding: 20px 16px;
  }

  .form-section {
    padding: 16px;
    border-radius: 8px;
  }

  .form-section.compact {
    padding: 14px;
  }

  .desktop-grid {
    gap: 16px;
  }

  .left-column,
  .right-column {
    gap: 16px;
  }

  .form-grid {
    gap: 14px;
  }

  .section-header {
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 14px;
    padding-bottom: 10px;
  }

  .section-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .placeholder-image {
    height: 240px;
  }

  .modern-input,
  .modern-textarea,
  .modern-select {
    padding: 10px 12px;
    font-size: 13px;
  }

  .modern-label {
    font-size: 13px;
    gap: 6px;
  }

  .modern-checkbox-label {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .form-content {
    padding: 16px 12px;
  }

  .form-section {
    padding: 12px;
  }

  .form-section.compact {
    padding: 10px;
  }

  .desktop-grid {
    gap: 12px;
  }

  .left-column,
  .right-column {
    gap: 12px;
  }

  .placeholder-image {
    height: 200px;
  }

  .section-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .section-title {
    font-size: 13px;
  }

  .modern-input,
  .modern-textarea,
  .modern-select {
    padding: 8px 10px;
    font-size: 12px;
  }

  .modern-label {
    font-size: 12px;
  }

  .modern-checkbox-label {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Animation for smooth transitions */
.form-section,
.modern-input,
.modern-textarea,
.modern-select,
.modern-checkbox-label,
.placeholder-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for better accessibility */
.modern-input:focus-visible,
.modern-textarea:focus-visible,
.modern-select:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.modern-checkbox-label:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}
</style>

<script>
  // Initialize modular image preview for journey cover image
  function initializeJourneyImagePreview() {
    const imageInput = document.getElementById('image');
    if (!imageInput) {
      console.log('Image input not found in create journey modal');
      return;
    }

    // Load required scripts and CSS
    const scripts = [
      '/static/js/file-validation.js',
      '/static/js/image-preview.js'
    ];

    const css = '/static/css/image-preview.css';

    // Load CSS if not already loaded
    if (!document.querySelector(`link[href="${css}"]`)) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = css;
      document.head.appendChild(link);
    }

    // Load scripts sequentially
    loadScriptsSequentially(scripts).then(() => {
      // Setup image preview for single image (journey cover)
      if (window.setupImagePreview) {
        window.setupImagePreview('#image', {
          maxFiles: 1,
          allowMultiple: false,
          containerSelector: '#imagePreviewContainer',
          gridSelector: '#previewGrid',
          feedbackSelector: '#imageFeedback',

          // Custom callbacks for journey cover image
          onFileSelect: function(files) {
            console.log('Journey cover image selected:', files.length);
            // Also update the placeholder image
            updatePlaceholderImage(files[0]);
          },
          onFileRemove: function(index, remainingFiles) {
            console.log('Journey cover image removed');
            // Reset placeholder image
            resetPlaceholderImage();
          },
          onClear: function() {
            console.log('Journey cover image cleared');
            // Reset placeholder image
            resetPlaceholderImage();
          },
          onValidationError: function(message) {
            console.log('Journey cover image validation error:', message);
          }
        });
        console.log('Modular image preview initialized for create journey modal');
      } else {
        console.error('setupImagePreview function not available');
      }
    }).catch(error => {
      console.error('Failed to load image preview scripts:', error);
    });
  }

  // Update placeholder image when file is selected
  function updatePlaceholderImage(file) {
    if (!file) return;

    const preview = document.getElementById('current-image-preview');
    if (!preview) return;

    const reader = new FileReader();
    reader.onload = function(e) {
      preview.src = e.target.result;
      preview.style.objectFit = 'cover';
    };
    reader.readAsDataURL(file);
  }

  // Reset placeholder image to default
  function resetPlaceholderImage() {
    const preview = document.getElementById('current-image-preview');
    if (preview) {
      preview.src = "{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}";
      preview.style.objectFit = 'cover';
    }
  }

  function loadScriptsSequentially(scripts) {
    return scripts.reduce((promise, script) => {
      return promise.then(() => loadScript(script));
    }, Promise.resolve());
  }

  function loadScript(src) {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Load form validation script and initialize
  function loadFormValidation() {
    // Check if form-validation.js is already loaded
    if (typeof window.initializeFormValidation === 'function') {
      initializeCreateJourneyValidation();
    } else {
      // Load the form validation script
      const script = document.createElement('script');
      script.src = '/static/js/form-validation.js';
      script.onload = function() {
        initializeCreateJourneyValidation();
      };
      document.head.appendChild(script);
    }
  }

  // Initialize create journey specific validation
  function initializeCreateJourneyValidation() {
    const form = document.getElementById('create-journey-form');
    if (!form) return;

    // Initialize the base form validation
    if (typeof window.initializeFormValidation === 'function') {
      window.initializeFormValidation(form);
    }

    // Add smooth focus animations
    setupFocusAnimations();

    // Add loading state for form submission
    setupSubmissionState();
  }

  // Setup smooth focus animations
  function setupFocusAnimations() {
    const inputs = document.querySelectorAll('.modern-input, .modern-textarea, .modern-select');

    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'translateY(-2px)';
      });

      input.addEventListener('blur', function() {
        this.parentElement.style.transform = 'translateY(0)';
      });
    });
  }

  // Setup form submission loading state
  function setupSubmissionState() {
    const form = document.getElementById('create-journey-form');
    if (!form) return;

    form.addEventListener('submit', function() {
      const submitBtn = document.querySelector('.modal-footer .btn-primary');
      if (submitBtn && form.checkValidity()) {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating...';
        submitBtn.disabled = true;
      }
    });
  }

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    loadFormValidation();
    initializeJourneyImagePreview();
  });
</script>
</div>
{% endblock %}