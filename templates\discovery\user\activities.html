{% set activities_tab = activities_tab or 'journeys' %}
<style>
  .activities-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .activities-tabs .btn {
    border: none !important;
    border-radius: 999px !important;
    background: #f4f6fb;
    color: #4e6bff;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.7rem 2.2rem;
    font-size: 1.08rem;
    min-width: 180px;
    max-width: 240px;
    text-align: center;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.15s;
    box-shadow: 0 1px 4px rgba(78, 107, 255, 0.06);
    margin: 0 0.25rem 0.5rem 0.25rem;
  }

  .activities-tabs .btn span,
  .activities-tabs .btn i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .activities-tabs .btn.active,
  .activities-tabs .btn:focus {
    background: #4e6bff;
    color: #fff !important;
  }

  .activities-tabs .btn:active {
    background: #4e6bff;
  }

  @media (max-width: 900px) {
    .activities-tabs {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .activities-tabs .btn {
      min-width: 45%;
      max-width: 100%;
      font-size: 0.98rem;
      padding: 0.6rem 0.5rem;
      margin: 0.25rem 0;
    }
  }

  @media (max-width: 600px) {
    .activities-tabs .btn {
      min-width: 100%;
      font-size: 0.92rem;
      padding: 0.5rem 0.2rem;
    }
  }

  .journey-card {
    height: 380px;
    border-radius: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    border: 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    background: #fff;
  }

  .journey-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .journey-image {
    height: 180px;
    border-radius: 15px 15px 0 0;
    overflow: hidden;
  }

  .journey-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .journey-dates {
    margin-bottom: 8px;
  }

  .journey-title {
    font-weight: bold;
    font-size: 1.1rem;
    line-height: 1.3;
    margin-bottom: 6px;
    max-height: 2.7rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .journey-description {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.5;
    max-height: 3.8rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .journey-author {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  .author-avatar {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }

  .author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .author-info {
    margin-left: 8px;
  }

  .author-label {
    color: #6c757d;
    line-height: 1;
  }

  .author-name {
    font-weight: 500;
  }
</style>
<div class="container">
  <div class="activities-tabs mb-4">
    <a href="{{ url_for('account.get_public_profile', username=user.username, active_tab='activities', activities_tab='journeys') }}"
      class="btn{% if activities_tab == 'journeys' %} active{% endif %}"><i class="bi bi-map"></i> <span>Public
        Journeys</span></a>
    <a href="{{ url_for('account.get_public_profile', username=user.username, active_tab='activities', activities_tab='places') }}"
      class="btn{% if activities_tab == 'places' %} active{% endif %}"><i class="bi bi-geo-alt"></i> <span>Visited
        Places</span></a>
    <a href="{{ url_for('account.get_public_profile', username=user.username, active_tab='activities', activities_tab='likes') }}"
      class="btn{% if activities_tab == 'likes' %} active{% endif %}"><i class="bi bi-heart"></i> <span>Recent
        Likes</span></a>
    <a href="{{ url_for('account.get_public_profile', username=user.username, active_tab='activities', activities_tab='comments') }}"
      class="btn{% if activities_tab == 'comments' %} active{% endif %}"><i class="bi bi-chat"></i> <span>Recent
        Comments</span></a>
  </div>
  <div class="tab-content p-3" id="activitiesTabContent">
    {%- if activities_tab == 'journeys' %}
    {% if user.public_journeys %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-5">
      {% for journey in user.public_journeys %}
      <div class="col">
        <div class="card h-100 journey-card border-0 shadow-sm">
          <div class="position-relative journey-image">
            {% if journey.cover_image %}
            <img src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
              alt="{{ journey.title }}" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;">
            {% elif journey.event_image %}
            <img src="{{ url_for('static', filename='uploads/event_images/' + journey.event_image) }}"
              alt="{{ journey.title }}" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;"
              onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
            {% elif journey.image_url %}
            <img src="{{ journey.image_url }}" alt="{{ journey.title }}" class="card-img-top"
              style="object-fit: cover; height: 100%; width: 100%;"
              onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
            {% else %}
            <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
              alt="Journey placeholder" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;">
            {% endif %}
          </div>
          <div class="card-body d-flex flex-column">
            <div class="d-flex journey-dates">
              <div class="small text-muted me-auto">
                <i class="bi bi-calendar3 me-1"></i>
                <span>{{ journey.start_date|date }}</span>
              </div>
            </div>
            <h4 class="card-title journey-title">{{ journey.title }}</h4>
            <p class="card-text journey-description">{{ journey.description|truncate(80) }}</p>
            <div class="mt-auto journey-author">
              <div class="d-flex align-items-center">
                <div class="rounded-circle overflow-hidden author-avatar">
                  {% if journey.profile_image %}
                  <img src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                    alt="{{ journey.username }}" class="img-fluid"
                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                  {% else %}
                  <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                    alt="{{ journey.username }}" class="img-fluid">
                  {% endif %}
                </div>
                <div class="author-info">
                  <span class="text-muted small d-block author-label">Written by</span>
                  <p class="mb-0 fw-medium small author-name">{{ journey.username }}</p>
                </div>
              </div>
            </div>
            <a href="{{ url_for('journey.get_public_journey', journey_id=journey.id) }}" class="stretched-link"
              aria-label="View {{ journey.title }}"></a>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div class="alert"
      style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
      <i class="bi bi-info-circle me-2"></i> No public journeys found.
      <p class="small text-muted mt-2 mb-0">
        Create journeys with events to see them here.
      </p>
    </div>
    {% endif %}
    {# 지도 #}
    {%- if user.events is defined and user.events and user.events|length > 0 -%}
    {%- set locations_json = user.events | tojson | safe -%}
    <div class="g-4 py-3">
      <div class="location-container">
        <div id="map" style="height: 300px; width: 100%; border-radius: 8px;"></div>
      </div>
    </div>
    <script>
      const unsortedLocations = {{ locations_json }};
      document.addEventListener('DOMContentLoaded', function () {
        if (!unsortedLocations || unsortedLocations.length === 0) {
          console.warn("No valid locations provided.");
          return;
        }
        if (typeof L === 'undefined') {
          console.error("Leaflet library (L) is not loaded!");
          return;
        }
        const locations = unsortedLocations.sort((a, b) => {
          return new Date(a.event_start_datetime) - new Date(b.event_start_datetime);
        });
        const firstLat = parseFloat(locations[0].latitude);
        const firstLng = parseFloat(locations[0].longitude);
        const mymap = L.map('map').setView([firstLat, firstLng], 10);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
        }).addTo(mymap);
        const redIcon = L.icon({
          iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowSize: [41, 41]
        });
        let pathPoints = [];
        const locationMap = locations.reduce((acc, loc) => {
          const key = `${loc.latitude},${loc.longitude}`;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(loc);
          return acc;
        }, {});
        window.moveSlide = function (btn, direction) {
          const container = btn.closest('.carousel-container');
          const slides = Array.from(container.querySelectorAll('.carousel-slide'));
          let currentIndex = slides.findIndex(slide => slide.style.display === 'block');
          slides[currentIndex].style.display = 'none';
          const nextIndex = (currentIndex + direction + slides.length) % slides.length;
          slides[nextIndex].style.display = 'block';
          const popup = btn.closest('.location-popup');
          const currentSlideEl = popup.querySelector('.current-slide');
          if (currentSlideEl) {
            currentSlideEl.textContent = nextIndex + 1;
          }
        };
        Object.entries(locationMap).forEach(([coordKey, locsAtSamePlace]) => {
          const [lat, lng] = coordKey.split(',').map(parseFloat);
          pathPoints.push([lat, lng]);
          const popupContent = `
                    <div class="location-popup">
                      ${locsAtSamePlace[0].name ? `<h3>${locsAtSamePlace[0].name}</h3>` : ''}
                      <div class="carousel-container">
                        <button class="carousel-button prev" onclick="moveSlide(this, -1)">&#10094;</button>
                        <div class="carousel-content">
                          ${locsAtSamePlace.map((loc, index) => `
                            <div class="carousel-slide" style="display: ${index === 0 ? 'block' : 'none'};">
                              <h5>Event</h5>
                              <h5 class="info-title">Title</h5>
                              <p>${loc.title}</p>
                              <h5 class="info-title">Description</h5>
                              <p>${loc.description || 'No description provided.'}</p>
                              <h5 class="info-title">Start Datetime</h5>
                              <p>${loc.start_datetime}</p>
                              <div class="d-flex justify-content-center">
                                <a href="/event/${loc.id}/detail" class="btn btn-dark rounded-pill px-4 text-white">View</a>
                              </div>
                            </div>
                          `).join('')}
                        </div>
                        <button class="carousel-button next" onclick="moveSlide(this, 1)">&#10095;</button>
                      </div>
                      <div class="carousel-indicator">
                        <span class="current-slide">1</span>/<span class="total-slides">${locsAtSamePlace.length}</span>
                      </div>
                    </div>
                  `;
          const marker = L.marker([lat, lng], { icon: redIcon }).addTo(mymap);
          marker.bindPopup(popupContent, { maxWidth: 300 });
        });
        if (pathPoints.length > 1) {
          const bounds = L.latLngBounds(pathPoints);
          mymap.fitBounds(bounds, {
            padding: [30, 30]
          });
        }
      });
    </script>
    {%- endif %}
    {% elif activities_tab == 'places' %}
    {% if user.visited_places %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      {% for place in user.visited_places %}
      <div class="col">
        <div class="card h-100 shadow-sm border-0 rounded-3 transition-hover">
          <div class="card-body d-flex flex-column">
            <div class="d-flex align-items-center mb-2">
              <i class="bi bi-geo-alt-fill fs-4 me-2 text-primary"></i>
              <h5 class="card-title mb-0">{{ place.name }}</h5>
            </div>
            <p class="card-text text-muted mt-auto">
              <span class="badge bg-light text-dark rounded-pill">
                <i class="bi bi-pin-map me-1"></i> {{ place.visit_count }} visits
              </span>
            </p>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div class="alert"
      style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
      <i class="bi bi-info-circle me-2"></i> No visited places found.
      <p class="small text-muted mt-2 mb-0">
        Create journeys with events to see your visited places here.
      </p>
    </div>
    {% endif %}
    {% elif activities_tab == 'likes' %}
    {% if user.recent_likes %}
    <div class="list-group">
      {% for like in user.recent_likes %}
      <a href="{{ url_for('event.get_event_details', event_id=like.event_id) }}"
        class="list-group-item list-group-item-action border-0 rounded-3 mb-2 shadow-sm p-4">
        <div class="d-flex w-100 justify-content-between align-items-center">
          <div class="d-flex align-items-center gap-2">
            {% if like.like_type == 'comment' %}
            <span class="badge bg-info-subtle text-dark px-3 py-2 rounded-pill"><i class="bi bi-chat-quote"></i> Liked
              Comment</span>
            {% else %}
            <span class="badge bg-danger-subtle text-dark px-3 py-2 rounded-pill"><i class="bi bi-heart-fill"></i> Liked
              Event</span>
            {% endif %}
            <h5 class="mb-0">{{ like.title }}</h5>
          </div>
          <small>{{ like.liked_at|datetime }}</small>
        </div>
        {% if like.like_type == 'comment' %}
        <div class="mt-2 p-2 rounded bg-light border">
          <span class="text-muted small">Comment by <b>{{ like.comment_username }}</b>:</span>
          <div class="fw-medium">{{ like.comment_content }}</div>
        </div>
        {% endif %}
        <div class="mt-2">
          <span class="text-muted small">{{ like.journey_title }}</span>
          <span class="ms-2"><i class="bi bi-person-fill me-1"></i>{{ like.journey_username }}</span>
        </div>
      </a>
      {% endfor %}
    </div>
    {% else %}
    <div class="alert"
      style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
      <i class="bi bi-info-circle me-2"></i> No recent likes found.
      <p class="small text-muted mt-2 mb-0">
        Like events or comments to see them here.
      </p>
    </div>
    {% endif %}
    {% elif activities_tab == 'comments' %}
    {% if user.recent_comments %}
    <div class="list-group">
      {% for comment in user.recent_comments %}
      <a href="{{ url_for('event.get_event_details', event_id=comment.event_id) }}"
        class="list-group-item list-group-item-action border-0 rounded-3 mb-2 shadow-sm p-4">
        <div class="d-flex w-100 justify-content-between">
          <h5 class="mb-1">{{ comment.event_title }}</h5>
          <small>{{ comment.created_at|datetime }}</small>
        </div>
        <p class="mb-1">{{ comment.content }}</p>
        <small><i class="bi bi-chat-fill me-1"></i>{{ comment.like_count }} like{% if comment.like_count != 1 %}s{%
          endif %}</small>
      </a>
      {% endfor %}
    </div>
    {% else %}
    <div class="alert"
      style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
      <i class="bi bi-info-circle me-2"></i> No recent comments found.
      <p class="small text-muted mt-2 mb-0">
        Comment on events to see them here.
      </p>
    </div>
    {% endif %}
    {% endif %}
  </div>
</div>