{"timestamp": "2025-06-05T01:07:08.915285", "summary": {"total_functions_tracked": 27, "functions_used": 0, "functions_unused": 27, "usage_percentage": 0.0}, "cleanup_actions": {"safe_to_remove": ["manageImages", "addComment", "showModal", "editMapLocation", "deleteComment", "reportComment", "likeEvent", "showProtectedEventMessage", "searchLocation", "editLocationName", "deleteEvent", "updateMap", "hideModal", "changeLocation", "backToSearch", "confirmLocationChange", "validateForm", "selectLocation", "submitForm", "initMap", "confirmDeleteComment", "cancelLocationEdit", "uploadImages", "smartBack", "deleteImage", "editEvent", "confirmDeleteEvent"], "files_to_review": ["templates/event/detail.html", "templates/event/edit.html"], "search_patterns": ["function manageImages", "function addComment", "function showModal", "function editMapLocation", "function deleteComment", "function reportComment", "function likeEvent", "function showProtectedEventMessage", "function searchLocation", "function editLocationName"]}, "next_steps": ["1. Review unused functions in templates", "2. Search for each unused function in codebase", "3. Remove unused JavaScript code blocks", "4. Test functionality after removal", "5. Run tests again to verify cleanup"]}