{% block content %}
<div class="edit-event-modal" id="editEventModal">
  <form method="post" action="{{ url_for('event.update_event', event_id=event.id) }}" enctype="multipart/form-data"
    novalidate id="editEventForm" class="needs-validation modern-form">

    <!-- Hidden fields to store original values for change detection -->
    <input type="hidden" id="original_title" value="{{ event.title }}">
    <input type="hidden" id="original_description" value="{{ event.description }}">
    <input type="hidden" id="original_location" value="{{ event.location_name }}">
    <input type="hidden" id="original_start_datetime"
      value="{{ event.start_datetime.strftime('%Y-%m-%dT%H:%M') if event.start_datetime else '' }}">
    <input type="hidden" id="original_end_datetime"
      value="{{ event.end_datetime.strftime('%Y-%m-%dT%H:%M') if event.end_datetime else '' }}">

    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid">

        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-info-circle section-icon"></i>
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-type"></i>
                  Event Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title"
                  value="{{ event.title }}" required minlength="5" maxlength="50"
                  placeholder="Enter event title" />
                <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="3"
                  placeholder="Describe your event...">{{ event.description }}</textarea>
                <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
              </div>
            </div>
          </div>

          <!-- Date & Time Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-clock section-icon"></i>
              <span class="section-title">Date & Time</span>
              {% if journey.user_id != session.user_id and can_manage_content() %}
              <span class="permission-badge">
                <i class="bi bi-lock-fill"></i>
                Owner Only
              </span>
              {% endif %}
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="startDatetime" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date & Time *
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="startDatetime"
                  name="start_datetime" value="{{ event.start_datetime.strftime('%Y-%m-%dT%H:%M') if event.start_datetime else '' }}"
                  {% if journey.user_id != session.user_id and can_manage_content() %}disabled{% else %}required{% endif %} />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              <div class="form-group">
                <label for="endDatetime" class="modern-label">
                  <i class="bi bi-calendar-check"></i>
                  End Date & Time
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="endDatetime" name="end_datetime"
                  value="{{ event.end_datetime.strftime('%Y-%m-%dT%H:%M') if event.end_datetime else ''}}"
                  {% if journey.user_id != session.user_id and can_manage_content() %}disabled{% endif %} />
                <div class="invalid-feedback">End date cannot be before start date.</div>
                {% if journey.user_id != session.user_id and can_manage_content() %}
                <div class="permission-notice compact">
                  <i class="bi bi-info-circle"></i>
                  Only the Journey owner can edit the date and time of an event.
                </div>
                {% endif %}
              </div>
            </div>
          </div>


        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Current Location Display (Initial State) -->
          <div class="form-section compact" id="currentLocationSection">
            <div class="section-header">
              <i class="bi bi-geo-alt section-icon"></i>
              <span class="section-title">Current Location</span>
              {% if journey.user_id != session.user_id and can_manage_content() %}
              <span class="permission-badge staff-edit">
                <i class="bi bi-shield-check"></i>
                Staff Edit
              </span>
              {% endif %}
            </div>

            <div class="current-location-display">
              <div class="current-location-info">
                <div class="location-header">
                  <i class="bi bi-geo-alt-fill text-primary"></i>
                  <span class="location-name">{{ event.location_name }}</span>
                </div>
              </div>
            </div>

            <!-- Location Actions -->
            <div class="change-location-actions">
              {% if journey.user_id == session.user_id %}
              <!-- Owner can change full location (coordinates + name) -->
              <button type="button" id="changeLocationBtn" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-pencil"></i>
                Change Location
              </button>
              {% elif can_manage_content() %}
              <!-- Content managers can only edit location name -->
              <button type="button" id="editLocationNameBtn" class="btn btn-outline-warning btn-sm">
                <i class="bi bi-pencil-square"></i>
                Edit Location Name
              </button>
              {% endif %}
            </div>

            {% if journey.user_id != session.user_id and can_manage_content() %}
            <div class="permission-notice compact staff-notice">
              <i class="bi bi-info-circle"></i>
              As content manager, you can edit the location name but not the map coordinates.
            </div>
            {% endif %}
          </div>

          <!-- Staff Location Name Edit Section (Hidden Initially) -->
          {% if journey.user_id != session.user_id and can_manage_content() %}
          <div class="form-section compact staff-section" id="staffLocationEditSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-pencil-square section-icon"></i>
              <span class="section-title">Edit Location Name</span>
              <span class="staff-badge">
                <i class="bi bi-shield-check"></i>
                Staff Only
              </span>
            </div>

            <div class="form-group">
              <label for="staffLocationName" class="modern-label">
                <i class="bi bi-tag"></i>
                Location Name *
              </label>
              <input type="text" class="modern-input" id="staffLocationName" name="location"
                value="{{ event.location_name }}" required />
              <div class="invalid-feedback" id="staffLocationNameError">Location name is required.</div>
            </div>

            <div class="form-group">
              <label for="staffLocationScope" class="modern-label">
                <i class="bi bi-gear"></i>
                Update Scope *
              </label>
              <select class="modern-input" id="staffLocationScope" name="staff_location_scope" required>
                <option value="all_events">Update location everywhere (Recommended)</option>
                <option value="this_event_only">Create new location for this event only</option>
              </select>
              <div class="input-help" id="staffLocationScopeHelp">
                <i class="bi bi-info-circle"></i>
                <span id="scopeHelpText">This will change the name for ALL events using this location.</span>
              </div>
            </div>

            <div class="staff-edit-actions">
              <button type="button" id="cancelLocationEditBtn" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-x"></i>
                Cancel
              </button>
            </div>
          </div>
          {% endif %}

          <!-- Staff Location Change Choice Modal -->
          {% if journey.user_id != session.user_id and can_manage_content() %}
          <div class="modal fade" id="staffLocationChoiceModal" tabindex="-1" aria-labelledby="staffLocationChoiceModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="staffLocationChoiceModalLabel">
                    <i class="bi bi-geo-alt me-2"></i>
                    Location Name Change Options
                  </h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>You're changing the location name:</strong>
                    <br>
                    From: "<span id="originalLocationName"></span>"
                    <br>
                    To: "<span id="newLocationName"></span>"
                  </div>

                  <div class="location-choice-options">
                    <div class="form-check mb-3">
                      <input class="form-check-input" type="radio" name="locationChangeOption" id="updateExistingLocation" value="update" checked>
                      <label class="form-check-label" for="updateExistingLocation">
                        <strong>Update location everywhere (Recommended)</strong>
                        <div class="choice-description">
                          <i class="bi bi-arrow-repeat me-1"></i>
                          This will change the name for <strong>ALL events</strong> using this location.
                          <span id="affectedEventsCount"></span>
                        </div>
                        <div class="choice-benefits">
                          <small class="text-success">
                            <i class="bi bi-check-circle me-1"></i>
                            Fixes inappropriate name everywhere • Maintains data consistency
                          </small>
                        </div>
                      </label>
                    </div>

                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="locationChangeOption" id="createNewLocation" value="create">
                      <label class="form-check-label" for="createNewLocation">
                        <strong>Create new location for this event only</strong>
                        <div class="choice-description">
                          <i class="bi bi-plus-circle me-1"></i>
                          Other events will keep the original name. Creates a new location in database.
                        </div>
                        <div class="choice-benefits">
                          <small class="text-warning">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            Only affects this event • Other events still have original name
                          </small>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>
                    Cancel
                  </button>
                  <button type="button" class="btn btn-primary" id="confirmLocationChange">
                    <i class="bi bi-check me-1"></i>
                    Apply Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <!-- Current Location Map Preview (Initial State) -->
          <div class="form-section map-section" id="currentMapSection">
            <div class="section-header">
              <i class="bi bi-map section-icon"></i>
              <span class="section-title">Location Map</span>
            </div>

            <div class="location-content">
              <!-- Non-Interactive Map Preview -->
              <div class="map-container desktop-optimized">
                <div id="currentMap" class="modern-map map-preview-only"></div>
              </div>
            </div>
          </div>

          <!-- Location Search Section (Hidden Initially, Same as Create Event Modal) -->
          <div class="form-section compact" id="locationSearchSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-search section-icon"></i>
              <span class="section-title">Search Location</span>
            </div>

            <div class="form-group">
              <label for="locationSearch" class="modern-label">
                <i class="bi bi-search"></i>
                Search for Location *
              </label>
              <div class="location-search-container">
                <input type="text" class="modern-input" id="locationSearch"
                  placeholder="Type location name or address..." />
                <button type="button" class="btn btn-outline-primary btn-sm ms-2" id="searchLocationBtn">
                  <i class="bi bi-search"></i> Search
                </button>
              </div>
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                Search for existing locations or new addresses
              </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="location-results" style="display: none;">
              <div class="results-header">
                <h6 class="mb-0">Search Results</h6>
              </div>
              <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
              </div>
            </div>
          </div>

          <!-- Selected Location Section (Hidden Initially, Same as Create Event Modal) -->
          <div class="form-section compact" id="selectedLocationSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-geo-alt section-icon"></i>
              <span class="section-title">Selected Location</span>
            </div>

            <div class="selected-location-info">
              <div class="form-group">
                <label for="location" class="modern-label">
                  <i class="bi bi-tag"></i>
                  Location Name *
                </label>
                <input type="text" class="modern-input" id="location" name="location"
                  value="{{ event.location_name }}" required readonly />
                <div class="invalid-feedback">Location is required.</div>
              </div>

              <div class="location-actions mt-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" id="backToSearchBtn">
                  <i class="bi bi-arrow-left"></i> Back to Search
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary" id="editMapLocationBtn"
                  style="display: none;">
                  <i class="bi bi-geo-alt"></i> Change Map Location
                </button>
              </div>
            </div>
          </div>

          <!-- Map Section (Hidden Initially, Same as Create Event Modal) -->
          <div class="form-section map-section" id="mapSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-map section-icon"></i>
              <span class="section-title">Map Preview</span>
            </div>

            <div class="location-content">
              <!-- Interactive Map -->
              <div class="map-container desktop-optimized">
                <div id="map" class="modern-map {% if journey.user_id != session.user_id and can_manage_content() %}map-disabled{% endif %}"></div>
              </div>

              <!-- Map Search (for new locations) -->
              <div id="mapSearchGroup" class="form-group mt-3" style="display: none;">
                <label for="mapSearch" class="modern-label">
                  <i class="bi bi-search"></i>
                  Search Address on Map
                </label>
                <div class="map-search-container">
                  <input type="text" class="modern-input" id="mapSearch" placeholder="Search for address..." />
                  <div id="mapSuggestions" class="modern-suggestions"></div>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Search for address and click on map to set location
                </div>

                <!-- Coordinates Status (Hidden from users) -->
                <div id="coordinatesStatus" class="mt-2" style="display: none !important;">
                  <div class="alert alert-success py-2 px-3 mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    <span id="coordinatesText">Location coordinates set</span>
                  </div>
                </div>

                <!-- New Location Name Input -->
                <div id="newLocationNameGroup" class="mt-3" style="display: none;">
                  <label for="newLocationName" class="modern-label">
                    <i class="bi bi-tag"></i>
                    Name for New Location *
                  </label>
                  <input type="text" class="modern-input" id="newLocationName"
                    placeholder="Enter unique name for this location" />
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    This name will be validated and created when you submit the event
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Staff Edit Reason Section (if applicable) -->
          {% if journey.user_id != session.user_id and session.get('role') in ['editor', 'admin', 'support_tech'] %}
          <div class="form-section staff-section compact">
            <div class="section-header">
              <i class="bi bi-shield-check section-icon"></i>
              <span class="section-title">Staff Edit Reason</span>
              <span class="required-badge">Required</span>
            </div>

            <div class="form-group">
              <label for="edit_reason" class="modern-label">
                <i class="bi bi-chat-square-text"></i>
                Reason for Edit *
              </label>
              <textarea class="modern-textarea" id="edit_reason" name="edit_reason" rows="3" required
                placeholder="Please provide a reason for this edit..."></textarea>
              <div class="invalid-feedback">Staff must provide a reason for editing user content</div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Hidden coordinate fields -->
      <input type="hidden" id="latitude" name="latitude"
        value="{{ event.location_latitude if event.location_latitude else '' }}">
      <input type="hidden" id="longitude" name="longitude"
        value="{{ event.location_longitude if event.location_longitude else '' }}">

      <!-- Hidden input to keep track of deleted images -->
      <input type="hidden" name="deleted_images" id="deletedImages" value="">
    </div>
  </form>

  <style>
    /* Modern Edit Event Modal Styles */
    .edit-event-modal {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0;
    }

    .modern-form {
      background: #ffffff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    /* Form Content */
    .form-content {
      padding: 24px;
      background: #fafbfc;
    }

    /* Desktop Grid Layout */
    .desktop-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      align-items: start;
    }

    .left-column,
    .right-column {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* Form Sections */
    .form-section {
      background: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 0;
      border: 1px solid #e1e8ed;
      transition: all 0.3s ease;
      height: fit-content;
    }

    .form-section.compact {
      padding: 18px;
    }

    .form-section:hover {
      border-color: #667eea;
      box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
    }

    .form-section.map-section {
      height: fit-content;
    }

    /* Section Headers */
    .section-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f1f3f4;
    }

    .section-icon {
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;
      flex: 1;
    }

    /* Permission Badges */
    .permission-badge {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      background: #fff3cd;
      color: #856404;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      border: 1px solid #ffeaa7;
    }

    .permission-badge.map-restricted {
      background: #f8d7da;
      color: #721c24;
      border-color: #f5c6cb;
    }

    .required-badge {
      background: #d1ecf1;
      color: #0c5460;
      padding: 3px 6px;
      border-radius: 10px;
      font-size: 9px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Permission Notice */
    .permission-notice {
      font-size: 12px;
      color: #856404;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 6px;
      padding: 8px 12px;
      margin-top: 8px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .permission-notice.compact {
      padding: 6px 10px;
      font-size: 11px;
    }

    .permission-notice i {
      color: #f39c12;
    }

    /* Staff location editing styles - subtle and professional */
    .permission-badge.staff-edit {
      background: linear-gradient(135deg, #f8d7da, #f5c6cb);
      color: #721c24;
      font-weight: 500;
      border: 1px solid #f1aeb5;
    }

    .staff-badge {
      background: linear-gradient(135deg, #e2e3e5, #d6d8db);
      color: #495057;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 500;
      border: 1px solid #ced4da;
    }

    .permission-notice.staff-notice {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      color: #6c757d;
    }

    .staff-edit-actions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
      justify-content: space-between;
      align-items: center;
    }

    .staff-edit-actions .btn {
      font-size: 0.85rem;
      padding: 4px 12px;
    }

    #staffLocationEditSection {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background: #f8f9fa;
    }

    #staffLocationEditSection .section-header {
      background: linear-gradient(135deg, #e9ecef, #dee2e6);
      color: #495057;
      margin: -1px -1px 15px -1px;
      padding: 10px 15px;
      border-radius: 6px 6px 0 0;
      border-bottom: 1px solid #ced4da;
    }

    #staffLocationEditSection .form-group {
      margin-bottom: 15px;
    }

    /* Staff location scope styling */
    #staffLocationScope {
      background-position: right 12px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      cursor: pointer !important; /* Override any disabled cursor styling */
      background-color: #ffffff !important; /* Ensure normal background */
      color: #2d3748 !important; /* Ensure normal text color */
      border-color: #e2e8f0 !important; /* Ensure normal border */
      pointer-events: auto !important; /* Ensure it's clickable */
    }

    #staffLocationScopeHelp .text-primary {
      color: #0d6efd !important;
      font-weight: 500;
    }

    #staffLocationScopeHelp .text-warning {
      color: #fd7e14 !important;
      font-weight: 500;
    }

    /* Staff Location Choice Modal Styles */
    .location-choice-options .form-check {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      transition: all 0.3s ease;
    }

    .location-choice-options .form-check:hover {
      border-color: #007bff;
      background-color: #f8f9fa;
    }

    .location-choice-options .form-check-input:checked + .form-check-label {
      color: #007bff;
    }

    .location-choice-options .form-check-input:checked + .form-check-label strong {
      color: #007bff;
    }

    .choice-description {
      margin-top: 8px;
      color: #6c757d;
      font-size: 0.9rem;
    }

    .choice-benefits {
      margin-top: 8px;
    }

    .choice-benefits small {
      font-size: 0.8rem;
    }

    #staffLocationChoiceModal .modal-header {
      background: linear-gradient(135deg, #e9ecef, #dee2e6);
      border-bottom: 1px solid #ced4da;
    }

    #staffLocationChoiceModal .modal-title {
      color: #495057;
      font-weight: 600;
    }

    /* Map disabled state */
    .map-disabled {
      opacity: 0.6;
      pointer-events: none;
      filter: grayscale(30%);
    }

    /* Current Location Display */
    .current-location-display {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
    }

    /* Change Location Actions */
    .change-location-actions {
      margin-bottom: 16px;
      text-align: left;
    }

    .current-location-info {
      flex: 1;
    }

    .location-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
    }

    .location-header i {
      font-size: 18px;
    }

    .location-name {
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
    }



    /* Map preview only (non-interactive) */
    .map-preview-only {
      pointer-events: none;
      position: relative;
    }

    .map-preview-only::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      pointer-events: none;
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      gap: 16px;
    }

    /* Form Groups */
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    /* Modern Labels */
    .modern-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .modern-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Modern Inputs */
    .modern-input,
    .modern-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      color: #2d3748;
      background: #ffffff;
      transition: all 0.3s ease;
      outline: none;
    }

    .modern-input:focus,
    .modern-textarea:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    .modern-input:disabled,
    .modern-input:read-only,
    .modern-textarea:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    .modern-input::placeholder,
    .modern-textarea::placeholder {
      color: #a0aec0;
    }

    /* Location Search Container */
    .location-search-container {
      display: flex;
      align-items: center;
    }

    .location-search-container .modern-input {
      flex: 1;
      margin-bottom: 0;
    }

    /* Location Results */
    .location-results {
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      margin-top: 12px;
      max-height: 300px;
      overflow-y: auto;
    }

    .results-header {
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 600;
      color: #495057;
    }

    .results-list {
      padding: 0;
    }

    .result-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f1f3f4;
      cursor: pointer;
      transition: all 0.2s ease;
      justify-content: space-between;
    }

    .result-item:hover {
      background: #f8f9fa;
    }

    .result-item:last-child {
      border-bottom: none;
    }

    .result-item.selected {
      background: #e3f2fd;
      border-color: #2196f3;
    }

    .result-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .result-icon {
      width: 32px;
      height: 32px;
      background: #e3f2fd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #2196f3;
    }

    .result-details h6 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .result-details small {
      color: #666;
      font-size: 12px;
    }

    .result-type {
      background: #e3f2fd;
      color: #2196f3;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    /* Selected Location Info */
    .selected-location-info {
      background: #e8f5e8;
      border: 1px solid #4caf50;
      border-radius: 8px;
      padding: 16px;
    }

    .location-actions {
      display: flex;
      gap: 8px;
    }

    /* Map Styles */
    .map-container {
      margin: 12px 0 0 0;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #e2e8f0;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .modern-map {
      height: 300px;
      width: 100%;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .map-container:hover {
      border-color: #667eea;
    }

    /* Map Search Container */
    .map-search-container {
      position: relative;
    }

    /* Suggestions */
    .modern-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 2px solid #e2e8f0;
      border-top: none;
      border-radius: 0 0 8px 8px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      display: none;
    }

    .modern-suggestions.show {
      display: block;
    }

    .suggestion-item {
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid #f1f3f4;
      background: white;
      color: #2d3748;
    }

    .suggestion-item:hover {
      background: #667eea;
      color: white;
    }

    .suggestion-item:last-child {
      border-bottom: none;
    }

    /* Help Text */
    .input-help {
      font-size: 12px;
      color: #718096;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .input-help i {
      color: #667eea;
    }

    /* Validation feedback states */
    .has-error .modern-input,
    .has-error .modern-textarea {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .has-success .modern-input,
    .has-success .modern-textarea {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .has-error .input-help {
      color: #e53e3e;
    }

    .has-success .input-help {
      color: #38a169;
    }

    .has-error .input-help i {
      color: #e53e3e;
    }

    .has-success .input-help i {
      color: #38a169;
    }

    /* Validation Styles */
    .modern-input.is-invalid,
    .modern-textarea.is-invalid {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .modern-input.is-valid,
    .modern-textarea.is-valid {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .invalid-feedback {
      color: #e53e3e;
      font-size: 12px;
      margin-top: 4px;
      display: none;
      align-items: center;
      gap: 6px;
    }

    .modern-input.is-invalid+.invalid-feedback,
    .modern-textarea.is-invalid+.invalid-feedback {
      display: flex;
    }

    .invalid-feedback::before {
      content: "⚠";
      font-size: 14px;
    }

    /* Success States */
    .success-state {
      background: #d4edda;
      border-color: #28a745;
      color: #155724;
    }

    /* Loading States */
    .loading {
      position: relative;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Search button loading state */
    .btn.loading {
      opacity: 0.8;
      cursor: not-allowed;
    }

    .btn.loading i {
      animation: spin 1s linear infinite;
    }

    /* Smooth transitions for section visibility */
    .location-results,
      .form-section {
      transition: all 0.3s ease;
    }

    .location-results.hide {
      opacity: 0;
      transform: translateY(-10px);
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* Responsive Design */
    @media (max-width: 992px) {
      .desktop-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .create-event-modal {
        max-width: 800px;
      }

      .modern-map {
        height: 250px;
      }
    }

    @media (max-width: 768px) {
      .form-content {
        padding: 20px 16px;
      }

      .form-section {
        padding: 16px;
      }

      .location-search-container {
        flex-direction: column;
        gap: 8px;
      }

      .location-search-container .btn {
        align-self: stretch;
      }

      .location-actions {
        flex-direction: column;
      }
    }
  </style>

  <script>
    // Function to initialize edit event location handler (called externally when modal loads)
    function initializeEditEventLocationHandler() {
      console.log('🚀 Initializing Edit Event Location Handler...');

      // Check if already initialized and working
      if (window.editEventLocationHandler && window.editEventLocationHandler.currentMap) {
        console.log('⚠️ Edit event location handler already initialized and has active map, skipping...');
        return true;
      }

      try {
        // Clean up existing instance if it exists
        if (window.editEventLocationHandler && typeof window.editEventLocationHandler.cleanup === 'function') {
          console.log('🧹 Cleaning up existing handler...');
          window.editEventLocationHandler.cleanup();
        }

        // Initialize edit location workflow
        window.editEventLocationHandler = new EditEventLocationHandler();
        console.log('✅ Edit event location handler initialized successfully');
        return true;
      } catch (error) {
        console.error('❌ Failed to initialize edit event location handler:', error);
        return false;
      }
    }

    // Make initialization function globally available
    window.initializeEditEventLocationHandler = initializeEditEventLocationHandler;

    // Auto-initialize if DOM is already ready (for direct page loads)
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeEditEventLocationHandler);
    } else {
      // DOM is already ready, initialize immediately
      setTimeout(initializeEditEventLocationHandler, 100);
    }

    // LocationSelector class removed - functionality handled by detail.html
    if (typeof window.LocationSelector === 'undefined') {
      // Placeholder class to prevent errors
      class LocationSelector {
        constructor() {
          // Minimal placeholder - functionality handled by detail.html
          console.log('LocationSelector placeholder initialized');
        }

        // Placeholder methods - functionality handled by detail.html
        initializeElements() { }

        bindEvents() { }
        performSearch() { }
        showSearchLoading() { }
        searchDatabase() { }
        searchMap() { }
        displaySearchResults() { }
        createResultItem() { }
        selectExistingLocation() { }
        selectNewLocation() { }
        updateLocationDisplay() { }
        initializeMap() { }
        initializeMapForNewLocation() { }
        debounceMapSearch() { }
        performMapSearch() { }
        displayMapSuggestions() { }
        selectMapSuggestion() { }
        validateNewLocationForm() { }
        checkLocationNameUniqueness() { }
        extractLocationName() { }
        setState() { }
        showLoading() { }
        hideLoading() { }
        showError() { }
        showSuccess() { }
        handleClickOutside() { }
        hideSearchSection() { }
        showSearchSection() { }
        hideNewLocationCreation() { }
        changeLocation() { }
        editMapLocation() { }
        debounceNameValidation() { }
        reverseGeocode() { }
        cleanup() { }

        // All method implementations removed - functionality handled by detail.html
      }

      // Make LocationSelector available globally
      window.LocationSelector = LocationSelector;
    }

    // Initialize the location selector when the modal is loaded
    /**
     * EditEventLocationHandler - Handles the edit event location workflow
     * 1. Shows current location with map preview (non-interactive)
     * 2. When "Change Location" clicked, shows search workflow (same as create event)
     */
    class EditEventLocationHandler {
      constructor() {
        this.currentMap = null;
        this.locationSelector = null;
        this.bindElements();
        this.setupEventListeners();

        // Delay map initialization to ensure DOM is ready
        setTimeout(() => {
          this.initializeCurrentLocationMap();
        }, 200);
      }

      bindElements() {
        this.elements = {
          // Current location display elements
          currentLocationSection: document.getElementById('currentLocationSection'),
          currentMapSection: document.getElementById('currentMapSection'),
          currentMap: document.getElementById('currentMap'),

          // Owner-only elements (may not exist for staff)
          changeLocationBtn: document.getElementById('changeLocationBtn'),

          // Staff-only elements (may not exist for owners)
          editLocationNameBtn: document.getElementById('editLocationNameBtn'),
          staffLocationEditSection: document.getElementById('staffLocationEditSection'),

          // Search workflow elements (same as create event)
          locationSearchSection: document.getElementById('locationSearchSection'),
          selectedLocationSection: document.getElementById('selectedLocationSection'),
          mapSection: document.getElementById('mapSection'),
          backToSearchBtn: document.getElementById('backToSearchBtn'),

          // Form elements
          locationInput: document.getElementById('location'),
          latitudeInput: document.getElementById('latitude'),
          longitudeInput: document.getElementById('longitude')
        };

        // Check for missing critical elements only
        const criticalElements = ['currentLocationSection', 'currentMapSection', 'currentMap', 'locationInput'];
        const missingCritical = criticalElements.filter(key => !this.elements[key]);

        if (missingCritical.length > 0) {
          console.error('❌ Missing critical elements:', missingCritical);
        }

        // Log which conditional elements are available
        const conditionalElements = ['changeLocationBtn', 'editLocationNameBtn'];
        const availableConditional = conditionalElements.filter(key => this.elements[key]);
        console.log('✅ Available conditional elements:', availableConditional);
      }

      setupEventListeners() {
        // Change location button - switch to search workflow (for owners)
        if (this.elements.changeLocationBtn) {
          this.elements.changeLocationBtn.addEventListener('click', () => {
            this.showSearchWorkflow();
          });
        }

        // Back to search button - return to search from selected location
        if (this.elements.backToSearchBtn) {
          this.elements.backToSearchBtn.addEventListener('click', () => {
            this.backToSearch();
          });
        }

        // Staff location name editing buttons
        if (this.elements.editLocationNameBtn) {
          this.elements.editLocationNameBtn.addEventListener('click', () => {
            this.showStaffLocationNameEdit();
          });
        }

        const cancelLocationEditBtn = document.getElementById('cancelLocationEditBtn');
        if (cancelLocationEditBtn) {
          cancelLocationEditBtn.addEventListener('click', () => {
            this.hideStaffLocationNameEdit();
          });
        }

        // Add location name validation for staff editing
        const staffLocationNameInput = document.getElementById('staffLocationName');
        if (staffLocationNameInput) {
          staffLocationNameInput.addEventListener('input', () => {
            this.validateStaffLocationName();
          });
          staffLocationNameInput.addEventListener('blur', () => {
            this.validateStaffLocationName();
          });
        }

        // Add scope selection change handler
        const staffLocationScopeSelect = document.getElementById('staffLocationScope');
        if (staffLocationScopeSelect) {
          staffLocationScopeSelect.addEventListener('change', () => {
            this.updateScopeHelpText();
          });
        }

        // Handle staff location choice modal
        const confirmLocationChangeBtn = document.getElementById('confirmLocationChange');
        if (confirmLocationChangeBtn) {
          confirmLocationChangeBtn.addEventListener('click', () => {
            this.handleStaffLocationChoice();
          });
        }

        // Intercept form submission for staff location changes
        this.setupStaffFormSubmissionHandler();
      }

      initializeCurrentLocationMap() {
        console.log('🗺️ Initializing current location map...');

        // Check if map is already initialized
        if (this.currentMap) {
          console.log('⚠️ Map already initialized, skipping...');
          return;
        }

        // Check if Leaflet is available
        if (typeof L === 'undefined') {
          console.error('❌ Leaflet library not available');
          return;
        }

        const lat = this.elements.latitudeInput?.value;
        const lng = this.elements.longitudeInput?.value;
        const locationName = this.elements.locationInput?.value;

        console.log('🔍 Map initialization debug:', {
          lat: lat,
          lng: lng,
          locationName: locationName,
          mapElement: !!this.elements.currentMap,
          mapElementId: this.elements.currentMap?.id,
          latInput: !!this.elements.latitudeInput,
          lngInput: !!this.elements.longitudeInput,
          locationInput: !!this.elements.locationInput,
          mapAlreadyExists: !!this.currentMap
        });

        if (lat && lng && this.elements.currentMap) {
          try {
            // Check if the map container already has a Leaflet instance
            if (this.elements.currentMap._leaflet_id) {
              console.log('⚠️ Map container already has Leaflet instance, clearing...');
              // Clear the container
              this.elements.currentMap._leaflet_id = undefined;
              this.elements.currentMap.innerHTML = '';
            }

            // Initialize map with current location
            this.currentMap = L.map(this.elements.currentMap, {
              zoomControl: false,
              dragging: false,
              touchZoom: false,
              doubleClickZoom: false,
              scrollWheelZoom: false,
              boxZoom: false,
              keyboard: false
            }).setView([parseFloat(lat), parseFloat(lng)], 13);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
              attribution: '© OpenStreetMap contributors'
            }).addTo(this.currentMap);

            // Add marker
            L.marker([parseFloat(lat), parseFloat(lng)])
              .addTo(this.currentMap)
              .bindPopup(locationName || 'Event Location')
              .openPopup();

            console.log(`✅ Current location map initialized: ${locationName} (${lat}, ${lng})`);
          } catch (error) {
            console.error('❌ Failed to initialize current location map:', error);
            // Try to clean up on error
            if (this.currentMap) {
              try {
                this.currentMap.remove();
              } catch (cleanupError) {
                console.warn('⚠️ Error during map cleanup:', cleanupError);
              }
              this.currentMap = null;
            }
          }
        } else {
          console.log('⚠️ Cannot initialize map - missing required data or elements');
          console.log('Missing:', {
            lat: !lat ? 'latitude' : null,
            lng: !lng ? 'longitude' : null,
            mapElement: !this.elements.currentMap ? 'map element' : null
          });
        }
      }

      showSearchWorkflow() {
        console.log('🔄 Switching to search workflow...');

        // Hide current location display
        if (this.elements.currentLocationSection) {
          this.elements.currentLocationSection.style.display = 'none';
        }
        if (this.elements.currentMapSection) {
          this.elements.currentMapSection.style.display = 'none';
        }

        // Show search section
        if (this.elements.locationSearchSection) {
          this.elements.locationSearchSection.style.display = 'block';
        }

        // Initialize location selector if not already done
        if (!this.locationSelector) {
          // Check if LocationSelector is available
          if (typeof window.LocationSelector === 'undefined') {
            console.error('❌ LocationSelector class not available');
            return;
          }

          try {
            this.locationSelector = new window.LocationSelector();
            console.log('✅ LocationSelector initialized for search workflow');
          } catch (error) {
            console.error('❌ Failed to initialize LocationSelector:', error);
            return;
          }
        }

        console.log('✅ Search workflow activated');
      }

      showStaffLocationNameEdit() {
        console.log('🔧 Showing staff location name edit interface');

        // Hide current location display
        const currentLocationSection = document.getElementById('currentLocationSection');
        const staffLocationEditSection = document.getElementById('staffLocationEditSection');

        if (currentLocationSection) {
          currentLocationSection.style.display = 'none';
        }

        if (staffLocationEditSection) {
          staffLocationEditSection.style.display = 'block';

          // Initialize scope help text
          this.updateScopeHelpText();

          // Focus on the input field
          const staffLocationNameInput = document.getElementById('staffLocationName');
          if (staffLocationNameInput) {
            setTimeout(() => {
              staffLocationNameInput.focus();
              staffLocationNameInput.select();
            }, 100);
          }
        }
      }

      hideStaffLocationNameEdit() {
        console.log('🔧 Hiding staff location name edit interface');

        // Show current location display
        const currentLocationSection = document.getElementById('currentLocationSection');
        const staffLocationEditSection = document.getElementById('staffLocationEditSection');

        if (currentLocationSection) {
          currentLocationSection.style.display = 'block';
        }

        if (staffLocationEditSection) {
          staffLocationEditSection.style.display = 'none';

          // Reset the input field to original value
          const staffLocationNameInput = document.getElementById('staffLocationName');
          const originalLocationName = document.querySelector('.location-name').textContent;
          if (staffLocationNameInput && originalLocationName) {
            staffLocationNameInput.value = originalLocationName;
            // Clear any validation states
            staffLocationNameInput.classList.remove('is-invalid', 'is-valid');
            const errorDiv = document.getElementById('staffLocationNameError');
            if (errorDiv) {
              errorDiv.style.display = 'none';
            }
          }
        }
      }

      async validateStaffLocationName() {
        const staffLocationNameInput = document.getElementById('staffLocationName');
        const errorDiv = document.getElementById('staffLocationNameError');

        if (!staffLocationNameInput || !errorDiv) return;

        const locationName = staffLocationNameInput.value.trim();
        const originalLocationName = document.querySelector('.location-name').textContent.trim();

        // Clear previous validation states
        staffLocationNameInput.classList.remove('is-invalid', 'is-valid');
        errorDiv.style.display = 'none';

        // Skip validation if empty or same as original
        if (!locationName) {
          errorDiv.textContent = 'Location name is required.';
          errorDiv.style.display = 'block';
          staffLocationNameInput.classList.add('is-invalid');
          return false;
        }

        if (locationName === originalLocationName) {
          // Same as original - valid but no change needed
          staffLocationNameInput.classList.add('is-valid');
          return true;
        }

        // Check for minimum length
        if (locationName.length < 2) {
          errorDiv.textContent = 'Location name must be at least 2 characters long.';
          errorDiv.style.display = 'block';
          staffLocationNameInput.classList.add('is-invalid');
          return false;
        }

        try {
          // Check if location name already exists in database
          const response = await fetch(`/location/search?query=${encodeURIComponent(locationName)}`);
          if (!response.ok) {
            throw new Error('Failed to validate location name');
          }

          const existingLocations = await response.json();
          const nameExists = existingLocations.some(loc =>
            loc.name.toLowerCase() === locationName.toLowerCase()
          );

          if (nameExists) {
            errorDiv.textContent = 'This location name already exists. Please choose a different name.';
            errorDiv.style.display = 'block';
            staffLocationNameInput.classList.add('is-invalid');
            return false;
          } else {
            // Name is available
            staffLocationNameInput.classList.add('is-valid');
            return true;
          }
        } catch (error) {
          console.error('Error validating location name:', error);
          errorDiv.textContent = 'Could not validate location name. Please try again.';
          errorDiv.style.display = 'block';
          staffLocationNameInput.classList.add('is-invalid');
          return false;
        }
      }

      setupStaffFormSubmissionHandler() {
        console.log('🔧 Setting up staff form submission handler');

        // Find the main form
        const form = document.querySelector('form');
        if (!form) {
          console.warn('⚠️ Main form not found for staff submission handler');
          return;
        }

        // Add form submission validation for staff location edits
        form.addEventListener('submit', async (e) => {
          const staffLocationEditSection = document.getElementById('staffLocationEditSection');
          if (staffLocationEditSection && staffLocationEditSection.style.display !== 'none') {
            // Staff is editing location name, validate it
            const staffLocationNameInput = document.getElementById('staffLocationName');
            const staffLocationScopeSelect = document.getElementById('staffLocationScope');

            if (staffLocationNameInput && staffLocationScopeSelect) {
              const locationName = staffLocationNameInput.value.trim();
              const scope = staffLocationScopeSelect.value;

              if (!locationName) {
                e.preventDefault();
                alert('Please enter a location name.');
                staffLocationNameInput.focus();
                return false;
              }

              if (!scope) {
                e.preventDefault();
                alert('Please select an update scope.');
                staffLocationScopeSelect.focus();
                return false;
              }

              // Check if the input has validation errors
              if (staffLocationNameInput.classList.contains('is-invalid')) {
                e.preventDefault();
                alert('Please fix the location name error before submitting.');
                staffLocationNameInput.focus();
                return false;
              }

              // Run validation one more time to be sure
              const isValid = await this.validateStaffLocationName();
              if (!isValid) {
                e.preventDefault();
                alert('Please fix the location name error before submitting.');
                staffLocationNameInput.focus();
                return false;
              }

              console.log(`✅ Staff location edit validated: "${locationName}" with scope "${scope}"`);
            }
          }
        });

        console.log('✅ Staff form submission handler set up successfully');
      }

      updateScopeHelpText() {
        const staffLocationScopeSelect = document.getElementById('staffLocationScope');
        const scopeHelpText = document.getElementById('scopeHelpText');

        if (!staffLocationScopeSelect || !scopeHelpText) return;

        const selectedValue = staffLocationScopeSelect.value;

        if (selectedValue === 'all_events') {
          scopeHelpText.textContent = 'This will change the name for ALL events using this location.';
          scopeHelpText.className = 'text-primary';
        } else if (selectedValue === 'this_event_only') {
          scopeHelpText.textContent = 'This will create a new location and only affect this event.';
          scopeHelpText.className = 'text-warning';
        }
      }

      backToSearch() {
        console.log('🔄 Returning to search...');

        // Hide selected location and map sections
        if (this.elements.selectedLocationSection) {
          this.elements.selectedLocationSection.style.display = 'none';
        }
        if (this.elements.mapSection) {
          this.elements.mapSection.style.display = 'none';
        }

        // Show search section
        if (this.elements.locationSearchSection) {
          this.elements.locationSearchSection.style.display = 'block';
        }

        // Clear search input and results
        const searchInput = document.getElementById('locationSearch');
        const searchResults = document.getElementById('searchResults');
        if (searchInput) searchInput.value = '';
        if (searchResults) searchResults.style.display = 'none';

        console.log('✅ Returned to search');
      }

      cleanup() {
        console.log('🧹 Cleaning up EditEventLocationHandler...');

        // Clean up current map
        if (this.currentMap) {
          this.currentMap.remove();
          this.currentMap = null;
        }

        // Clean up location selector
        if (this.locationSelector && typeof this.locationSelector.cleanup === 'function') {
          this.locationSelector.cleanup();
          this.locationSelector = null;
        }

        console.log('✅ EditEventLocationHandler cleaned up');
      }
    }

    // Make EditEventLocationHandler globally available
    window.EditEventLocationHandler = EditEventLocationHandler;
  </script>
</div>
{% endblock %}