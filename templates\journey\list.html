{% extends "base.html" %}
{% from "components/pagination.html" import render_pagination %}

{% block title %}My Journeys - Footprints{% endblock %}

{% block content %}
<!-- Header -->
<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="display-6 fw-bold">
      <span class="position-relative">
        My Journeys
        <span class="position-absolute start-0 bottom-0"
          style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
      </span>
    </h1>
    <button id="createJourneyBtn" class="btn rounded-pill px-3 py-2"
      style="background-color: #4e6bff; color: white; border: none;">
      <i class=" bi bi-plus-lg me-2"></i>Add journey</button>
  </div>

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold mb-0">Total ({{ total_count|default(0) }})</h5>
    <div class="d-flex gap-2 align-items-center">
      <form action="{{ url_for('journey.get_private_journeys') }}" method="get" class="d-flex gap-2">
        <div class="input-group">
          <input type="text" class="form-control" name="search" placeholder="Search journeys..."
            value="{{ request.args.get('search', '') }}">
          <a href="{{ url_for('journey.get_private_journeys') }}"
            class="btn btn-outline-secondary search-clear-btn {% if not request.args.get('search') %}d-none{% endif %}">
            <i class="bi bi-x"></i>
          </a>

        </div>
        <button type="submit" class="btn" style="background-color: black; color: white; border: none;">Search</button>
      </form>
    </div>
  </div>

  <!-- Journey list -->
  {% if journeys %}
  <div class=" journey-list">
    {% for journey in journeys %}
    <div id="{% if journey.id == new_journey_id %}newJourney{% endif %}"
      class="journey-card border-0 shadow-sm {% if journey.id == new_journey_id %}table-active{% endif %}"
      style="cursor: pointer;">
      <a href="{{ url_for('journey.get_private_journey', journey_id=journey.id) }}"
        class="text-decoration-none text-dark d-block p-4">
        <div class="row g-4 align-items-center">
          <div class="col-12 col-md-5 col-lg-3">
            <div class="image-container"
              style="width: 100%; height: 200px; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px; overflow: hidden;">
              {% if journey.cover_image %}
              <img src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                alt="{{ journey.title }}" width="100%" height="100%" style="object-fit: cover;">
              {% elif journey.event_image%}
              <img src="{{ url_for('static', filename='uploads/event_images/' + journey.event_image) }}"
                alt="{{ journey.title }}" width="100%" height="100%" style="object-fit: cover;"
                onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
              {% else %}
              <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                alt="Journey placeholder" width="100%" height="100%" style="object-fit: cover;">
              {% endif %}
            </div>
          </div>

          <div class="col-12 col-lg-6 col-md-5">
            <h4 class="mb-2">{{ journey.title }}</h4>
            <div class="d-flex flex-wrap gap-3 text-muted mb-2">
              <div>
                <i class="bi bi-calendar3 me-1"></i>
                {{ journey.start_date.strftime('%d/%m/%Y') }}
              </div>
              <div>
                <i class="bi bi-collection me-1"></i>
                {% if journey.event_count == 1 %}
                {{ journey.event_count }} event
                {% else %}
                {{ journey.event_count }} events
                {% endif %}
              </div>
            </div>
            <small class="text-muted d-block mb-2">Updated: {{ journey.updated_at.strftime('%d/%m/%Y %H:%M') }}</small>
            <p class="mb-1 text-muted">{{ journey.description|truncate(100) }}</p>
          </div>

          <div class="col-12 col-lg-3 col-md-2 d-flex justify-content-center justify-content-md-end align-items-center">
            <span
              class="badge rounded-pill {% if journey.visibility in ('public', 'published') %}text-bg-success{% else %}text-bg-secondary{% endif %} px-3 py-2">
              {% if journey.visibility in ('public', 'published') %}{{ journey.visibility|capitalize }}{% else
              %}Private{% endif %}
            </span>
          </div>
        </div>
      </a>
    </div>
    {% endfor %}
  </div>

  <!-- Pagination -->
  {{ render_pagination(page, total_pages, 'journey.get_private_journeys', q=request.args.get('search', ''),
  filter=request.args.get('filter')) }}

  {% else %}
  <div class="alert"
    style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
    {% if request.args.get('search') %}
    <p class="mb-0"><i class="bi bi-info-circle me-2"></i>No matching journeys found.</p>
    {% else %}
    <p class="mb-0"><i class="bi bi-journal-plus me-2"></i>You haven't created any journeys yet.</p>
    {% endif %}
  </div>
  {% endif %}
</div>

<script>
  // scroll to newly created journey
  document.addEventListener("DOMContentLoaded", function () {
    const newJourney = document.getElementById("newJourney");
    if (newJourney) {
      newJourney.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  });

  // shows modal for creating new journey
  document.getElementById('createJourneyBtn').addEventListener('click', async function (event) {
    event.preventDefault();

    const response = await fetch("{{ url_for('journey.get_private_journey_form')}}");
    const formHtml = await response.text();

    showModal('Add New Journey', formHtml, {
      actionText: 'Create',
      onAction: function () {
        const form = document.getElementById('create-journey-form');
        form.classList.add('was-validated');

        if (!form.checkValidity()) {
          // Focus the first invalid field instead of showing browser popup
          const firstInvalidField = form.querySelector(':invalid');
          if (firstInvalidField) {
            firstInvalidField.focus();
          }
          return false; // Prevent modal from closing
        }

        form.submit();
        return true; // Allow modal to close
      }
    });

    // Initialize image preview after modal is shown
    setTimeout(() => {
      initializeCreateJourneyImagePreview();
    }, 200);
  });


  function previewImage(input) {
    const preview = document.getElementById('current-image-preview');
    const container = document.getElementById('image-preview-container');

    if (input.files && input.files[0]) {
      const reader = new FileReader();

      reader.onload = function (e) {
        if (preview) {
          preview.src = e.target.result;
        } else {
          const img = document.createElement('img');
          img.id = 'current-image-preview';
          img.src = e.target.result;
          img.className = 'img-fluid rounded w-100';
          img.style.maxHeight = '300px';
          img.style.objectFit = 'cover';

          // Clear placeholder and add the image
          container.innerHTML = '';
          container.appendChild(img);
        }
      };

      reader.readAsDataURL(input.files[0]);
    }
  }

  // Initialize modular image preview for create journey modal
  function initializeCreateJourneyImagePreview() {
    const imageInput = document.getElementById('image');
    if (!imageInput) {
      console.log('Image input not found in create journey modal');
      return;
    }

    // Load required scripts and CSS
    const scripts = [
      '/static/js/file-validation.js',
      '/static/js/image-preview.js'
    ];

    const css = '/static/css/image-preview.css';

    // Load CSS if not already loaded
    if (!document.querySelector(`link[href="${css}"]`)) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = css;
      document.head.appendChild(link);
    }

    // Load scripts sequentially
    loadScriptsSequentially(scripts).then(() => {
      // Setup image preview for single image (journey cover)
      if (window.setupImagePreview) {
        window.setupImagePreview('#image', {
          maxFiles: 1,
          allowMultiple: false,
          containerSelector: '#imagePreviewContainer',
          gridSelector: '#previewGrid',
          feedbackSelector: '#imageFeedback',

          // Custom callbacks for journey cover image
          onFileSelect: function(files) {
            console.log('Journey cover image selected:', files.length);
            // Also update the placeholder image
            updatePlaceholderImage(files[0]);
          },
          onFileRemove: function(index, remainingFiles) {
            console.log('Journey cover image removed');
            // Reset placeholder image
            resetPlaceholderImage();
          },
          onClear: function() {
            console.log('Journey cover image cleared');
            // Reset placeholder image
            resetPlaceholderImage();
          },
          onValidationError: function(message) {
            console.log('Journey cover image validation error:', message);
          }
        });
        console.log('Modular image preview initialized for create journey modal');
      } else {
        console.error('setupImagePreview function not available');
      }
    }).catch(error => {
      console.error('Failed to load image preview scripts:', error);
    });
  }

  // Update placeholder image when file is selected
  function updatePlaceholderImage(file) {
    if (!file) return;

    const preview = document.getElementById('current-image-preview');
    if (!preview) return;

    const reader = new FileReader();
    reader.onload = function(e) {
      preview.src = e.target.result;
      preview.style.objectFit = 'cover';
    };
    reader.readAsDataURL(file);
  }

  // Reset placeholder image to default
  function resetPlaceholderImage() {
    const preview = document.getElementById('current-image-preview');
    if (preview) {
      preview.src = "{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}";
      preview.style.objectFit = 'cover';
    }
  }

  function loadScriptsSequentially(scripts) {
    return scripts.reduce((promise, script) => {
      return promise.then(() => loadScript(script));
    }, Promise.resolve());
  }

  function loadScript(src) {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
</script>

<style>
  .journey-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .journey-card {
    border-radius: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
  }

  .journey-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .journey-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .journey-card .row {
    height: 100%;
  }

  .image-container {
    width: 220px !important;
    height: 150px !important;
    border: 1px solid rgba(78, 107, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
  }

  @media (max-width: 767px) {
    .journey-card .row {
      flex-direction: column;
    }

    .journey-card img {
      width: 100%;
      height: auto;
    }

    .journey-card .col-md-3 .col-md-6 {
      margin-bottom: 1rem;
    }
  }

  button[style*="background-color: #4e6bff"]:hover {
    background-color: #3a55e8 !important;
  }
</style>

{% endblock %}