{% extends "base.html" %}

{% block title %}Edit History - {{ journey.title }}{% endblock %}

{% block content %}
<div class="container"
     data-is-logged-in="{{ 'true' if g.current_user else 'false' }}"
     data-journey-private-url="{{ url_for('journey.get_private_journey', journey_id=journey.id) }}"
     data-journey-public-url="{{ url_for('journey.get_public_journey', journey_id=journey.id) }}"
     data-landing-url="{{ url_for('main.get_landing_page') }}"
     data-journey-title="{{ journey.title }}"
     data-journey-visibility="{{ journey.visibility }}">
  <a href="javascript:void(0)" onclick="smartBack()" class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
    <i class="bi bi-arrow-left me-2"></i>
    <span id="backButtonText">Back</span>
  </a>
  <div class="card shadow border-0 mb-4">
    <div class="card-header bg-white border-0 py-3">
      <div class="d-flex justify-content-between align-items-center">
        <h1 class="fs-4 fw-bold mb-0">Edit History for "{{ journey.title }}"</h1>
      </div>
    </div>
    <div class="card-body">
      {% if edit_history %}
        {% if is_premium or is_staff %}
          <!-- Full edit history for premium users and staff -->
          <div class="timeline">
            {% for edit in edit_history %}
            <div class="timeline-item">
              <div class="timeline-badge bg-primary">
                <i class="bi bi-pencil text-white"></i>
              </div>
              <div class="timeline-panel">
                <div class="timeline-heading">
                  <h5 class="timeline-title">
                    Edit by {{ edit.editor_username }}
                  </h5>
                  <p>
                    <small class="text-muted">
                      <i class="bi bi-clock"></i> {{ edit.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                    </small>
                  </p>
                </div>
                <div class="timeline-body">
                  <p class="reason"><strong>Reason:</strong> {{ edit.reason }}</p>

                  <div class="changes">
                    <p><strong>Changes:</strong></p>
                    <div class="table-responsive">
                      <table class="table table-sm">
                        <thead>
                          <tr>
                            <th>Field</th>
                            <th>Before</th>
                            <th>After</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for change in edit.field_changes %}
                          <tr>
                            <td>{{ change.field_name|replace('_', ' ')|title }}</td>
                            <td class="text-danger">{{ change.old_value|format_edit_value(change.field_name) }}</td>
                            <td class="text-success">{{ change.new_value|format_edit_value(change.field_name) }}</td>
                          </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <!-- Greyed out content for free users with premium prompt -->
          <div class="position-relative">
            <div class="timeline" style="opacity: 0.3; pointer-events: none;">
              {% for edit in edit_history %}
              <div class="timeline-item">
                <div class="timeline-badge bg-secondary">
                  <i class="bi bi-pencil text-white"></i>
                </div>
                <div class="timeline-panel">
                  <div class="timeline-heading">
                    <h5 class="timeline-title">
                      Edit by {{ edit.editor_username }}
                    </h5>
                    <p>
                      <small class="text-muted">
                        <i class="bi bi-clock"></i> {{ edit.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                      </small>
                    </p>
                  </div>
                  <div class="timeline-body">
                    <p class="reason"><strong>Reason:</strong> {{ edit.reason }}</p>

                    <div class="changes">
                      <p><strong>Changes:</strong></p>
                      <div class="table-responsive">
                        <table class="table table-sm">
                          <thead>
                            <tr>
                              <th>Field</th>
                              <th>Before</th>
                              <th>After</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for change in edit.field_changes %}
                            <tr>
                              <td>{{ change.field_name|replace('_', ' ')|title }}</td>
                              <td class="text-danger">{{ change.old_value or 'None' }}</td>
                              <td class="text-success">{{ change.new_value or 'None' }}</td>
                            </tr>
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>

            <!-- Premium upgrade overlay -->
            <div class="position-absolute top-50 start-50 translate-middle text-center bg-white p-4 rounded shadow-lg border" style="z-index: 10;">
              <div class="mb-3">
                <i class="bi bi-star-fill fs-1 text-warning"></i>
              </div>
              <h4 class="mb-3">Premium Feature</h4>
              <p class="text-muted mb-3">Upgrade to Premium to access complete edit history for all your content.</p>
              <a href="http://127.0.0.1:5000/account/profile?active_tab=subscription" class="btn btn-warning">
                <i class="bi bi-star me-2"></i>Upgrade to Premium
              </a>
            </div>
          </div>
        {% endif %}
      {% else %}
        <!-- No edit history message -->
        <div class="text-center py-5">
          <div class="mb-4">
            <i class="bi bi-clock-history fs-1 text-muted"></i>
          </div>
          <h4 class="text-muted mb-3">No Edit History</h4>
          <p class="text-muted mb-0">This journey hasn't been edited yet. Any future changes made by staff will appear here.</p>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .timeline {
    position: relative;
    padding: 20px 0;
    list-style: none;
    max-width: 1200px;
    margin: 0 auto;
  }

  .timeline:before {
    content: " ";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50px;
    width: 3px;
    background-color: #eee;
    margin-left: -1.5px;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 40px;
    padding-left: 80px;
  }

  .timeline-badge {
    position: absolute;
    top: 0;
    left: 30px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-align: center;
    font-size: 1.4em;
    line-height: 40px;
    margin-left: -20px;
    z-index: 100;
  }

  .timeline-panel {
    position: relative;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
    background-color: #fff;
  }

  .timeline-panel:before {
    content: " ";
    display: inline-block;
    position: absolute;
    top: 26px;
    left: -14px;
    border-top: 14px solid transparent;
    border-right: 14px solid #fff;
    border-bottom: 14px solid transparent;
  }

  .timeline-title {
    margin-top: 0;
    font-weight: 600;
  }

  .changes {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
  }

  @media (max-width: 767px) {
    .timeline:before {
      left: 40px;
    }

    .timeline-item {
      padding-left: 70px;
    }

    .timeline-badge {
      left: 20px;
    }
  }
</style>

<script>
  // Simple and reliable back button function
  function smartBack() {
    // Method 1: Check for explicit back URL parameter (most reliable)
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
      try {
        const decodedUrl = decodeURIComponent(backUrl);
        // Only allow internal URLs for security
        if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
          window.location.href = decodedUrl;
          return;
        }
      } catch (e) {
        // Invalid URL, continue to next method
      }
    }

    // Method 2: Simple history back (works most of the time)
    if (window.history.length > 1) {
      window.history.back();
      return;
    }

    // Method 3: Fallback based on context
    const container = document.querySelector('.container');
    const isLoggedIn = container.dataset.isLoggedIn === 'true';
    const journeyPrivateUrl = container.dataset.journeyPrivateUrl;
    const journeyPublicUrl = container.dataset.journeyPublicUrl;
    const landingUrl = container.dataset.landingUrl;
    const journeyVisibility = container.dataset.journeyVisibility;

    if (isLoggedIn) {
      if (journeyVisibility === 'private' && journeyPrivateUrl) {
        window.location.href = journeyPrivateUrl;
      } else if (journeyPublicUrl) {
        window.location.href = journeyPublicUrl;
      } else {
        window.location.href = '/journey/public';
      }
    } else {
      window.location.href = landingUrl || '/';
    }
  }

  // Update back button text based on context
  function updateBackButtonText() {
    const backButtonText = document.getElementById('backButtonText');
    const referrer = document.referrer;
    const currentDomain = window.location.origin;
    const container = document.querySelector('.container');
    const journeyTitle = container.dataset.journeyTitle;

    if (referrer && referrer.startsWith(currentDomain)) {
      if (referrer.includes('/journey/')) {
        backButtonText.textContent = `Back to Journey`;
      } else if (referrer.includes('/event/')) {
        backButtonText.textContent = 'Back to Event';
      } else if (referrer.includes('/admin') || referrer.includes('/dashboard')) {
        backButtonText.textContent = 'Back to Dashboard';
      } else {
        backButtonText.textContent = 'Back';
      }
    } else {
      // No referrer or external referrer
      backButtonText.textContent = `Back to Journey`;
    }
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', function () {
    updateBackButtonText();
  });
</script>
{% endblock %}