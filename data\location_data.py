from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def create_location(name: str, longitude: float, latitude: float) -> Optional[int]:
    """Create a new location.
    
    Args:
        name: The name of the new location.
        
    Returns:
        int: The ID of the newly created location, or None if creation failed.
    """
    logger.info(f"Creating new location: {name}")

    if latitude is not None and longitude is not None:
        query = """
        INSERT INTO locations (name, latitude, longitude)
        VALUES (%s, %s, %s)
        """
        params = (name, latitude, longitude)
    else:
        query = """
        INSERT INTO locations (name)
        VALUES (%s)
        """
        params = (name,)

    location_id = execute_query(query, params)

    
    if location_id:
        logger.info(f"Created new location with ID: {location_id}")
    else:
        logger.error("Failed to create new location")
    return location_id


def get_location(location_id: int) -> Optional[Dict[str, Any]]:
    """Get a location by ID.
    
    Args:
        location_id: The ID of the location to retrieve.
        
    Returns:
        Dict[str, Any]: Location data if found, None otherwise.
    """
    logger.debug(f"Getting location with ID: {location_id}")
    query = """
    SELECT id, name
    FROM locations
    WHERE id = %s
    """
    result = execute_query(query, (location_id,), fetch_one=True)
    logger.debug(f"Location lookup result: {'Found' if result else 'Not found'}")
    return result

def get_location_by_name(name: str) -> Optional[Dict[str, Any]]:
    """Get a location by name.
    
    Args:
        name: The name of the location to retrieve.
        
    Returns:
        Dict[str, Any]: Location data if found, None otherwise.
    """
    logger.debug(f"Getting location with name: {name}")
    query = """
    SELECT *
    FROM locations
    WHERE name = %s
    """
    result = execute_query(query, (name,), fetch_one=True)
    logger.debug(f"Location lookup result: {'Found' if result else 'Not found'}")
    return result

def get_locations(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all locations with basic pagination.
    
    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of location records.
    """
    logger.debug(f"Getting locations with limit: {limit}, offset: {offset}")
    query = """
    SELECT id, name
    FROM locations
    ORDER BY name
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} locations")
    return results or []

def get_locations_with_count(limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all locations with event counts.
    
    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of location records with event counts.
    """
    logger.debug(f"Getting locations with event counts, limit: {limit}, offset: {offset}")
    query = """
    SELECT l.id, l.name, COUNT(e.id) as event_count
    FROM locations l
    LEFT JOIN events e ON l.id = e.location_id
    GROUP BY l.id, l.name
    ORDER BY l.name
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} locations with event counts")
    return results or []

def get_total_locations_count() -> int:
    """Get total count of all locations.
    
    Returns:
        int: Total count of locations.
    """
    logger.debug("Counting all locations")
    query = """
    SELECT COUNT(*) as count
    FROM locations
    """
    result = execute_query(query, (), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} total locations")
    return count

def search_locations(search_term: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for locations by name.
    
    Args:
        search_term: Term to search for in location names.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of matching location records.
    """
    logger.debug(f"Searching locations with term: '{search_term}'")
    search_pattern = f"%{search_term}%"
    query = """
    SELECT id, name
    FROM locations
    WHERE name LIKE %s
    ORDER BY name
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (search_pattern, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} locations matching search term: '{search_term}'")
    return results or []

def count_search_locations(search_term: str) -> int:
    """Count locations matching a search term.
    
    Args:
        search_term: Term to search for in location names.
        
    Returns:
        int: Count of matching locations.
    """
    logger.debug(f"Counting locations matching search term: '{search_term}'")
    search_pattern = f"%{search_term}%"
    query = """
    SELECT COUNT(*) as count
    FROM locations
    WHERE name LIKE %s
    """
    result = execute_query(query, (search_pattern,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} locations matching search term: '{search_term}'")
    return count

def search_locations_with_count(search_term: str, limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for locations by name with event counts.
    
    Args:
        search_term: Term to search for in location names.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of matching location records with event counts.
    """
    logger.debug(f"Searching locations with event counts, term: '{search_term}'")
    search_pattern = f"%{search_term}%"
    query = """
    SELECT l.id, l.name, COUNT(e.id) as event_count
    FROM locations l
    LEFT JOIN events e ON l.id = e.location_id
    WHERE l.name LIKE %s
    GROUP BY l.id, l.name
    ORDER BY l.name
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (search_pattern, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} locations with event counts matching search term: '{search_term}'")
    return results or []

def search_locations_count(search_term: str) -> int:
    """Get count of locations matching search term.
    
    Args:
        search_term: Term to search for in location names.
        
    Returns:
        int: Count of matching locations.
    """
    logger.debug(f"Counting locations matching search term: '{search_term}'")
    search_pattern = f"%{search_term}%"
    query = """
    SELECT COUNT(*) as count
    FROM locations
    WHERE name LIKE %s
    """
    result = execute_query(query, (search_pattern,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} locations matching search term: '{search_term}'")
    return count

def update_location(location_id: int, name: str) -> int:
    """Update a location's name.
    
    Args:
        location_id: The ID of the location to update.
        name: The new name for the location.
        
    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating location ID: {location_id} to name: {name}")
    query = """
    UPDATE locations
    SET name = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (name, location_id))
    logger.info(f"Updated location ID: {location_id}, rows affected: {rows_affected}")
    return rows_affected

def merge_locations(source_id: int, target_id: int) -> bool:
    """Merge source location into target location.
    
    Args:
        source_id: Source location ID to merge from.
        target_id: Target location ID to merge into.
        
    Returns:
        bool: True if successful, False otherwise.
    """
    logger.info(f"Merging location ID: {source_id} into location ID: {target_id}")
    # Begin transaction
    execute_query("BEGIN")
    
    try:
        # Update all events from source location to target location
        update_query = """
        UPDATE events
        SET location_id = %s
        WHERE location_id = %s
        """
        updated_events = execute_query(update_query, (target_id, source_id))
        logger.debug(f"Updated {updated_events} events from location ID: {source_id} to location ID: {target_id}")
        
        # Delete the source location
        delete_query = """
        DELETE FROM locations
        WHERE id = %s
        """
        deleted = execute_query(delete_query, (source_id,))
        logger.debug(f"Deleted location ID: {source_id}, result: {deleted}")
        
        # Commit transaction
        execute_query("COMMIT")
        logger.info(f"Successfully merged location ID: {source_id} into location ID: {target_id}")
        return True
        
    except Exception as e:
        # Rollback on error
        execute_query("ROLLBACK")
        logger.error(f"Failed to merge locations: {e}")
        raise e

def get_user_visited_locations(user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """Get locations visited by a user.
    
    Args:
        user_id: The ID of the user to get visited locations for.
        limit: Maximum number of locations to return.
        
    Returns:
        List[Dict[str, Any]]: List of location objects with visit counts.
    """
    logger.debug(f"Getting visited locations for user ID: {user_id}")
    query = """
    SELECT DISTINCT l.id, l.name, COUNT(e.id) as visit_count
    FROM locations l
    JOIN events e ON l.id = e.location_id
    JOIN journeys j ON e.journey_id = j.id
    WHERE j.user_id = %s
    GROUP BY l.id
    ORDER BY visit_count DESC
    LIMIT %s
    """
    results = execute_query(query, (user_id, limit), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} visited locations for user ID: {user_id}")
    return results or []

def get_locations_for_journey(journey_id: int) -> List[Dict[str, Any]]:
    """Get all locations associated with events in a specific journey.

    Args:
        journey_id: The ID of the journey to get locations for.

    Returns:
        List[Dict[str, Any]]: List of location objects.
    """
    logger.debug(f"Getting locations for journey ID: {journey_id}")
    query = """
    SELECT DISTINCT l.id, l.name, l.latitude, l.longitude, e.title as event_title, e.id as event_id, e.description as event_description, e.start_datetime as event_start_datetime
    FROM locations l
    JOIN events e ON l.id = e.location_id
    WHERE e.journey_id = %s
    """
    results = execute_query(query, (journey_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} locations for journey ID: {journey_id}")
    return results or []
