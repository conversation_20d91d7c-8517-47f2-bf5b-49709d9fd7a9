"""
Function Usage Tracking Test Runner

This script runs comprehensive tests to track function usage
and generate reports for identifying unused code.
"""

import subprocess
import sys
import os
import json
from datetime import datetime


def ensure_flask_app_running():
    """Ensure Flask app is running before tests"""
    print("Checking if Flask app is running...")
    
    try:
        import requests
        response = requests.get("http://127.0.0.1:5000/", timeout=5)
        if response.status_code == 200:
            print("✓ Flask app is running")
            return True
    except:
        pass
    
    print("✗ Flask app is not running")
    print("Please start your Flask app with: python app.py")
    return False


def extract_functions_from_templates():
    """Extract functions from templates first"""
    print("\n1. Extracting JavaScript functions from templates...")
    
    try:
        result = subprocess.run([
            sys.executable, 
            "tests/frontend/extract_js_functions.py"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✓ Function extraction completed")
            print(result.stdout)
        else:
            print("✗ Function extraction failed")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ Error running function extraction: {e}")
        return False
    
    return True


def run_playwright_tests():
    """Run Playwright tests with function tracking"""
    print("\n2. Running Playwright tests with function tracking...")
    
    try:
        # Run specific function tracking tests
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/frontend/test_function_usage_tracking.py::test_generate_usage_report",
            "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✓ Playwright tests completed successfully")
            print(result.stdout)
        else:
            print("✗ Playwright tests failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print(f"✗ Error running Playwright tests: {e}")
        return False
    
    return True


def generate_cleanup_recommendations():
    """Generate recommendations for code cleanup"""
    print("\n3. Generating cleanup recommendations...")
    
    # Look for the latest analysis report
    report_dir = "test_reports"
    if not os.path.exists(report_dir):
        print("✗ No test reports found")
        return False
    
    # Find latest analysis file
    analysis_files = [f for f in os.listdir(report_dir) if f.startswith("function_analysis_")]
    if not analysis_files:
        print("✗ No analysis reports found")
        return False
    
    latest_analysis = max(analysis_files)
    analysis_path = os.path.join(report_dir, latest_analysis)
    
    try:
        with open(analysis_path, 'r') as f:
            analysis = json.load(f)
        
        print(f"✓ Analysis loaded from {latest_analysis}")
        
        # Generate recommendations
        recommendations = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_functions_tracked": analysis.get("total_possible_functions", 0),
                "functions_used": len(analysis.get("functions_used", [])),
                "functions_unused": len(analysis.get("functions_unused", [])),
                "usage_percentage": analysis.get("usage_percentage", 0)
            },
            "cleanup_actions": {
                "safe_to_remove": analysis.get("functions_unused", []),
                "files_to_review": [
                    "templates/event/detail.html",
                    "templates/event/edit.html"
                ],
                "search_patterns": [
                    f"function {func}" for func in analysis.get("functions_unused", [])[:10]
                ]
            },
            "next_steps": [
                "1. Review unused functions in templates",
                "2. Search for each unused function in codebase",
                "3. Remove unused JavaScript code blocks",
                "4. Test functionality after removal",
                "5. Run tests again to verify cleanup"
            ]
        }
        
        # Save recommendations
        rec_file = os.path.join(report_dir, f"cleanup_recommendations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(rec_file, 'w') as f:
            json.dump(recommendations, f, indent=2)
        
        print(f"✓ Cleanup recommendations saved to {rec_file}")
        
        # Print summary
        print("\n📊 SUMMARY:")
        print(f"   Functions tracked: {recommendations['summary']['total_functions_tracked']}")
        print(f"   Functions used: {recommendations['summary']['functions_used']}")
        print(f"   Functions unused: {recommendations['summary']['functions_unused']}")
        print(f"   Usage percentage: {recommendations['summary']['usage_percentage']:.1f}%")
        
        if recommendations['summary']['functions_unused'] > 0:
            print(f"\n🧹 CLEANUP OPPORTUNITIES:")
            print(f"   {recommendations['summary']['functions_unused']} unused functions found")
            print(f"   Review files: {', '.join(recommendations['cleanup_actions']['files_to_review'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error generating recommendations: {e}")
        return False


def main():
    """Main function to run complete function tracking workflow"""
    print("🔍 Function Usage Tracking Workflow")
    print("=" * 50)
    
    # Check prerequisites
    if not ensure_flask_app_running():
        return False
    
    # Step 1: Extract functions from templates
    if not extract_functions_from_templates():
        return False
    
    # Step 2: Run Playwright tests
    if not run_playwright_tests():
        return False
    
    # Step 3: Generate cleanup recommendations
    if not generate_cleanup_recommendations():
        return False
    
    print("\n✅ Function tracking workflow completed successfully!")
    print("\nNext steps:")
    print("1. Review the generated reports in test_reports/")
    print("2. Use cleanup recommendations to remove unused code")
    print("3. Test your application after cleanup")
    print("4. Run this script again to verify improvements")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
