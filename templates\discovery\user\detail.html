{% extends "base.html" %}

{% block title %}{{ user.username }}'s Profile - Footprints{% endblock %}

{% block content %}
<div class="container">
  <a href="{{ url_for('journey.get_public_journeys') }}"
    class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
    <i class="bi bi-arrow-left me-2"></i>
    <span>Back to Discovery</span>
  </a>
  <div class="row">
    <div class="col-12">
      <h1 class="display-6 fw-bold">
        <span class="position-relative">
          {{ user.username }}'s Profile
          <span class="position-absolute start-0 bottom-0"
            style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
        </span>
      </h1>
    </div>
  </div>
  <div class="mb-4">
    <div class="border-bottom position-relative">
      <div class="d-flex" id="profileTabNav">
        <div class="me-4 position-relative">
          <a href="{{ url_for('account.get_public_profile', username=user.username, active_tab='profile') }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'profile' %}text-primary{% else %}text-secondary{% endif %}">
            Profile
          </a>
          <div
            class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'profile' %} d-none{% endif %}"
            style="height: 3px; background-color: #6366f1;"></div>
        </div>
        <div class="position-relative">
          <a href="{{ url_for('account.get_public_profile', username=user.username, active_tab='activities') }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'activities' %}text-primary{% else %}text-secondary{% endif %}">
            Activities
          </a>
          <div
            class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'activities' %} d-none{% endif %}"
            style="height: 3px; background-color: #6366f1;"></div>
        </div>
      </div>
    </div>
  </div>

  {% if active_tab == 'profile' %}
  <div class="row g-4">
    <div class="col-lg-4 col-md-5 d-flex flex-column">
      <!-- Profile Image Card -->
      <div class="card shadow-sm border-0 rounded-4 mb-3">
        <div class="card-header bg-light py-3 rounded-top-4 border-0">
          <h5 class="fw-bold mb-0">Profile</h5>
        </div>
        <div class="card-body d-flex flex-column align-items-center justify-content-center">
          <div class="mb-3">
            <img src="{{ url_for('static', filename=get_safe_image_url(user.profile_image, 'profile')) }}"
              class="img-fluid rounded-circle shadow-sm"
              style="width: 150px; height: 150px; object-fit: cover; border: 2px solid #000;"
              alt="{{ user.username }}'s profile">
          </div>
        </div>
        <!-- ACCOUNT TYPE -->
        <div class="w-100 text-center mb-3">
          <div class="text-uppercase text-muted small fw-semibold mb-1" style="letter-spacing: 1px;">ACCOUNT TYPE
          </div>
          <span class="badge rounded-pill bg-light text-dark px-4 py-2 fs-6 mb-2" style="font-weight:600;">
            <i class="{{ get_role_icon(user.role) }} me-1"></i>{{ user.role|title }}
          </span>
        </div>
      </div>
      <!-- Account Information Card -->
      <div class="card shadow-sm border-0 rounded-4 h-100 align-self-stretch">
        <div class="card-header bg-light py-3 rounded-top-4 border-0">
          <h5 class="fw-bold mb-0">Connections</h5>
        </div>
        <div class="card-body d-flex flex-column align-items-center py-4 h-100">
          <!-- CONNECTION -->
          <div class="w-100 text-center mb-2">
            {% if premium_access %}
            {% if is_following_user %}
            <form method="POST" action="{{ url_for('departure_board.unfollow_user', followed_id=user.id) }}"
              class="d-block w-100">
              <button type="submit" class="btn btn-dark rounded-pill w-100 py-2 fs-7 mb-2" style="font-weight:600;">
                <i class="bi bi-person-dash me-2"></i> Unfollow
              </button>
            </form>
            {% else %}
            <form method="POST" action="{{ url_for('departure_board.follow_user', followed_id=user.id) }}"
              class="d-block w-100">
              <button type="submit" class="btn btn-dark rounded-pill w-100 py-2 fs-7 mb-2" style="font-weight:600;">
                <i class="bi bi-person-plus me-2"></i> Follow
              </button>
            </form>
            {% endif %}
            {% else %}
            <div class="alert alert-info py-2 px-3 mb-2 fs-7"
              style="background: #f4f6fb; color: #4e6bff; border: none;">
              <i class="bi bi-lock me-1"></i> Upgrade to Premium to connect with users.
            </div>
            <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
              class="btn btn-primary rounded-pill w-100 py-2 fs-7" style="font-weight:600;">
              <i class="bi bi-star me-2"></i> Upgrade to Premium
            </a>
            {% endif %}
          </div>
          <!-- COMMUNICATION -->
          <div class="w-100 text-center mt-auto">
            {% if can_message %}
            <a href="{{ url_for('message.create_conversation', recipient_id=user.id) }}"
              class="btn btn-primary rounded-pill w-100 py-2 fs-7 shadow-sm d-flex align-items-center justify-content-center gap-2"
              style="background: linear-gradient(90deg, #6a7cff 60%, #7b8fff 100%); font-weight:600;">
              <i class="bi bi-chat-dots me-2"></i> Send Message
            </a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-8 col-md-7">
      <div class="card shadow-sm border-0 rounded-4 h-100">
        <div class="card-header bg-light py-3 rounded-top-4 border-0 d-flex justify-content-between align-items-center">
          <h5 class="card-title fw-bold mb-0">
            Personal Information
          </h5>
        </div>

        <div class="card-body px-4 py-3">
          {% if user.first_name and user.show_first_name or user.last_name and user.show_last_name %}
          <div class="row g-3">
            {% if user.first_name and user.show_first_name %}
            <div class="col-md-6">
              <label class="form-label text-muted small text-uppercase fw-medium">First Name</label>
              <div class="profile-info fw-medium">{{ user.first_name }}</div>
            </div>
            {% endif %}

            {% if user.last_name and user.show_last_name %}
            <div class="col-md-6">
              <label class="form-label text-muted small text-uppercase fw-medium">Last Name</label>
              <div class="profile-info fw-medium">{{ user.last_name }}</div>
            </div>
            {% endif %}
          </div>
          {% endif %}

          {% if user.show_username %}
          <div class="mb-2 mt-2">
            <label class="form-label text-muted small text-uppercase fw-medium">Username</label>
            <div class="profile-info">
              <i class="bi bi-person-badge me-2 text-primary opacity-75"></i>
              <span class="fw-medium">{{ user.username }}</span>
            </div>
          </div>
          {% endif %}

          {% if user.email and user.show_email %}
          <div class="mb-2 mt-2">
            <label class="form-label text-muted small text-uppercase fw-medium">Email</label>
            <div class="profile-info">
              <i class="bi bi-envelope me-2 text-primary opacity-75"></i>
              <span class="fw-medium">{{ user.email }}</span>
            </div>
          </div>
          {% endif %}

          {% if user.location and user.show_location %}
          <div class="mb-2 mt-2">
            <label class="form-label text-muted small text-uppercase fw-medium">Location</label>
            <div class="profile-info">
              <i class="bi bi-geo-alt me-2 text-primary opacity-75"></i>
              <span class="fw-medium">{{ user.location }}</span>
            </div>
          </div>
          {% endif %}

          {% if (user.description or user.biography) and user.show_description %}
          <div class="mb-0 mt-2">
            <label class="form-label text-muted small text-uppercase fw-medium">About</label>
            <div class="profile-info description p-3 bg-light rounded-3 shadow-sm">
              <i class="bi bi-quote me-2 text-primary opacity-75"></i>
              {{ user.biography or user.description }}
              <i class="bi bi-quote ms-2 text-primary opacity-75"
                style="display: inline-block; transform: rotate(180deg);"></i>
            </div>
          </div>
          {% endif %}

          {% if user.interests and user.show_interests %}
          <div class="mb-0 mt-3">
            <label class="form-label text-muted small text-uppercase fw-medium">Interests</label>
            <div class="profile-info">
              <i class="bi bi-tags me-2 text-primary opacity-75"></i>
              <span class="fw-medium">{{ user.interests }}</span>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  {% elif active_tab == 'activities' %}
  {% if premium_access %}
  {% include 'discovery/user/activities.html' %}
  {% else %}
  <div class="mt-5 text-center">
    <div class="alert alert-info">
      <i class="bi bi-lock me-2"></i> Subscribe to Premium to view more details about this user's activities
    </div>
    <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
      class="btn btn-primary rounded-pill py-3 px-4">
      <i class="bi bi-star me-2"></i> Upgrade to Premium
    </a>
  </div>
  {% endif %}
  {% endif %}
</div>

<style>
  .form-control {
    border-color: #dee2e6;
    border-radius: 0.5rem;
  }

  .input-group .form-control {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .form-control:disabled,
  .form-control:read-only {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }

  .input-group-text {
    border-color: #dee2e6;
    border-color: #dee2e6;
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .form-label {
    font-weight: 500;
    margin-bottom: 0.1rem;
  }

  @media (max-width: 768px) {
    .card {
      border-radius: 0.5rem;
    }
  }

  .input-group-text i {
    color: var(--bs-primary);
    opacity: 0.75;
  }

  .no-border-tabs .nav-link {
    color: black !important;
    font-weight: 700;
    border: none !important;
    border-radius: 0 !important;
    margin-right: 30px !important;
  }

  .no-border-tabs .nav-link.active {
    color: black !important;
    font-weight: 700;
    border-bottom: 2px solid #0d6efd !important;
    background-color: transparent !important;
    margin-right: 30px !important;
  }

  .no-border-tabs .nav-link:hover {
    color: black !important;
    font-weight: 700;
    border-bottom: 2px solid #0d6efd !important;
    background-color: transparent !important;
    margin-right: 30px !important;
  }

  /* Tab content styles */
  .tab-content {
    padding: 20px 0;
  }

  .tab-pane {
    padding: 10px 0;
  }

  /* Card hover effects */
  .transition-hover {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .transition-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }

  .btn-primary.rounded-pill:focus,
  .btn-primary.rounded-pill:hover {
    box-shadow: 0 4px 16px rgba(78, 107, 255, 0.15) !important;
    background: linear-gradient(90deg, #4e6bff 80%, #6c8cff 100%);
    color: #fff !important;
  }
</style>
{% endblock %}