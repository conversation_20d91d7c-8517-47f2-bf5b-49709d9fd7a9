"""
Debug Routes

Routes for debugging and development tools, including function usage tracking.
"""

from flask import Blueprint, render_template, jsonify, request, session
from utils.security import login_required
import os

bp = Blueprint('debug', __name__, url_prefix='/debug')


@bp.route('/function-tracker')
def function_tracker():
    """Debug page for function usage tracking"""
    return render_template('debug/function_tracker.html')


@bp.route('/extract-functions')
def extract_functions():
    """Extract JavaScript functions from templates for analysis"""
    
    # This would run the function extraction script
    try:
        import subprocess
        import sys
        
        result = subprocess.run([
            sys.executable, 
            'tests/frontend/extract_js_functions.py'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            return jsonify({
                'success': True,
                'output': result.stdout,
                'message': 'Function extraction completed successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.stderr,
                'message': 'Function extraction failed'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Error running function extraction'
        }), 500


@bp.route('/run-tracking-tests')
def run_tracking_tests():
    """Run Playwright tests for function tracking"""
    
    try:
        import subprocess
        import sys
        
        result = subprocess.run([
            sys.executable, '-m', 'pytest',
            'tests/frontend/test_function_usage_tracking.py::test_generate_usage_report',
            '-v', '--tb=short'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            return jsonify({
                'success': True,
                'output': result.stdout,
                'message': 'Tracking tests completed successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': result.stderr,
                'output': result.stdout,
                'message': 'Tracking tests failed'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Error running tracking tests'
        }), 500


@bp.route('/reports')
def list_reports():
    """List available tracking reports"""
    
    reports_dir = 'test_reports'
    reports = []
    
    if os.path.exists(reports_dir):
        for filename in os.listdir(reports_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(reports_dir, filename)
                stat = os.stat(filepath)
                reports.append({
                    'filename': filename,
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'url': f'/debug/report/{filename}'
                })
    
    return jsonify({
        'reports': sorted(reports, key=lambda x: x['modified'], reverse=True)
    })


@bp.route('/report/<filename>')
def get_report(filename):
    """Get a specific tracking report"""
    
    reports_dir = 'test_reports'
    filepath = os.path.join(reports_dir, filename)
    
    if not os.path.exists(filepath) or not filename.endswith('.json'):
        return jsonify({'error': 'Report not found'}), 404
    
    try:
        import json
        with open(filepath, 'r') as f:
            report_data = json.load(f)
        return jsonify(report_data)
    except Exception as e:
        return jsonify({'error': f'Error reading report: {str(e)}'}), 500


@bp.route('/cleanup-recommendations')
def cleanup_recommendations():
    """Generate cleanup recommendations based on latest report"""
    
    try:
        import json
        import glob
        from datetime import datetime
        
        # Find latest analysis report
        pattern = 'test_reports/function_analysis_*.json'
        analysis_files = glob.glob(pattern)
        
        if not analysis_files:
            return jsonify({
                'error': 'No analysis reports found. Run tracking tests first.'
            }), 404
        
        latest_file = max(analysis_files, key=os.path.getmtime)
        
        with open(latest_file, 'r') as f:
            analysis = json.load(f)
        
        # Generate recommendations
        recommendations = {
            'timestamp': datetime.now().isoformat(),
            'source_report': os.path.basename(latest_file),
            'summary': {
                'total_functions': analysis.get('total_possible_functions', 0),
                'used_functions': len(analysis.get('functions_used', [])),
                'unused_functions': len(analysis.get('functions_unused', [])),
                'usage_percentage': analysis.get('usage_percentage', 0)
            },
            'cleanup_actions': {
                'safe_to_remove': analysis.get('functions_unused', []),
                'files_to_review': [
                    'templates/event/detail.html',
                    'templates/event/edit.html'
                ],
                'search_patterns': [
                    f'function {func}\\(' for func in analysis.get('functions_unused', [])[:10]
                ] + [
                    f'onclick="{func}\\(' for func in analysis.get('functions_unused', [])[:10]
                ]
            },
            'next_steps': [
                '1. Review unused functions in templates',
                '2. Search for each unused function in codebase',
                '3. Remove unused JavaScript code blocks',
                '4. Test functionality after removal',
                '5. Run tracking tests again to verify cleanup'
            ]
        }
        
        return jsonify(recommendations)
        
    except Exception as e:
        return jsonify({
            'error': f'Error generating recommendations: {str(e)}'
        }), 500


@bp.route('/help')
def help_page():
    """Help page for function tracking tools"""
    
    help_content = {
        'title': 'Function Usage Tracking Help',
        'sections': [
            {
                'title': 'Getting Started',
                'content': [
                    '1. Visit /debug/function-tracker to access the tracking interface',
                    '2. Start tracking and navigate to your event pages',
                    '3. Interact with buttons, forms, and modals',
                    '4. Stop tracking and download the report'
                ]
            },
            {
                'title': 'Automated Testing',
                'content': [
                    '1. Run: python tests/frontend/run_function_tracking.py',
                    '2. Ensure your Flask app is running first',
                    '3. Check test_reports/ folder for generated reports'
                ]
            },
            {
                'title': 'Manual Browser Testing',
                'content': [
                    '1. Add ?debug=true to any page URL',
                    '2. Use the floating tracker controls',
                    '3. Test specific functions manually'
                ]
            },
            {
                'title': 'API Endpoints',
                'content': [
                    'GET /debug/reports - List all tracking reports',
                    'GET /debug/report/<filename> - Get specific report',
                    'GET /debug/cleanup-recommendations - Get cleanup suggestions',
                    'GET /debug/extract-functions - Extract functions from templates',
                    'GET /debug/run-tracking-tests - Run automated tests'
                ]
            }
        ]
    }
    
    return jsonify(help_content)
