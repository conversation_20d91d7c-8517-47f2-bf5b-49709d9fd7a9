{% block content %}
<div class="create-event-modal" id="createEventModal">
  <form method="post" action="{{ url_for('event.create_event', journey_id=journey.id) }}" enctype="multipart/form-data"
    novalidate id="createEventForm" class="needs-validation modern-form">

    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid">

        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-info-circle section-icon"></i>
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-type"></i>
                  Event Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title"
                  value="{{ request.form.get('title', '') }}" required minlength="5" maxlength="50"
                  placeholder="Enter event title" />
                <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="3"
                  placeholder="Describe your event...">{{ request.form.get('description', '') }}</textarea>
                <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
              </div>
            </div>
          </div>

          <!-- Date & Time Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-clock section-icon"></i>
              <span class="section-title">Date & Time</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="startDatetime" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date & Time *
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="startDatetime"
                  name="start_datetime" value="{{ request.form.get('start_datetime', '') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              <div class="form-group">
                <label for="endDatetime" class="modern-label">
                  <i class="bi bi-calendar-check"></i>
                  End Date & Time
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="endDatetime" name="end_datetime"
                  value="{{ request.form.get('end_datetime', '') }}" />
                <div class="invalid-feedback">End date cannot be before start date.</div>
              </div>
            </div>
          </div>

          <!-- Images Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-images section-icon"></i>
              <span class="section-title">Event Images</span>
            </div>

            <div class="form-group">
              <label for="images" class="modern-label">
                <i class="bi bi-camera"></i>
                {% if premium_access %}Images (optional, max {{ file_config.maxFileSizeMB }}MB each, limit 10){% else
                %}Image (optional, max {{ file_config.maxFileSizeMB }}MB){% endif %}
              </label>
              <input type="file" class="modern-input" id="images" name="images" {% if premium_access %}multiple{% endif
                %} accept="{{ file_config.allowedExtensionsHtml }}"
                data-premium="{% if premium_access %}true{% else %}false{% endif %}">
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                {% if premium_access %}
                Upload up to 10 images. Maximum {{ file_config.maxFileSizeMB }}MB each. Allowed formats: {{
                file_config.allowedFormatsText }}.
                {% else %}
                Upload one image. Maximum {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{
                file_config.allowedFormatsText }}.
                {% endif %}
              </div>
              <div class="invalid-feedback" id="imagesFeedback">
                {% if premium_access %}
                Images cannot exceed {{ file_config.maxFileSizeMB }}MB each, and you can only upload up to 10 images.
                {% else %}
                Image cannot exceed {{ file_config.maxFileSizeMB }}MB.
                {% endif %}
              </div>

              <!-- Image Preview Container -->
              <div id="imagePreviewContainer" class="image-preview-grid mt-3" style="display: none;">
                <div class="preview-header">
                  <span class="preview-title">
                    <i class="bi bi-eye"></i>
                    Selected Images
                  </span>
                  <button type="button" class="btn btn-sm btn-outline-secondary"
                    onclick="window.imagePreviewInstance?.clearAll()">
                    <i class="bi bi-x"></i>
                    Clear All
                  </button>
                </div>
                <div id="previewGrid" class="row g-2">
                  <!-- Preview images will be inserted here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Location Search Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-search section-icon"></i>
              <span class="section-title">Search Location</span>
            </div>

            <div class="form-group">
              <label for="locationSearch" class="modern-label">
                    <i class="bi bi-search"></i>
                Search for Location *
              </label>
              <div class="location-search-container">
                <input type="text" class="modern-input" id="locationSearch"
                  placeholder="Type location name or address..." />
                <button type="button" class="btn btn-outline-primary btn-sm ms-2" id="searchLocationBtn">
                  <i class="bi bi-search"></i> Search
                  </button>
              </div>
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                Search for existing locations or new addresses
              </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="location-results" style="display: none;">
              <div class="results-header">
                <h6 class="mb-0">Search Results</h6>
              </div>
              <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
              </div>
            </div>
          </div>

          <!-- Selected Location Section -->
          <div class="form-section compact" id="selectedLocationSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-geo-alt section-icon"></i>
              <span class="section-title">Selected Location</span>
            </div>

            <div class="selected-location-info">
              <div class="form-group">
                <label for="location" class="modern-label">
                  <i class="bi bi-tag"></i>
                  Location Name *
                </label>
                <input type="text" class="modern-input" id="location" name="location" required readonly />
              <div class="invalid-feedback">Location is required.</div>
              </div>

              <div class="location-actions mt-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" id="changeLocationBtn">
                  <i class="bi bi-arrow-left"></i> Change Location
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary" id="editMapLocationBtn"
                  style="display: none;">
                  <i class="bi bi-geo-alt"></i> Change Map Location
                </button>
              </div>
            </div>
          </div>

          <!-- Map Section -->
          <div class="form-section map-section" id="mapSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-map section-icon"></i>
              <span class="section-title">Map Preview</span>
            </div>

            <div class="location-content">
              <!-- Interactive Map -->
              <div class="map-container desktop-optimized">
                <div id="map" class="modern-map"></div>
              </div>

              <!-- Map Search (for new locations) -->
              <div id="mapSearchGroup" class="form-group mt-3" style="display: none;">
                <label for="mapSearch" class="modern-label">
                  <i class="bi bi-search"></i>
                  Search Address on Map
                </label>
                <div class="map-search-container">
                  <input type="text" class="modern-input" id="mapSearch" placeholder="Search for address..." />
                  <div id="mapSuggestions" class="modern-suggestions"></div>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Search for address and click on map to set location
                </div>

                <!-- Coordinates Status (Hidden from users) -->
                <div id="coordinatesStatus" class="mt-2" style="display: none !important;">
                  <div class="alert alert-success py-2 px-3 mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    <span id="coordinatesText">Location coordinates set</span>
                  </div>
                </div>

                <!-- New Location Name Input -->
                <div id="newLocationNameGroup" class="mt-3" style="display: none;">
                  <label for="newLocationName" class="modern-label">
                    <i class="bi bi-tag"></i>
                    Name for New Location *
                  </label>
                  <input type="text" class="modern-input" id="newLocationName"
                    placeholder="Enter unique name for this location" />
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    This name will be validated and created when you submit the event
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Hidden coordinate fields -->
      <input type="hidden" id="latitude" name="latitude">
      <input type="hidden" id="longitude" name="longitude">
    </div>
  </form>

  <style>
    /* Modern Create Event Modal Styles */
    .create-event-modal {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0;
    }

    .modern-form {
      background: #ffffff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    /* Form Content */
    .form-content {
      padding: 24px;
      background: #fafbfc;
    }

    /* Desktop Grid Layout */
    .desktop-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      align-items: start;
    }

    .left-column,
    .right-column {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* Form Sections */
    .form-section {
      background: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 0;
      border: 1px solid #e1e8ed;
      transition: all 0.3s ease;
      height: fit-content;
    }

    .form-section.compact {
      padding: 18px;
    }

    .form-section:hover {
      border-color: #667eea;
      box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
    }

    .form-section.map-section {
      height: fit-content;
    }

    /* Section Headers */
    .section-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f1f3f4;
    }

    .section-icon {
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;
      flex: 1;
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      gap: 16px;
    }

    /* Form Groups */
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    /* Modern Labels */
    .modern-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .modern-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Modern Inputs */
    .modern-input,
    .modern-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      color: #2d3748;
      background: #ffffff;
      transition: all 0.3s ease;
      outline: none;
    }

    .modern-input:focus,
    .modern-textarea:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    .modern-input:disabled,
    .modern-input:read-only,
    .modern-textarea:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    .modern-input::placeholder,
    .modern-textarea::placeholder {
      color: #a0aec0;
    }

    /* Location Search Container */
    .location-search-container {
      display: flex;
      align-items: center;
    }

    .location-search-container .modern-input {
      flex: 1;
      margin-bottom: 0;
    }

    /* Location Results */
    .location-results {
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      margin-top: 12px;
      max-height: 300px;
      overflow-y: auto;
    }

    .results-header {
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 600;
      color: #495057;
    }

    .results-list {
      padding: 0;
    }

    .result-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f1f3f4;
      cursor: pointer;
      transition: all 0.2s ease;
      justify-content: space-between;
    }

    .result-item:hover {
      background: #f8f9fa;
    }

    .result-item:last-child {
      border-bottom: none;
    }

    .result-item.selected {
      background: #e3f2fd;
      border-color: #2196f3;
    }

    .result-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .result-icon {
      width: 32px;
      height: 32px;
      background: #e3f2fd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #2196f3;
    }

    .result-details h6 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .result-details small {
      color: #666;
      font-size: 12px;
    }

    .result-type {
      background: #e3f2fd;
      color: #2196f3;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    /* Selected Location Info */
    .selected-location-info {
      background: #e8f5e8;
      border: 1px solid #4caf50;
      border-radius: 8px;
      padding: 16px;
    }

    .location-actions {
      display: flex;
      gap: 8px;
    }

    /* Map Styles */
    .map-container {
      margin: 12px 0 0 0;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #e2e8f0;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .modern-map {
      height: 300px;
      width: 100%;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .map-container:hover {
      border-color: #667eea;
    }

    /* Map Search Container */
    .map-search-container {
      position: relative;
    }

    /* Suggestions */
    .modern-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 2px solid #e2e8f0;
      border-top: none;
      border-radius: 0 0 8px 8px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      display: none;
    }

    .modern-suggestions.show {
      display: block;
    }

    .suggestion-item {
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid #f1f3f4;
      background: white;
      color: #2d3748;
    }

    .suggestion-item:hover {
      background: #667eea;
      color: white;
    }

    .suggestion-item:last-child {
      border-bottom: none;
    }

    /* Help Text */
    .input-help {
      font-size: 12px;
      color: #718096;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .input-help i {
      color: #667eea;
    }

    /* Validation feedback states */
    .has-error .modern-input,
    .has-error .modern-textarea {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .has-success .modern-input,
    .has-success .modern-textarea {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .has-error .input-help {
      color: #e53e3e;
    }

    .has-success .input-help {
      color: #38a169;
    }

    .has-error .input-help i {
      color: #e53e3e;
    }

    .has-success .input-help i {
      color: #38a169;
    }

    /* Validation Styles */
    .modern-input.is-invalid,
    .modern-textarea.is-invalid {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .modern-input.is-valid,
    .modern-textarea.is-valid {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .invalid-feedback {
      color: #e53e3e;
      font-size: 12px;
      margin-top: 4px;
      display: none;
      align-items: center;
      gap: 6px;
    }

    .modern-input.is-invalid+.invalid-feedback,
    .modern-textarea.is-invalid+.invalid-feedback {
      display: flex;
    }

    .invalid-feedback::before {
      content: "⚠";
      font-size: 14px;
    }

    /* Success States */
    .success-state {
      background: #d4edda;
      border-color: #28a745;
      color: #155724;
    }

    /* Loading States */
    .loading {
      position: relative;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Search button loading state */
    .btn.loading {
      opacity: 0.8;
      cursor: not-allowed;
    }

    .btn.loading i {
      animation: spin 1s linear infinite;
    }

    /* Smooth transitions for section visibility */
    .location-results,
      .form-section {
      transition: all 0.3s ease;
    }

    .location-results.hide {
      opacity: 0;
      transform: translateY(-10px);
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* Responsive Design */
    @media (max-width: 992px) {
      .desktop-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .create-event-modal {
        max-width: 800px;
      }

      .modern-map {
        height: 250px;
      }
    }

    @media (max-width: 768px) {
      .form-content {
        padding: 20px 16px;
      }

      .form-section {
        padding: 16px;
      }

      .location-search-container {
        flex-direction: column;
        gap: 8px;
      }

      .location-search-container .btn {
        align-self: stretch;
      }

      .location-actions {
        flex-direction: column;
      }
    }
  </style>

  <script>
    // Only define LocationSelector if it doesn't already exist
    if (typeof window.LocationSelector === 'undefined') {
      /**
       * Simple Location Selection System
       * 
       * This implementation uses a clear state machine approach:
       * 1. SEARCHING - User is searching for locations
       * 2. SELECTED_EXISTING - User selected an existing location from database
       * 3. CREATING_NEW - User is creating a new location with map coordinates
       */

      class LocationSelector {
        constructor() {
          this.state = 'INITIAL';
          this.selectedLocation = null;
          this.map = null;
          this.marker = null;
          this.tempCoordinates = null;
          this.lastSuggestedName = '';

          try {
            this.initializeElements();
            this.bindEvents();
            console.log('✅ LocationSelector initialized successfully');
          } catch (error) {
            console.error('❌ LocationSelector initialization failed:', error);
            throw error;
          }
        }

        initializeElements() {
          // Get all required elements
          this.elements = {
            locationSearch: document.getElementById('locationSearch'),
            searchBtn: document.getElementById('searchLocationBtn'),
            searchResults: document.getElementById('searchResults'),
            resultsList: document.getElementById('resultsList'),
            selectedSection: document.getElementById('selectedLocationSection'),
            locationInput: document.getElementById('location'),
            changeLocationBtn: document.getElementById('changeLocationBtn'),
            editMapLocationBtn: document.getElementById('editMapLocationBtn'),
            mapSection: document.getElementById('mapSection'),
            mapSearchGroup: document.getElementById('mapSearchGroup'),
            mapSearch: document.getElementById('mapSearch'),
            mapSuggestions: document.getElementById('mapSuggestions'),
            coordinatesStatus: document.getElementById('coordinatesStatus'),
            coordinatesText: document.getElementById('coordinatesText'),
            newLocationNameGroup: document.getElementById('newLocationNameGroup'),
            newLocationName: document.getElementById('newLocationName'),
            latitudeInput: document.getElementById('latitude'),
            longitudeInput: document.getElementById('longitude')
          };

          // Check for missing elements
          const missingElements = [];
          for (const [key, element] of Object.entries(this.elements)) {
            if (!element) {
              missingElements.push(key);
            }
          }

          if (missingElements.length > 0) {
            console.error('❌ Missing required elements:', missingElements);
            throw new Error(`Missing required elements: ${missingElements.join(', ')}`);
          }

          console.log('✅ All required elements found');
        }

        bindEvents() {
          // Search functionality
          this.elements.searchBtn.addEventListener('click', () => this.performSearch());
          this.elements.locationSearch.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              e.stopPropagation();
              this.performSearch();
            }
          });

          // Location management
          this.elements.changeLocationBtn.addEventListener('click', () => this.changeLocation());
          this.elements.editMapLocationBtn.addEventListener('click', () => this.editMapLocation());

          // New location creation
          this.elements.newLocationName.addEventListener('input', () => this.debounceNameValidation());

          // Map search
          this.elements.mapSearch.addEventListener('input', () => this.debounceMapSearch());

          // Click outside to hide suggestions
          document.addEventListener('click', (e) => this.handleClickOutside(e));
        }

        async performSearch() {
          const query = this.elements.locationSearch.value.trim();
          if (!query) {
            this.showError('Please enter a location name or address to search.');
        return;
      }

          // Show loading state
          this.showSearchLoading(true);

          try {
            // Search both database and map
            const [dbResults, mapResults] = await Promise.all([
              this.searchDatabase(query),
              this.searchMap(query)
            ]);

            this.displaySearchResults(dbResults, mapResults);
            this.setState('SEARCHING');
          } catch (error) {
            console.error('Search error:', error);
            this.showError('Search failed. Please check your connection and try again.');
          } finally {
            this.showSearchLoading(false);
          }
        }

        showSearchLoading(isLoading) {
          const searchBtn = this.elements.searchBtn;
          const searchInput = this.elements.locationSearch;

          if (isLoading) {
            // Show loading state
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Searching...';
            searchBtn.classList.add('loading');
            searchInput.disabled = true;

            // Hide any previous results
            this.elements.searchResults.style.display = 'none';
          } else {
            // Reset to normal state
            searchBtn.disabled = false;
            searchBtn.innerHTML = '<i class="bi bi-search"></i> Search';
            searchBtn.classList.remove('loading');
            searchInput.disabled = false;
          }
        }

        async searchDatabase(query) {
          const response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
          if (!response.ok) throw new Error('Database search failed');
          return await response.json();
        }

        async searchMap(query) {
          const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1&addressdetails=1`);
          if (!response.ok) throw new Error('Map search failed');
          return await response.json();
        }

        displaySearchResults(dbResults, mapResults) {
          this.elements.resultsList.innerHTML = '';

          // Add database results
          dbResults.forEach(location => {
            const item = this.createResultItem(
              location.name,
              'Database Location',
              'bi-database',
              'existing',
              () => this.selectExistingLocation(location.name)
            );
            this.elements.resultsList.appendChild(item);
          });

          // Add map results (limited to most relevant result for better UX)
          mapResults.forEach((place, index) => {
            const cleanName = this.extractLocationName(place.display_name);
            const item = this.createResultItem(
              cleanName,
              index === 0 ? 'New Location (Most Relevant)' : 'New Location',
              'bi-geo-alt',
              'new',
              () => this.selectNewLocation(place, cleanName)
            );
            this.elements.resultsList.appendChild(item);
          });

          this.elements.searchResults.style.display = 'block';
        }

        createResultItem(name, type, icon, category, onClick) {
          const item = document.createElement('div');
          item.className = 'result-item';
          item.innerHTML = `
            <div class="result-info">
              <div class="result-icon">
                <i class="bi ${icon}"></i>
              </div>
              <div class="result-details">
                <h6>${name}</h6>
                <small>${type}</small>
              </div>
            </div>
            <span class="result-type">${category}</span>
          `;
          item.addEventListener('click', onClick);
          return item;
        }

        async selectExistingLocation(locationName) {
          try {
            this.showLoading(this.elements.selectedSection);

            // Get coordinates for existing location
            const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
        const data = await response.json();

            this.selectedLocation = {
              name: locationName,
              type: 'existing',
              lat: data.lat,
              lng: data.lng
            };

            this.updateLocationDisplay();
            this.initializeMap();
            this.hideSearchSection();
            this.setState('SELECTED_EXISTING');
      } catch (error) {
            console.error('Error selecting existing location:', error);
            this.showError('Failed to load location details.');
          } finally {
            this.hideLoading(this.elements.selectedSection);
          }
        }

        selectNewLocation(place, suggestedName) {
          this.tempCoordinates = {
            lat: parseFloat(place.lat),
            lng: parseFloat(place.lon),
            mapDisplayName: place.display_name
          };

          // Show map for new location creation
          this.elements.newLocationName.value = suggestedName;
          this.initializeMapForNewLocation();
          this.hideSearchSection();
          this.setState('CREATING_NEW');

          // Enhanced UX: Auto-focus on map and prepopulate map search field
          setTimeout(() => {
            // Prepopulate the map search field with the selected location
            if (this.elements.mapSearch) {
              this.elements.mapSearch.value = place.display_name;
              console.log(`✨ Auto-populated map search field: ${place.display_name}`);
            }

            // Auto-focus on the map section by scrolling to it
            if (this.elements.mapSection) {
              this.elements.mapSection.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              });
              console.log('✨ Auto-focused on map section for better UX');
            }

            // Validate the form since we now have coordinates
            this.validateNewLocationForm();
          }, 200); // Small delay to ensure map is initialized
        }

        updateLocationDisplay() {
          this.elements.locationInput.value = this.selectedLocation.name;
          this.elements.latitudeInput.value = this.selectedLocation.lat;
          this.elements.longitudeInput.value = this.selectedLocation.lng;

          this.elements.selectedSection.style.display = 'block';
          this.elements.searchResults.style.display = 'none';

          // Show edit map button for existing locations
          if (this.selectedLocation.type === 'existing') {
            this.elements.editMapLocationBtn.style.display = 'inline-block';
          }
        }

        initializeMap() {
          if (!this.selectedLocation?.lat || !this.selectedLocation?.lng) return;

          // Show only the map section for existing locations (no search interface)
          this.elements.mapSection.style.display = 'block';

          // Hide map search and new location creation fields for existing locations
          this.elements.mapSearchGroup.style.display = 'none';
          this.elements.newLocationNameGroup.style.display = 'none';
          this.elements.coordinatesStatus.style.display = 'none';

          // Remove existing map
          if (this.map) {
            this.map.remove();
          }

          // Create new map for preview only (non-interactive)
          this.map = L.map('map').setView([this.selectedLocation.lat, this.selectedLocation.lng], 13);

          L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          }).addTo(this.map);

          // Add non-draggable marker for existing locations (preview only)
          this.marker = L.marker([this.selectedLocation.lat, this.selectedLocation.lng])
            .addTo(this.map)
            .bindPopup(this.selectedLocation.name);

          // Invalidate size after modal is shown
          setTimeout(() => this.map.invalidateSize(), 100);
        }

        initializeMapForNewLocation() {
          this.elements.mapSection.style.display = 'block';
          this.elements.mapSearchGroup.style.display = 'block';
          this.elements.newLocationNameGroup.style.display = 'block';
          this.elements.selectedSection.style.display = 'none';
          this.elements.searchResults.style.display = 'none';

          // Update help text to be contextually appropriate
          const helpText = this.elements.mapSearchGroup.querySelector('.input-help');
          helpText.innerHTML = '<i class="bi bi-info-circle"></i> Search for address and click on map to set location';
          helpText.className = 'input-help';

          // Remove existing map
          if (this.map) {
            this.map.remove();
          }

          // Create new map
          const lat = this.tempCoordinates.lat;
          const lng = this.tempCoordinates.lng;

          this.map = L.map('map').setView([lat, lng], 13);

          L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          }).addTo(this.map);

          // Add draggable marker
          this.marker = L.marker([lat, lng], { draggable: true })
            .addTo(this.map)
            .bindPopup('New Location');

          // Update coordinates when marker is moved
          this.marker.on('dragend', (e) => {
            const position = e.target.getLatLng();
            this.tempCoordinates.lat = position.lat;
            this.tempCoordinates.lng = position.lng;

            // Coordinates are set but hidden from users (for internal use only)
            this.elements.coordinatesText.textContent = 'Location coordinates set';
            // Keep coordinates status hidden from users

            // Perform reverse geocoding to update search field
            this.reverseGeocode(position.lat, position.lng);

            this.validateNewLocationForm();
          });

          // Update marker when map is clicked
          this.map.on('click', (e) => {
            this.marker.setLatLng(e.latlng);
            this.tempCoordinates.lat = e.latlng.lat;
            this.tempCoordinates.lng = e.latlng.lng;

            // Coordinates are set but hidden from users (for internal use only)
            this.elements.coordinatesText.textContent = 'Location coordinates set';
            // Keep coordinates status hidden from users

            // Perform reverse geocoding to update search field
            this.reverseGeocode(e.latlng.lat, e.latlng.lng);

            this.validateNewLocationForm();
          });

          setTimeout(() => this.map.invalidateSize(), 100);
        }

        debounceMapSearch() {
          clearTimeout(this.mapSearchTimeout);
          this.mapSearchTimeout = setTimeout(() => this.performMapSearch(), 300);
        }

        async performMapSearch() {
          const query = this.elements.mapSearch.value.trim();
      if (query.length < 3) {
            this.elements.mapSuggestions.style.display = 'none';
        return;
      }

      try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5`);
            const results = await response.json();

            this.displayMapSuggestions(results);
          } catch (error) {
            console.error('Map search error:', error);
          }
        }

        displayMapSuggestions(results) {
          this.elements.mapSuggestions.innerHTML = '';

          if (results.length === 0) {
            this.elements.mapSuggestions.style.display = 'none';
          return;
        }

          results.forEach(place => {
          const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = place.display_name;
            item.addEventListener('click', () => this.selectMapSuggestion(place));
            this.elements.mapSuggestions.appendChild(item);
          });

          // Show the suggestions dropdown
          this.elements.mapSuggestions.style.display = 'block';
        }

        selectMapSuggestion(place) {
            const lat = parseFloat(place.lat);
            const lng = parseFloat(place.lon);

          // Update map view and marker
          this.map.setView([lat, lng], 13);
          this.marker.setLatLng([lat, lng]);

          // Update coordinates
          this.tempCoordinates.lat = lat;
          this.tempCoordinates.lng = lng;

          // Update search field and hide suggestions
          this.elements.mapSearch.value = place.display_name;
          this.elements.mapSuggestions.style.display = 'none';

          // Coordinates are set but hidden from users (for internal use only)
          this.elements.coordinatesText.textContent = 'Location coordinates set';
          // Keep coordinates status hidden from users

          // Enable save button since coordinates are now selected
          this.validateNewLocationForm();
        }

        validateNewLocationForm() {
          const name = this.elements.newLocationName.value.trim();
          const hasCoordinates = this.tempCoordinates && this.tempCoordinates.lat && this.tempCoordinates.lng;

          // Update form fields when both name and coordinates are available
          if (name && hasCoordinates) {
            // Check name uniqueness before populating form fields
            this.checkLocationNameUniqueness(name);
          } else if (!hasCoordinates) {
            // Clear form fields if coordinates missing
            this.elements.locationInput.value = '';
            this.elements.latitudeInput.value = '';
            this.elements.longitudeInput.value = '';

            const helpText = this.elements.newLocationNameGroup.querySelector('.input-help');
            helpText.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i> Please search for an address and select coordinates from the map';
            helpText.className = 'input-help text-warning';
          } else {
            // Clear form fields if name missing
            this.elements.locationInput.value = '';
            this.elements.latitudeInput.value = '';
            this.elements.longitudeInput.value = '';

            const helpText = this.elements.newLocationNameGroup.querySelector('.input-help');
            helpText.innerHTML = '<i class="bi bi-info-circle"></i> This name will be validated and created when you submit the event';
            helpText.className = 'input-help';
          }
        }

        async checkLocationNameUniqueness(name) {
          if (!name || name.length < 2) return;

          try {
            // Add loading indicator
            const helpText = this.elements.newLocationNameGroup.querySelector('.input-help');
            helpText.innerHTML = '<i class="bi bi-hourglass-split text-info"></i> Checking name availability...';
            helpText.className = 'input-help text-info';

            // Check if location name already exists
            const response = await fetch(`/location/search?query=${encodeURIComponent(name)}`);
            if (!response.ok) throw new Error('Failed to check location names');

            const existingLocations = await response.json();
            const nameExists = existingLocations.some(loc => loc.name.toLowerCase() === name.toLowerCase());

            if (nameExists) {
              // Name already exists
              this.elements.locationInput.value = '';
              this.elements.latitudeInput.value = '';
              this.elements.longitudeInput.value = '';

              helpText.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i> This location name already exists. Please choose a different name.';
              helpText.className = 'input-help text-danger';

              // Add visual feedback to input
              this.elements.newLocationName.classList.add('is-invalid');
        } else {
              // Name is available - populate form fields
              this.elements.locationInput.value = name;
              this.elements.latitudeInput.value = this.tempCoordinates.lat;
              this.elements.longitudeInput.value = this.tempCoordinates.lng;

              helpText.innerHTML = '<i class="bi bi-check-circle text-success"></i> Location name is available! Ready to create event.';
              helpText.className = 'input-help text-success';

              // Remove invalid styling
              this.elements.newLocationName.classList.remove('is-invalid');
              this.elements.newLocationName.classList.add('is-valid');
        }
      } catch (error) {
            console.error('Error checking location name:', error);
            const helpText = this.elements.newLocationNameGroup.querySelector('.input-help');
            helpText.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i> Could not verify name availability. Will check when creating event.';
            helpText.className = 'input-help text-warning';
          }
        }

        extractLocationName(displayName) {
          const parts = displayName.split(',');
          let cleanName = parts[0].trim();

          // If first part is just a number, combine with second part
          if (/^\d+$/.test(cleanName) && parts.length > 1) {
            cleanName = `${cleanName} ${parts[1].trim()}`;
          }

          return cleanName;
        }

        setState(newState) {
          console.log(`LocationSelector: ${this.state} -> ${newState}`);
          this.state = newState;
        }

        showLoading(element) {
          element.classList.add('loading');
        }

        hideLoading(element) {
          element.classList.remove('loading');
        }

        showError(message) {
          // You can implement a toast notification system here
          alert(message);
        }

        showSuccess(message) {
          // You can implement a toast notification system here
          alert(message);
        }

        handleClickOutside(e) {
          // Hide map suggestions if clicking outside
          if (!this.elements.mapSearchGroup.contains(e.target)) {
            this.elements.mapSuggestions.style.display = 'none';
          }
        }

        hideSearchSection() {
          // Hide the search section and results
          this.elements.searchResults.style.display = 'none';

          // Clear search input for next use
          this.elements.locationSearch.value = '';

          // Reset search button state
          this.showSearchLoading(false);
        }

        showSearchSection() {
          // Show search section and focus on input
          this.elements.locationSearch.focus();

          // Clear any existing search results
          this.elements.searchResults.style.display = 'none';
          this.elements.resultsList.innerHTML = '';

          // Ensure search button is ready
          this.showSearchLoading(false);
        }

        hideNewLocationCreation() {
          this.elements.mapSearchGroup.style.display = 'none';
          this.elements.newLocationNameGroup.style.display = 'none';
          this.elements.mapSection.style.display = 'none';

          if (this.map) {
            this.map.remove();
            this.map = null;
          }
        }

        changeLocation() {
          // Reset all location-related state
          this.selectedLocation = null;
          this.tempCoordinates = null;

          // Hide all location display sections
          this.elements.selectedSection.style.display = 'none';
          this.elements.mapSection.style.display = 'none';
          this.elements.mapSearchGroup.style.display = 'none';
          this.elements.coordinatesStatus.style.display = 'none';
          this.elements.newLocationNameGroup.style.display = 'none';

          // Clear form inputs
          this.elements.locationInput.value = '';
          this.elements.latitudeInput.value = '';
          this.elements.longitudeInput.value = '';

          // Reset map search placeholder
          this.elements.mapSearch.placeholder = 'Search for address...';
          this.elements.mapSearch.value = '';

          // Clean up map if it exists
          if (this.map) {
            this.map.remove();
            this.map = null;
          }

          // Show search section and focus on input
          this.showSearchSection();

          // Return to initial state
          this.setState('INITIAL');
        }

        editMapLocation() {
          if (this.selectedLocation?.type === 'existing') {
            // Create a new location based on the existing one, but user will provide new name and coordinates
            // This is the same as selecting a "New Location" from search results

            // Initialize temp coordinates with current location as starting point
            this.tempCoordinates = {
              lat: this.selectedLocation.lat,
              lng: this.selectedLocation.lng,
              mapDisplayName: `New location based on ${this.selectedLocation.name}`
            };

            // Suggest a name based on the current location
            const suggestedName = `${this.selectedLocation.name} (Custom)`;

            // Show new location creation interface (same as selecting new location from search)
            this.elements.newLocationName.value = suggestedName;
            this.initializeMapForNewLocation();
            this.hideSearchSection();
            this.setState('CREATING_NEW');

            // Enhanced UX: Auto-focus on map search field and prepopulate with current location
            setTimeout(() => {
              // Prepopulate the map search field with the current location name
              if (this.elements.mapSearch) {
                this.elements.mapSearch.value = this.selectedLocation.name;
                console.log(`✨ Auto-populated map search field with current location: ${this.selectedLocation.name}`);

                // Auto-focus on the map search field for immediate editing
                this.elements.mapSearch.focus();
                this.elements.mapSearch.select(); // Select all text for easy replacement
                console.log('✨ Auto-focused on map search field for better UX');
              }

              // Auto-scroll to the map search section
              if (this.elements.mapSearchGroup) {
                this.elements.mapSearchGroup.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center'
                });
                console.log('✨ Auto-scrolled to map search section');
              }

              // Validate the form since we now have coordinates
              this.validateNewLocationForm();
            }, 300); // Small delay to ensure map and UI are initialized
          }
        }

        debounceNameValidation() {
          clearTimeout(this.nameValidationTimeout);
          this.nameValidationTimeout = setTimeout(() => this.validateNewLocationForm(), 500);
        }

        async reverseGeocode(lat, lng) {
          try {
            // Show loading indicator in search field
            const originalPlaceholder = this.elements.mapSearch.placeholder;
            this.elements.mapSearch.placeholder = 'Loading address...';
            this.elements.mapSearch.disabled = true;

            // Call Nominatim reverse geocoding API
            const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`);

            if (!response.ok) {
              throw new Error('Reverse geocoding failed');
            }

            const data = await response.json();

            if (data && data.display_name) {
              // Update the search field with the new address
              this.elements.mapSearch.value = data.display_name;

              // Extract a clean name for the location
              const cleanName = this.extractLocationName(data.display_name);

              // Update the new location name field if it's empty or contains the old suggested name
              const currentName = this.elements.newLocationName.value.trim();
              if (!currentName || currentName.includes('(Custom)') || currentName === this.lastSuggestedName) {
                this.elements.newLocationName.value = cleanName;
                this.lastSuggestedName = cleanName;

                // Trigger validation for the new name
                this.validateNewLocationForm();
              }
            }
          } catch (error) {
            console.error('Reverse geocoding error:', error);
            this.elements.mapSearch.value = `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`;
          } finally {
            // Reset search field state
            this.elements.mapSearch.placeholder = 'Search for address...';
            this.elements.mapSearch.disabled = false;
          }
        }

        cleanup() {
          // Clean up map instance
          if (this.map) {
            this.map.remove();
            this.map = null;
          }

          // Clear timeouts
          if (this.mapSearchTimeout) {
            clearTimeout(this.mapSearchTimeout);
          }
          if (this.nameValidationTimeout) {
            clearTimeout(this.nameValidationTimeout);
          }

          // Reset state
          this.selectedLocation = null;
          this.tempCoordinates = null;
          this.marker = null;
          this.lastSuggestedName = '';

          console.log('✅ LocationSelector cleaned up');
        }
      }

      // Make LocationSelector available globally
      window.LocationSelector = LocationSelector;
    }

    // Initialize the location selector when the modal is loaded
    let locationSelector;

    function initializeLocationSelector() {
      // Clean up existing instance if it exists
      if (window.locationSelector && typeof window.locationSelector.cleanup === 'function') {
        window.locationSelector.cleanup();
      }

      locationSelector = new window.LocationSelector();
      // Make it globally available for modal integration
      window.locationSelector = locationSelector;
    }

    // Make the initialization function globally available too
    window.initializeLocationSelector = initializeLocationSelector;

    // Call initialization when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeLocationSelector);
    } else {
      initializeLocationSelector();
    }
  </script>
</div>
{% endblock %}