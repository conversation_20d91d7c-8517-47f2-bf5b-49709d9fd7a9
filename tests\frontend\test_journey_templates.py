from playwright.sync_api import Page, expect


def login_traveller(page: Page) -> None:
    page.goto("http://127.0.0.1:5000/")
    page.locator("#navbarLogin").get_by_role("link", name="Login").click()
    page.get_by_role("textbox", name="Username").click()
    page.get_by_role("textbox", name="Username").fill("traveller1")
    page.get_by_role("textbox", name="Password").click()
    page.get_by_role("textbox", name="Password").fill("Password123!")
    page.get_by_role("button", name="Login").click()

    expect(page.locator("#my_journeys_navbar")).to_be_visible(timeout=10000)


def test_journey_page_loads(page: Page) -> None:
        login_traveller(page)

        page.locator("#my_journeys_navbar").click()
        journey_card = page.locator(".journey-card").first

        # click on journey and get journey id from url
        journey = journey_card.locator("a")
        href = journey.get_attribute("href")
        journey_id = href.split("/")[-1]
        journey.click()

        expect(page).to_have_url(f"http://127.0.0.1:5000/journey/private/{journey_id}")

        # page contains journey info
        assert page.locator("#journeyTitle").is_visible()
        assert page.locator("#journeyStartDate").is_visible()
        assert page.locator("#journeyDescription").is_visible()

def locate_journey_page(page: Page) -> None:
        page.locator("#myJourneysNavbar").click()
        journey_card = page.locator(".journey-card").first
        journey_card.locator("a").click()


def test_journey_page_shows_events(page: Page) -> None:
        login_traveller(page)
        locate_journey_page(page)

        event_card = page.locator(".event-card").first

        # page contains events
        if event_card.is_visible():
            assert int(page.locator("#eventCount").text_content()) > 0
        else:
            assert page.locator("#noEventsMessage").is_visible()


def test_add_event_modal_appears(page: Page) -> None:
        login_traveller(page)
        locate_journey_page(page)
        page.locator('.create-event-button').click()
        page.locator("#createEventModal").wait_for(state="visible")
        assert page.locator("#createEventModal").is_visible()

        assert page.locator("#title").is_visible()
        assert page.locator("#description").is_visible()
        assert page.locator("#location").is_visible()
        assert page.locator("#startDatetime").is_visible()
        assert page.locator("#endDatetime").is_visible()
        assert page.locator("#image").is_visible()


        page.fill("#title", "test title")
        page.fill("#description", "test description")
        page.fill("#location", "test location")
        page.fill("#startDatetime", "2025-05-04T10:00")
        page.fill("#endDatetime", "2025-05-04T14:00")
        # not checking if form submits as this creates a real event in the database

def test_edit_event_modal_appears(page: Page) -> None:
    login_traveller(page)
    locate_journey_page(page)

    # check if any events exist
    if page.locator(".event-card").count() > 0:
        page.locator(".event-menu").first.click()
        page.locator(".editEventBtn").first.click()

        expect(page.locator("#editEventModal")).to_be_visible()
        expect(page.locator("#title")).to_be_visible()
        expect(page.locator("#description")).to_be_visible()
        expect(page.locator("#location")).to_be_visible()
        expect(page.locator("#start_datetime")).to_be_visible()
        expect(page.locator("#end_datetime")).to_be_visible()

def test_edit_journey_modal_appears(page: Page) -> None:
    login_traveller(page)
    locate_journey_page(page)
    page.locator("#journey-menu").click()
    page.locator("#editJourneyBtn").click()

    expect(page.locator(".editJourneyModal")).to_be_visible()
    expect(page.locator("#title")).to_be_visible()
    expect(page.locator("#description")).to_be_visible()
    expect(page.locator("#start_date")).to_be_visible()
    expect(page.locator("#is_public")).to_be_visible()