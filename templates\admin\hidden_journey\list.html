{% extends "base.html" %}

{% block title %}Hidden Journeys - Footprints{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <div class="d-flex align-items-center mb-4">
    <h1 class="display-6 fw-bold mb-0">
      <span class="position-relative">
        Hidden Journeys
        <span class="position-absolute start-0 bottom-0"
          style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
      </span>
    </h1>
  </div>

  <div class="card shadow-sm border-0 rounded-3">
    <div class="card-header bg-white">
      <h3 class="card-title fs-5 fw-semibold mb-0">Hidden Journeys Grouped by User</h3>
    </div>
    <div class="card-body">
      {% if journeys_by_user %}
      <div class="accordion" id="hiddenJourneysAccordion">
        {% for user_id, user_info in journeys_by_user.items() %}
        <div class="accordion-item border mb-3 rounded-3 shadow-sm">
          <h2 class="accordion-header" id="heading{{ user_id }}">
            <button class="accordion-button collapsed rounded-3" type="button" data-bs-toggle="collapse"
              data-bs-target="#collapse{{ user_id }}" aria-expanded="false" aria-controls="collapse{{ user_id }}">
              <div class="d-flex justify-content-between align-items-center w-100 me-3">
                <div class="d-flex align-items-center">
                  <div class="position-relative me-3">
                    <div class="rounded-circle overflow-hidden" style="width: 40px; height: 40px;">
                      {% if user_info.profile_image %}
                      <img src="{{ url_for('static', filename='uploads/profile_images/' + user_info.profile_image) }}"
                        alt="{{ user_info.username }}" style="width: 100%; height: 100%; object-fit: cover;"
                        onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                      {% else %}
                      <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                        alt="{{ user_info.username }}" style="width: 100%; height: 100%; object-fit: cover;">
                      {% endif %}
                    </div>
                  </div>
                  <span>
                    <strong class="fw-semibold">{{ user_info.username }}</strong>
                    {% if user_info.first_name or user_info.last_name %}
                    <span class="text-muted small ms-2">({{ user_info.first_name or '' }} {{
                      user_info.last_name or '' }})</span>
                    {% endif %}
                  </span>
                </div>
                <span class="badge bg-warning text-dark rounded-pill py-1 px-3">{{
                  user_info.journeys|length }} hidden {{ 'journey' if user_info.journeys|length == 1
                  else 'journeys' }}</span>
              </div>
            </button>
          </h2>
          <div id="collapse{{ user_id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ user_id }}"
            data-bs-parent="#hiddenJourneysAccordion">
            <div class="accordion-body">
              <div class="list-group">
                {% for journey in user_info.journeys %}
                <div class="list-group-item border mb-2 rounded-3 shadow-sm">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="fw-semibold mb-0">{{ journey.title }}</h5>
                    <div class="d-flex align-items-center">
                      {% if journey.visibility == 'private' %}
                      <span class="badge bg-secondary text-white rounded-pill me-2">
                        <i class="bi bi-lock-fill me-1"></i>Private
                      </span>
                      {% endif %}
                      <small class="text-muted">{{ journey.start_date.strftime('%B %d, %Y') }}</small>
                    </div>
                  </div>
                  <p class="mb-3 text-muted">{{ journey.description|truncate(150) }}</p>

                  {% if journey.visibility == 'private' %}
                  <div class="alert alert-warning py-2 px-3 mb-3 small">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Access Restricted:</strong> This journey has been set to private by the author and cannot be accessed by staff members.
                  </div>
                  {% endif %}

                  <div class="d-flex justify-content-end mt-2">
                    {% if journey.visibility == 'private' %}
                    <button class="btn btn-sm btn-secondary rounded-pill me-2" disabled title="Cannot view private journeys">
                      <i class="bi bi-eye-slash me-1"></i>View (Restricted)
                    </button>
                    {% else %}
                    <a href="{{ url_for('journey.get_admin_journey', journey_id=journey.id) }}"
                      class="btn btn-sm btn-primary rounded-pill me-2">
                      <i class="bi bi-eye me-1"></i>View
                    </a>
                    {% endif %}

                    <form method="post" class="unhide-form"
                      action="{{ url_for('journey.toggle_hidden', journey_id=journey.id) }}">
                      <button type="submit" class="btn btn-sm btn-warning rounded-pill unhide-btn"
                        data-journey-title="{{ journey.title }}">
                        <i class="bi bi-eye me-1"></i>Unhide
                      </button>
                    </form>
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
      {% else %}
      <div class="alert alert-info rounded-3 shadow-sm">
        <div class="d-flex align-items-center">
          <i class="bi bi-info-circle-fill me-3 fs-4"></i>
          <p class="mb-0">No hidden journeys found.</p>
        </div>
      </div>
      {% endif %}
    </div>
    <div class="card-footer bg-white">
      <p class="mb-0 text-muted small">
        <i class="bi bi-shield-lock me-2"></i>Hidden journeys are not visible to regular users in public listings.
        <strong>Note:</strong> Journeys marked as "Private" by their authors cannot be accessed by staff members.
      </p>
    </div>
  </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="unhideConfirmModal" tabindex="-1" aria-labelledby="unhideConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="unhideConfirmModalLabel">
          <i class="bi bi-eye text-warning me-2"></i>Confirm Unhide Journey
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to unhide the journey <strong id="journeyTitleSpan"></strong>?</p>
        <p class="text-muted small mb-0">
          <i class="bi bi-info-circle me-1"></i>
          This will make the journey visible to all users again in the public listings if this journey is set to public.
        </p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-warning" id="confirmUnhideBtn">
          <i class="bi bi-eye me-1"></i>Yes, Unhide Journey
        </button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const unhideButtons = document.querySelectorAll('.unhide-btn');
    const confirmModal = new bootstrap.Modal(document.getElementById('unhideConfirmModal'));
    const journeyTitleSpan = document.getElementById('journeyTitleSpan');
    const confirmUnhideBtn = document.getElementById('confirmUnhideBtn');
    let currentForm = null;

    unhideButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const journeyTitle = this.getAttribute('data-journey-title');
            const form = this.closest('.unhide-form');

            journeyTitleSpan.textContent = journeyTitle;
            currentForm = form;

            confirmModal.show();
        });
    });

    confirmUnhideBtn.addEventListener('click', function() {
        if (currentForm) {
            currentForm.submit();
        }
        confirmModal.hide();
    });
});
</script>
{% endblock %}