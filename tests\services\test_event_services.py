from services.event_service import (
    create_event,
    get_event,
    get_journey_events,
    update_event,
    delete_event
)

def test_create_event(mock_event_data, mock_location_data, mock_user_data, mocker):
    mocker.patch('services.journey_service.get_journey', return_value=(True, "Journey found", {'user_id': 1}))
    mocker.patch('services.event_service.location_data.get_location_by_name', return_value={'id': 10})
    mocker.patch('services.event_service.event_data.create_event', return_value=42)

    success, message, event_id = create_event(
        journey_id=1,
        user_id=1,
        location_name="Beach",
        title="Beach Party",
        description="Fun at the beach",
        start_datetime="2025-01-01T10:00",
        end_datetime="2025-01-01T12:00"
    )

    assert success
    assert message == "Event created successfully"
    assert event_id == 42

from services.event_service import create_event, get_event, get_journey_events

def test_create_event_invalid_permission(mocker):
    mocker.patch('services.journey_service.get_journey', return_value=(True, "Journey found", {'user_id': 2}))

    success, message, event_id = create_event(
        journey_id=1,
        user_id=1,
        location_name="Park",
        title="Morning Walk",
        description="Chill vibes",
        start_datetime="2025-01-01T08:00"
    )

    assert not success
    assert message == "Only the owner can add events to a journey"
    assert event_id is None


def test_create_event_invalid_dates(mocker):
    mocker.patch('services.journey_service.get_journey', return_value=(True, "Journey found", {'user_id': 1}))

    success, message, event_id = create_event(
        journey_id=1,
        user_id=1,
        location_name="Lake",
        title="Fishing",
        description="Catch some fish",
        start_datetime="2025-01-02T10:00",
        end_datetime="2025-01-01T10:00"
    )

    assert not success
    assert message == "End date and time must be after start date and time"
    assert event_id is None


def test_create_event_creates_new_location(mocker):
    mocker.patch('services.journey_service.get_journey', return_value=(True, "Journey found", {'user_id': 1}))
    mocker.patch('services.event_service.location_data.get_location_by_name', return_value=None)
    mocker.patch('services.event_service.location_data.create_location', return_value=55)
    mocker.patch('services.event_service.event_data.create_event', return_value=10)

    success, message, event_id = create_event(
        journey_id=1,
        user_id=1,
        location_name="New Place",
        title="Explore",
        description="New location event",
        start_datetime="2025-01-01T10:00"
    )

    assert success
    assert message == "Event created successfully"
    assert event_id == 10


def test_get_event(mocker):
    mocker.patch('services.event_service.event_data.get_event', return_value={
        'event_id': 1,
        'journey_id': 2,
        'title': "Sunset View"
    })
    mocker.patch('services.journey_service.get_journey', return_value=(True, "OK", {'user_id': 1}))

    success, message, event = get_event(1, 1)

    assert success
    assert message == "Event retrieved successfully"
    assert event['event_id'] == 1


def test_get_event_not_found(mocker):
    mocker.patch('services.event_service.event_data.get_event', return_value=None)

    success, message, event = get_event(1, 1)

    assert not success
    assert message == "Event not found"
    assert event is None


def test_get_event_permission_denied(mocker):
    mocker.patch('services.event_service.event_data.get_event', return_value={
        'event_id': 1,
        'journey_id': 1
    })
    mocker.patch('services.journey_service.get_journey', return_value=(False, "Forbidden", None))

    success, message, event = get_event(1, 1)

    assert not success
    assert message == "You do not have permission to view this event"
    assert event is None


def test_get_journey_events(mocker):
    mocker.patch('services.journey_service.get_journey', return_value=(True, "Journey found", {}))
    mocker.patch('services.event_service.event_data.get_journey_events', return_value=[
        {'event_id': 1, 'title': 'Event 1'},
        {'event_id': 2, 'title': 'Event 2'}
    ])

    success, message, events = get_journey_events(1, 1)

    assert success
    assert message == "Events retrieved successfully"
    assert len(events) == 2


def test_get_journey_events_no_access(mocker):
    mocker.patch('services.journey_service.get_journey', return_value=(False, "Forbidden", None))

    success, message, events = get_journey_events(1, 1)

    assert not success
    assert message == "Forbidden"
    assert events is None


def test_update_event(mocker):
    mocker.patch('services.event_service.get_event', return_value=(True, "Event found", {
        'event_id': 1,
        'journey_id': 1,
    }))
    mocker.patch('services.journey_service.get_journey', return_value=(True, "Journey found", {'user_id': 1}))
    mocker.patch('services.event_service.event_data.update_event', return_value=True)

    success, message = update_event(1, 1, title="New Title", description="Updated description")

    assert success
    assert message == "Event updated successfully"

def test_delete_event(mocker):
    mocker.patch('services.event_service.get_event', return_value=(True, "Event found", {
        'event_id': 1,
        'journey_id': 10
    }))
    mocker.patch('services.event_service.journey_service.get_journey', return_value=(True, "Journey found", {
        'user_id': 1
    }))
    mocker.patch('services.event_service.user_data.get_user_by_id', return_value={
        'id': 1,
        'role': 'user'
    })
    mocker.patch('services.event_service.event_data.delete_event', return_value=True)
    mocker.patch('services.event_service.event_data.get_primary_event_image', return_value={'image_filename': 'mock_image.jpg'})
    mocker.patch('services.event_service.delete_file', return_value=None)

    success, message = delete_event(event_id=1, user_id=1)

    assert success is True
    assert message == "Event deleted successfully"




