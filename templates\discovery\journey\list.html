{% extends "base.html" %}
{% from "components/pagination.html" import render_pagination %}

{% block title %}Discover - Footprints{% endblock %}

{% block content %}
<div class="container">
  <!-- Page Title -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="display-6 fw-bold">
      <span class="position-relative">
        Discovery
        <span class="position-absolute start-0 bottom-0"
          style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
      </span>
    </h1>
  </div>

  <div class="mb-4">
    <div class="border-bottom position-relative">
      <div class="d-flex">
        <div class="me-4 position-relative">
          <a href="{{ url_for('journey.get_public_journeys', active_tab='journeys', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'journeys' %}text-primary{% else %}text-secondary{% endif %}">
            Journeys
          </a>
          {% if active_tab == 'journeys' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
        <div class="position-relative">
          <a href="{{ url_for('user.get_public_users', active_tab='users', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'users' %}text-primary{% else %}text-secondary{% endif %}">
            Users
          </a>
          {% if active_tab == 'users' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Counts and Search Form -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold">Total ({{ total_count|default(0) }})</h5>

    <div class="d-flex gap-2 align-items-center">
      <form action="{{ url_for('journey.get_public_journeys' if active_tab == 'journeys' else 'user.get_users') }}"
        method="get" class="d-flex gap-2">
        <div class="input-group">
          <input type="text" class="form-control" name="q"
            placeholder="Search {{ 'journeys' if active_tab == 'journeys' else 'users' }}..." value="{{ search_term }}">
          <input type="hidden" name="active_tab" value="{{ active_tab }}">
          {% if search_term %}
          <a href="{{ url_for('journey.get_public_journeys' if active_tab == 'journeys' else 'user.get_users', active_tab=active_tab) }}"
            class="btn btn-outline-secondary border-start-0">
            <i class="bi bi-x"></i>
          </a>
          {% endif %}
        </div>
        <button type="submit" class="btn" style="background-color: black; color: white; border: none;">Search</button>
      </form>
    </div>
  </div>

  {% if journeys %}
  <!-- Journey Cards Grid -->
  <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-5">
    {% for journey in journeys %}
    <div class="col">
      <div class="card h-100 journey-card border-0 shadow-sm">
        <!-- Card Image -->
        <div class="position-relative journey-image">
          {% if journey.cover_image %}
          <img src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
            alt="{{ journey.title }}" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;">
          {% elif journey.event_image %}
          <img src="{{ url_for('static', filename='uploads/event_images/' + journey.event_image) }}"
            alt="{{ journey.title }}" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;"
            onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
          {% elif journey.image_url %}
          <img src="{{ journey.image_url }}" alt="{{ journey.title }}" class="card-img-top"
            style="object-fit: cover; height: 100%; width: 100%;"
            onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
          {% else %}
          <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
            alt="Journey placeholder" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;">
          {% endif %}
        </div>

        <!-- Card Body -->
        <div class="card-body d-flex flex-column">
          <!-- Date information -->
          <div class="d-flex journey-dates">
            <div class="small text-muted me-auto">
              <i class="bi bi-calendar3 me-1"></i>
              <span>{{ journey.start_date.strftime('%d/%m/%Y') }}</span>
            </div>
            <div class="small text-muted">
              <i class="bi bi-clock-history me-1"></i>
              <span>Updated {{ journey.updated_at.strftime('%d/%m/%Y') }}</span>
            </div>
          </div>

          <!-- Title and Description -->
          <h4 class="card-title journey-title">{{ journey.title }}</h4>
          <p class="card-text journey-description">{{ journey.description|truncate(80) }}</p>

          <!-- Author Info -->
          <div class="mt-auto journey-author">
            <div class="d-flex align-items-center">
              <div class="rounded-circle overflow-hidden author-avatar">
                {% if journey.profile_image %}
                <img src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                  alt="{{ journey.username }}" class="img-fluid"
                  onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                {% else %}
                <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                  alt="{{ journey.username }}" class="img-fluid">
                {% endif %}
              </div>
              <div class="author-info">
                <span class="text-muted small d-block author-label">Written by</span>
                <p class="mb-0 fw-medium small author-name">{{ journey.username }}</p>
              </div>
            </div>
          </div>

          <!-- Clickable link over the whole card -->
          <a href="{{ url_for('journey.get_public_journey', journey_id=journey.id) }}" class="stretched-link"
            aria-label="View {{ journey.title }}"></a>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  <!-- Pagination -->
  {{ render_pagination(page, total_pages, 'journey.get_public_journeys', q=search_term, filter=filter,
  active_tab=active_tab) }}

  {% else %}
  <!-- No Results Alert -->
  <div class="alert rounded-4"
    style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2);">
    <div class="d-flex align-items-center">
      <i class="bi bi-info-circle-fill me-3 fs-4"></i>
      <p class="mb-0">No matching public journeys found.</p>
    </div>
  </div>
  {% endif %}
</div>

<style>
  /* Card layout and sizing */
  .journey-card {
    height: 380px;
    border-radius: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
  }

  .journey-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .journey-image {
    height: 180px;
    border-radius: 15px 15px 0 0;
    overflow: hidden;
  }

  .journey-dates {
    margin-bottom: 8px;
  }

  .journey-title {
    font-weight: bold;
    font-size: 1.1rem;
    line-height: 1.3;
    margin-bottom: 6px;
    max-height: 2.7rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .journey-description {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.5;
    max-height: 3.8rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .journey-author {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  .author-avatar {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }

  .author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .author-info {
    margin-left: 8px;
  }

  .author-label {
    color: #6c757d;
    line-height: 1;
  }

  .author-name {
    font-weight: 500;
  }

  /* Responsive adjustments */
  @media (max-width: 767.98px) {
    .row-cols-1>.col {
      margin-bottom: 1rem;
    }
  }
</style>
{% endblock %}