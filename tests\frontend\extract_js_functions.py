"""
JavaScript Function Extractor

This script extracts all JavaScript functions from HTML templates
to create a comprehensive list for tracking usage.
"""

import re
import os
from typing import Set, Dict, List


def extract_js_functions_from_file(file_path: str) -> Dict[str, Set[str]]:
    """Extract JavaScript functions from an HTML file"""
    
    functions = {
        'function_declarations': set(),
        'function_calls': set(),
        'event_handlers': set(),
        'jquery_functions': set(),
        'onclick_handlers': set()
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract function declarations
        # Pattern: function functionName() or var functionName = function()
        func_declarations = re.findall(r'function\s+(\w+)\s*\(', content)
        functions['function_declarations'].update(func_declarations)
        
        var_func_declarations = re.findall(r'(?:var|let|const)\s+(\w+)\s*=\s*function', content)
        functions['function_declarations'].update(var_func_declarations)
        
        # Extract function calls
        # Pattern: functionName(
        func_calls = re.findall(r'(\w+)\s*\(', content)
        functions['function_calls'].update(func_calls)
        
        # Extract onclick handlers
        onclick_handlers = re.findall(r'onclick=["\']([^"\']*)["\']', content)
        for handler in onclick_handlers:
            # Extract function name from onclick handler
            func_match = re.search(r'(\w+)\s*\(', handler)
            if func_match:
                functions['onclick_handlers'].add(func_match.group(1))
        
        # Extract jQuery function calls
        jquery_calls = re.findall(r'\$\([^)]*\)\.(\w+)', content)
        functions['jquery_functions'].update(jquery_calls)
        
        # Extract event handlers like data-bs-toggle, etc.
        event_attrs = re.findall(r'data-[\w-]+=["\']([^"\']*)["\']', content)
        functions['event_handlers'].update(event_attrs)
        
        # Extract ID and class names that might be used in JavaScript
        ids = re.findall(r'id=["\']([^"\']*)["\']', content)
        classes = re.findall(r'class=["\']([^"\']*)["\']', content)
        
        # Store IDs and classes for reference
        functions['element_ids'] = set(ids)
        functions['element_classes'] = set()
        for class_list in classes:
            functions['element_classes'].update(class_list.split())
    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return functions


def extract_all_template_functions() -> Dict[str, Dict]:
    """Extract functions from all relevant templates"""
    
    template_files = [
        'templates/event/detail.html',
        'templates/event/edit.html',
        'templates/event/create.html',
        'templates/base.html'
    ]
    
    all_functions = {}
    
    for template_file in template_files:
        if os.path.exists(template_file):
            print(f"Extracting functions from {template_file}...")
            functions = extract_js_functions_from_file(template_file)
            all_functions[template_file] = functions
        else:
            print(f"Template file not found: {template_file}")
    
    return all_functions


def generate_comprehensive_function_list() -> Set[str]:
    """Generate comprehensive list of all JavaScript functions to track"""
    
    all_functions = extract_all_template_functions()
    comprehensive_set = set()
    
    for template, functions in all_functions.items():
        print(f"\n{template}:")
        print(f"  Function declarations: {len(functions['function_declarations'])}")
        print(f"  Function calls: {len(functions['function_calls'])}")
        print(f"  Onclick handlers: {len(functions['onclick_handlers'])}")
        print(f"  jQuery functions: {len(functions['jquery_functions'])}")
        
        # Add all function types to comprehensive set
        comprehensive_set.update(functions['function_declarations'])
        comprehensive_set.update(functions['function_calls'])
        comprehensive_set.update(functions['onclick_handlers'])
    
    # Filter out common JavaScript/jQuery methods that aren't custom functions
    common_methods = {
        'click', 'submit', 'change', 'load', 'ready', 'on', 'off', 'trigger',
        'addClass', 'removeClass', 'toggleClass', 'hasClass', 'attr', 'removeAttr',
        'val', 'text', 'html', 'append', 'prepend', 'remove', 'hide', 'show',
        'fadeIn', 'fadeOut', 'slideUp', 'slideDown', 'animate', 'stop',
        'each', 'map', 'filter', 'find', 'parent', 'children', 'siblings',
        'console', 'log', 'error', 'warn', 'alert', 'confirm', 'prompt',
        'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval',
        'addEventListener', 'removeEventListener', 'preventDefault', 'stopPropagation'
    }
    
    # Remove common methods
    custom_functions = comprehensive_set - common_methods
    
    return custom_functions


def save_function_list_for_tracking():
    """Save comprehensive function list for use in tracking tests"""
    
    functions = generate_comprehensive_function_list()
    
    # Create Python code for the function list
    function_list_code = f"""
# Auto-generated comprehensive function list for tracking
# Generated on: {__import__('datetime').datetime.now().isoformat()}

COMPREHENSIVE_FUNCTION_LIST = {{
{chr(10).join(f"    '{func}'," for func in sorted(functions))}
}}

# Convert to set for faster lookups
COMPREHENSIVE_FUNCTION_SET = set(COMPREHENSIVE_FUNCTION_LIST)
"""
    
    with open('tests/frontend/extracted_functions.py', 'w') as f:
        f.write(function_list_code)
    
    print(f"\nSaved {len(functions)} custom functions to tests/frontend/extracted_functions.py")
    print("Top 20 functions found:")
    for func in sorted(list(functions))[:20]:
        print(f"  - {func}")
    
    return functions


if __name__ == "__main__":
    print("Extracting JavaScript functions from templates...")
    functions = save_function_list_for_tracking()
    print(f"\nTotal custom functions found: {len(functions)}")
