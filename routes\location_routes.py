from flask import Blueprint, jsonify, render_template, request, redirect, url_for, flash
from services import location_service
from utils.security import content_manager_required, login_required

bp = Blueprint('location', __name__, url_prefix='/location')

@bp.route('/manage', methods=['GET'])
@content_manager_required
def get_locations():
    """Location management with pagination and search"""
    # Get pagination parameters
    search_term = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    limit = 10
    offset = (page - 1) * limit

    # Get locations based on search term
    if search_term:
        locations = location_service.search_locations(search_term, limit=limit, offset=offset)
        total_count = location_service.count_search_results(search_term)
    else:
        locations = location_service.get_locations(limit=limit, offset=offset)
        total_count = location_service.get_locations_count()

    # Calculate total pages
    total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1

    return render_template('admin/location/list.html',
                          locations=locations,
                          page=page,
                          total_pages=total_pages,
                          total_count=total_count,
                          search_term=search_term,
                          per_page=limit)

@bp.route('/<int:location_id>')
@login_required
def get_location(location_id):
    """Get location details for modal display"""
    # Query the location by ID
    location = location_service.get_location(location_id)

    if not location:
        return "Location not found", 404

    # Simply render the location details template with the location data
    return render_template('admin/location/detail.html', location=location)


@bp.route('/update/<int:location_id>', methods=['POST'])
@content_manager_required
def update_location(location_id):
    """Update a location from the modal form"""
    name = request.form.get('name')

    if not name:
        flash('Location name is required', 'danger')
        return redirect(url_for('location.get_locations'))

    success, message = location_service.update_location(location_id, name)

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('location.get_locations'))

@bp.route('/update/<int:location_id>', methods=['GET'])
@content_manager_required
def get_location_edit_form(location_id):
    """Get location edit form for modal display"""
    # Query the location by ID
    location = location_service.get_location(location_id)

    if not location:
        return "Location not found", 404

    # Simply render the location edit form template with the location data
    return render_template('admin/location/edit.html', location=location)

@bp.route('/merge', methods=['GET', 'POST'])
@content_manager_required
def merge_locations():
    """Merge multiple locations"""
    source_id = request.args.get('source_id', type=int)
    search_term = request.args.get('q', '')

    if request.method == 'POST':
        if 'validation_error' in request.form:
            flash(request.form.get('validation_error'), 'danger')
            return redirect(url_for('location.merge_locations', q=search_term))

        if 'source_id' in request.form and 'target_id' in request.form:
            source_id = request.form.get('source_id', type=int)
            target_id = request.form.get('target_id', type=int)

            if not source_id or not target_id:
                flash('Both source and target locations must be selected', 'danger')
                return redirect(url_for('location.merge_locations'))

            if source_id == target_id:
                flash('Source and target locations cannot be the same', 'danger')
                return redirect(url_for('location.merge_locations', source_id=source_id))

            success, message = location_service.merge_locations(source_id, target_id)
            flash(message, 'success' if success else 'danger')

            if success:
                return redirect(url_for('location.get_locations'))
            else:
                return redirect(url_for('location.merge_locations', source_id=source_id))

        if 'source_ids' in request.form:
            source_ids = request.form.getlist('source_ids')
            target_location = request.form.get('target_location', '')
            target_id = request.form.get('target_id', '')

            if not source_ids:
                flash('Please select at least one source location to merge', 'danger')
                return redirect(url_for('location.merge_locations', q=search_term))

            if len(source_ids) < 2:
                flash('Please select at least two locations to merge', 'danger')
                return redirect(url_for('location.merge_locations', q=search_term))

            if not target_location:
                flash('Please select or enter a target location', 'danger')
                return redirect(url_for('location.merge_locations', q=search_term))

            if not target_id:
                # Convert source_ids to integers
                source_ids_int = [int(src_id) for src_id in source_ids]

                # Use the new function that creates and merges in one transaction
                success, message, new_target_id = location_service.create_location_and_merge(
                    target_location,
                    source_ids_int
                )
                if not success:
                    flash(f"Failed to create and merge: {message}", 'danger')
                    return redirect(url_for('location.merge_locations', q=search_term))

                flash(message, 'success')
                return redirect(url_for('location.get_locations'))
            else:
                target_id = int(target_id)

                source_ids = [int(src_id) for src_id in source_ids if int(src_id) != target_id]

                if not source_ids:
                    flash('Cannot merge: Target location cannot be selected as a source location', 'danger')
                    return redirect(url_for('location.merge_locations', q=search_term))

                success_count = 0
                fail_count = 0

                source_names = []
                for src_id in source_ids:
                    src_location = location_service.get_location(int(src_id))
                    if src_location:
                        source_names.append(src_location['name'])

                for src_id in source_ids:
                    try:
                        success, message = location_service.merge_locations(int(src_id), target_id)
                        if success:
                            success_count += 1
                        else:
                            fail_count += 1
                            flash(f"Failed to merge location ID {src_id}: {message}", 'danger')
                    except Exception as e:
                        fail_count += 1
                        flash(f"Error merging location ID {src_id}: {str(e)}", 'danger')

                if success_count > 0:
                    source_names_str = ", ".join(source_names[:3])
                    if len(source_names) > 3:
                        source_names_str += f" and {len(source_names) - 3} more"

                    flash(f"Successfully merged {success_count} location(s) ({source_names_str}) into '{target_location}'", 'success')

                if success_count > 0 and fail_count == 0:
                    return redirect(url_for('location.get_locations'))
                else:
                    return redirect(url_for('location.merge_locations', q=search_term))

    source = None
    if source_id:
        source = location_service.get_location(source_id)

    target_locations = []
    if search_term:
        target_locations = location_service.search_locations(search_term, limit=100, offset=0)
    elif source:
        target_locations = location_service.get_locations(limit=100, offset=0)

    template_path = 'admin/location/merge_locations.html'

    return render_template(template_path,
                          source=source,
                          target_locations=target_locations,
                          search_term=search_term)


@bp.route('/search', methods=['GET'])
@login_required
def search_locations():
    """Search locations based on user input."""
    query = request.args.get('query', '').strip()

    if not query:
        locations = location_service.get_locations()
    else:
        locations = location_service.search_locations(query)

    results = [{'name': location['name']} for location in locations]

    return jsonify(results)

@bp.route('/map/<int:journey_id>', methods=['GET'])
def get_journey_map(journey_id):
    """Get map of locations for a journey"""
    locations = location_service.get_locations_for_journey(journey_id=journey_id)

    return render_template('journey/map.html', locations=locations)
