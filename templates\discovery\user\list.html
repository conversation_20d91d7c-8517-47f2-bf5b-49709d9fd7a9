{% extends "base.html" %}
{% from "components/pagination.html" import render_pagination %}

{% block title %}Discover Users - Footprints{% endblock %}

{% block content %}
<div class="container">
  <!-- Page Title -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="display-6 fw-bold">
      <span class="position-relative">
        Discovery
        <span class="position-absolute start-0 bottom-0"
          style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
      </span>
    </h1>
  </div>

  <div class="mb-4">
    <div class="border-bottom position-relative">
      <div class="d-flex">
        <div class="me-4 position-relative">
          <a href="{{ url_for('journey.get_public_journeys', active_tab='journeys', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'journeys' %}text-primary{% else %}text-secondary{% endif %}">
            Journeys
          </a>
          {% if active_tab == 'journeys' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
        <div class="position-relative">
          <a href="{{ url_for('user.get_public_users', active_tab='users', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'users' %}text-primary{% else %}text-secondary{% endif %}">
            Users
          </a>
          {% if active_tab == 'users' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Counts and Search Form -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold">Total ({{ total_count|default(0) }})</h5>

    <div class="d-flex gap-2 align-items-center">
      <form action="{{ url_for('user.get_public_users') }}" method="get" class="d-flex gap-2">
        <div class="input-group">
          <input type="text" class="form-control" name="q" placeholder="Search users..." value="{{ search_term }}">
          <input type="hidden" name="active_tab" value="{{ active_tab }}">
          {% if search_term %}
          <a href="{{ url_for('user.get_public_users', active_tab=active_tab) }}"
            class="btn btn-outline-secondary border-start-0">
            <i class="bi bi-x"></i>
          </a>
          {% endif %}
        </div>
        <button type="submit" class="btn" style="background-color: black; color: white; border: none;">Search</button>
      </form>
    </div>
  </div>

  {% if users %}
  <!-- User Cards Grid -->
  <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-5">
    {% for user in users %}
    <div class="col">
      <div class="card h-100 user-card border-0 shadow-sm">
        <!-- User Profile Image -->
        <div class="text-center pt-3">
          <div class="mx-auto rounded-circle overflow-hidden user-avatar">
            {% if user.profile_image %}
            <img src="{{ url_for('static', filename='uploads/profile_images/' + user.profile_image) }}"
              alt="{{ user.username }}" class="img-fluid"
              onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
            {% else %}
            <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
              alt="{{ user.username }}" class="img-fluid">
            {% endif %}
          </div>
        </div>

        <!-- Card Body -->
        <div class="card-body d-flex flex-column text-center">
          <!-- Username and Name -->
          <h4 class="card-title user-title">{{ user.username }}</h4>
          {% if user.first_name or user.last_name %}
          <p class="card-text user-name">{{ user.first_name or '' }} {{ user.last_name or '' }}</p>
          {% endif %}

          <!-- Location -->
          {% if user.location %}
          <div class="mb-1">
            <i class="bi bi-geo-alt text-muted"></i>
            <span class="text-muted">{{ user.location }}</span>
          </div>
          {% endif %}

          <!-- Bio/Description -->
          {% if user.biography %}
          <p class="card-text user-description">{{ user.biography|truncate(75) }}</p>
          {% elif user.description %}
          <p class="card-text user-description">{{ user.description|truncate(75) }}</p>
          {% endif %}

          <!-- Interests -->
          {% if user.interests %}
          <div class="mt-1">
            <h6 class="text-muted small mb-1">Interests</h6>
            <div class="d-flex flex-wrap justify-content-center gap-1">
              {% for interest in user.interests.split(',')[:2] %}
              <span class="badge bg-light text-dark rounded-pill">{{ interest.strip() }}</span>
              {% endfor %}
              {% if user.interests.split(',')|length > 3 %}
              <span class="badge bg-light text-dark rounded-pill">+{{ user.interests.split(',')|length - 3 }}</span>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <a href="{{ url_for('account.get_public_profile', username=user.username) }}" class="stretched-link"
            aria-label="View {{ user.username }}"></a>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  <!-- Pagination -->
  {{ render_pagination(page, total_pages, 'user.get_public_users', q=search_term, active_tab=active_tab) }}

  {% else %}
  <!-- No Results Alert -->
  <div class="alert rounded-4"
    style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2);">
    <div class="d-flex align-items-center">
      <i class="bi bi-info-circle-fill me-3 fs-4"></i>
      <p class="mb-0">No public users found matching your search criteria.</p>
    </div>
  </div>
  {% endif %}
</div>

<style>
  /* Card layout and sizing */
  .user-card {
    height: 380px;
    border-radius: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
  }

  .user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .user-avatar {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    border: 1px solid black;
  }

  .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .user-title {
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
  }

  .user-name {
    color: #6c757d;
    font-size: 0.9rem;
  }

  .user-description {
    color: #6c757d;
    font-size: 0.85rem;
    line-height: 1.5;
    max-height: 4.5rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  /* Responsive adjustments */
  @media (max-width: 767.98px) {
    .row-cols-1>.col {
      margin-bottom: 1rem;
    }
  }
</style>
{% endblock %}