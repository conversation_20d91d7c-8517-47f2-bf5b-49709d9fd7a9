{% block content %}
<div class="container-fluid" id="createTicketModal">
  <div class="row justify-content-center">
    <form method="post" action="{{ url_for('helpdesk.create_ticket') }}" id="create-ticket-form"
      enctype="multipart/form-data" novalidate class="row needs-validation">
      <div>

        <div class="mb-3">
          <label for="title" class="form-label">Title *</label>
          <input type="text" class="form-control" id="title" name="title" required minlength="5" maxlength="50"
            value="{{ request.form.get('title', '') }}" />
          <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
        </div>

        <div class="mb-3">
          <label for="category" class="form-label">Category *</label>
          <select id="category" name="category" class="form-select" required>
            <option value="">Select a category</option>
            <option value="help" {{ 'selected' if request.form.get('category')=='help' else '' }}>General Help</option>
            <option value="bug" {{ 'selected' if request.form.get('category')=='bug' else '' }}>Bug Report</option>
            <option value="appeal" {{ 'selected' if request.form.get('category')=='appeal' else '' }}>Account Ban Appeal
            </option>
            <option value="other" {{ 'selected' if request.form.get('category')=='other' else '' }}>Other</option>
          </select>
          <div class="invalid-feedback">Please select a category.</div>
        </div>

        <!-- Username field for ban appeals -->
        <div class="mb-3" id="usernameField" style="display: none;">
          <label for="username" class="form-label">Username *</label>
          <input type="text" class="form-control" id="username" name="username"
            value="{{ request.form.get('username', '') }}" placeholder="Enter the username of the banned account" />
          <div class="invalid-feedback">Username is required for ban appeals.</div>
          <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            Please enter the exact username of the banned account you want to appeal for.
          </div>
        </div>

        <div class="mb-3">
          <label for="description" class="form-label">Description *</label>
          <textarea class="form-control" id="description" name="description" required minlength="5" maxlength="1000"
            rows="5"
            placeholder="Please provide detailed information about your request or appeal reason">{{ request.form.get('description', '') }}</textarea>
          <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
          <div class="form-text" id="appealHelpText" style="display: none;">
            <i class="bi bi-info-circle me-1"></i>
            For ban appeals, please explain why you believe your account should be unbanned. Include any relevant
            context or changes in behavior.
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const categorySelect = document.getElementById('category');
    const usernameField = document.getElementById('usernameField');
    const usernameInput = document.getElementById('username');
    const appealHelpText = document.getElementById('appealHelpText');

    function toggleUsernameField() {
      if (categorySelect.value === 'appeal') {
        usernameField.style.display = 'block';
        usernameInput.required = true;
        appealHelpText.style.display = 'block';
      } else {
        usernameField.style.display = 'none';
        usernameInput.required = false;
        appealHelpText.style.display = 'none';
      }
    }

    // Initial check
    toggleUsernameField();

    // Listen for changes
    categorySelect.addEventListener('change', toggleUsernameField);
  });
</script>

{% endblock %}