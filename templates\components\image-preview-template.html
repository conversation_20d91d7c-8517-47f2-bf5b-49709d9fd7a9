<!-- 
  Image Preview Component Template
  
  This template shows how to use the modular image preview system.
  Copy this structure to any template that needs image upload with preview.
  
  Required:
  1. Include the CSS and JS files (done automatically by the setupImagePreview function)
  2. Add the HTML structure below
  3. Call setupImagePreview() in your JavaScript
  
  Example usage in your template:
  {% include 'components/image-preview-template.html' %}
-->

<!-- File Input -->
<div class="form-group">
  <label for="images" class="modern-label">
    <i class="bi bi-camera"></i>
    Upload Images
  </label>
  <input type="file" 
         class="modern-input" 
         id="images" 
         name="images" 
         multiple 
         accept=".png,.jpg,.jpeg,.gif"
         data-premium="true">
  <div class="input-help">
    <i class="bi bi-info-circle"></i>
    Upload up to 10 images. Maximum 5MB each. Allowed formats: PNG, JPG, JPEG, GIF.
  </div>
  <div class="invalid-feedback" id="imagesFeedback">
    <!-- Validation messages will appear here -->
  </div>
  
  <!-- Image Preview Container -->
  <div id="imagePreviewContainer" class="image-preview-grid" style="display: none;">
    <div class="preview-header">
      <span class="preview-title">
        <i class="bi bi-eye"></i>
        Selected Images
      </span>
      <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.imagePreviewInstance?.clearAll()">
        <i class="bi bi-x"></i>
        Clear All
      </button>
    </div>
    <div id="previewGrid" class="row g-2">
      <!-- Preview images will be inserted here -->
    </div>
  </div>
</div>

<!-- JavaScript Initialization -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Setup image preview with custom options
  if (window.setupImagePreview) {
    window.setupImagePreview('#images', {
      maxFiles: 10,
      allowMultiple: true,
      containerSelector: '#imagePreviewContainer',
      gridSelector: '#previewGrid',
      feedbackSelector: '#imagesFeedback',
      
      // Optional callbacks
      onFileSelect: function(files) {
        console.log('Files selected:', files);
      },
      onFileRemove: function(index, remainingFiles) {
        console.log('File removed at index:', index);
      },
      onClear: function() {
        console.log('All files cleared');
      },
      onValidationError: function(message) {
        console.log('Validation error:', message);
      }
    });
  } else {
    // Fallback: load the scripts manually
    const scripts = [
      '/static/js/file-validation.js',
      '/static/js/image-preview.js'
    ];
    
    const css = '/static/css/image-preview.css';
    
    // Load CSS
    if (!document.querySelector(`link[href="${css}"]`)) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = css;
      document.head.appendChild(link);
    }
    
    // Load scripts
    scripts.forEach(src => {
      if (!document.querySelector(`script[src="${src}"]`)) {
        const script = document.createElement('script');
        script.src = src;
        script.onload = function() {
          if (src.includes('image-preview.js')) {
            // Initialize after image-preview.js loads
            window.setupImagePreview('#images', {
              maxFiles: 10,
              allowMultiple: true
            });
          }
        };
        document.head.appendChild(script);
      }
    });
  }
});
</script>

<!-- 
  Configuration Options for setupImagePreview():
  
  {
    // Container selectors
    containerSelector: '#imagePreviewContainer',  // Preview container
    gridSelector: '#previewGrid',                 // Grid for preview cards
    feedbackSelector: '#imagesFeedback',          // Error message container
    
    // Preview settings
    maxFiles: 10,                                 // Maximum number of files
    allowMultiple: true,                          // Allow multiple file selection
    showFileInfo: true,                           // Show filename and size
    showRemoveButton: true,                       // Show remove button on cards
    showClearAll: true,                           // Show clear all button
    
    // Styling
    cardClass: 'preview-card',                    // CSS class for preview cards
    errorClass: 'preview-error',                  // CSS class for error state
    imageHeight: '120px',                         // Height of preview images
    colClass: 'col-6 col-md-4',                  // Bootstrap column classes
    
    // Callbacks
    onFileSelect: function(files) { },            // Called when files are selected
    onFileRemove: function(index, files) { },     // Called when a file is removed
    onValidationError: function(message) { },     // Called on validation error
    onClear: function() { }                       // Called when all files are cleared
  }
  
  Methods available on the ImagePreview instance:
  
  - setupInput(input, options)     // Setup preview for an input
  - removeFile(index)              // Remove file by index
  - clearAll()                     // Clear all files
  - getFiles()                     // Get current files array
  - hasFiles()                     // Check if any files are selected
  - showPreview()                  // Show preview container
  - hidePreview()                  // Hide preview container
  - showValidationError(message)   // Show validation error
  - clearValidation()              // Clear validation state
-->
