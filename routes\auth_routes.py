from flask import Blueprint, render_template, request, redirect, url_for, flash, session, current_app
from services import auth_service, subscription_service, notification_service, helpdesk_service
from utils.security import set_user_session, clear_user_session, login_required
from datetime import date

bp = Blueprint('auth', __name__)

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """Register a new user"""
    # Redirect if already logged in
    if 'user_id' in session:
        flash('You are already logged in. Please logout to register a new user.', 'info')
        return redirect(url_for('main.get_user_dashboard'))

    request_types = helpdesk_service.get_request_type_enum_values()
    if request.method == 'POST':
        # Get form data
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        location = request.form.get('location')

        # Register user
        success, message, user_id = auth_service.register_user(
            username=username,
            email=email,
            password=password,
            confirm_password=confirm_password,
            first_name=first_name,
            last_name=last_name,
            location=location
        )

        if success:
            # Set default privacy settings
            try:
                from data.privacy_settings_data import set_default_privacy_settings
                set_default_privacy_settings(user_id)
            except Exception as e:
                current_app.logger.error(f"Error setting default privacy settings: {e}")

            flash(message, 'success')
            return redirect(url_for('auth.login'))
        else:
            flash(message, 'danger')
            # Pass form data back to template
            return render_template('auth/register.html',
                                   username=username,
                                   email=email,
                                   first_name=first_name,
                                   last_name=last_name,
                                   location=location,
                                   request_types=request_types)

    return render_template('auth/register.html', request_types=request_types)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """Login a user"""
    # Redirect if already logged in
    if 'user_id' in session:
        flash('You are already logged in.', 'info')
        from utils.permissions import Roles
        if session.get('role') == Roles.ADMIN:
            return redirect(url_for('main.get_admin_dashboard'))
        elif session.get('role') == Roles.EDITOR:
            return redirect(url_for('main.get_editor_dashboard'))
        else:
            return redirect(url_for('main.get_user_dashboard'))

    request_types = helpdesk_service.get_request_type_enum_values()
    if request.method == 'POST':
        # Get form data
        username = request.form.get('username')
        password = request.form.get('password')

        # Authenticate user
        success, message, user = auth_service.authenticate_user(
            username=username,
            password=password
        )

        if success:
            # Set session data
            set_user_session(user)

            # --- Subscription expiry/expired notification logic for traveller ---
            today = date.today()
            is_premium = False
            if user['role'] == 'traveller':
                sub = subscription_service.get_user_active_subscription(user['id'])
                if sub and sub.get('end_date'):
                    is_premium = True
                    days_left = (sub['end_date'] - today).days
                    if 0 <= days_left <= 7:
                        if days_left == 0:
                            msg = f"Your subscription expires today ({sub['end_date']})."
                        elif days_left == 1:
                            msg = f"Your subscription expires tomorrow ({sub['end_date']})."
                        else:
                            msg = f"Your subscription will expire in {days_left} days (on {sub['end_date']})."
                        if not notification_service.notification_exists(user['id'], 'subscription', msg):
                            notification_service.create_notification(
                                user_id=user['id'],
                                notification_type='subscription',
                                content=msg,
                                related_id=sub.get('id')
                            )
                # Check for expired subscription (no active, but had premium before)
                elif sub is None:
                    status = subscription_service.get_user_subscription_status_for_login(user['id'])
                    if status.get('status') in ['premium', 'trial'] and status.get('end_date') and status['end_date'] < today:
                        msg = (
                            f"Your subscription expired on {status['end_date']}. "
                            f"You no longer have access to premium features. "
                            f"Renew your subscription</a> to regain access."
                        )
                        if not notification_service.notification_exists(user['id'], 'subscription', msg):
                            notification_service.create_notification(
                                user_id=user['id'],
                                notification_type='subscription',
                                content=msg,
                                related_id=None
                            )
                session['is_premium'] = is_premium

            flash(message, 'success')
            from utils.permissions import Roles
            if session.get('role') == Roles.ADMIN:
                return redirect(url_for('main.get_admin_dashboard'))
            elif session.get('role') == Roles.SUPPORT_TECH:
                return redirect(url_for('main.get_support_tech_dashboard'))
            elif session.get('role') == Roles.EDITOR:
                return redirect(url_for('main.get_editor_dashboard'))
            elif session.get('role') == Roles.MODERATOR:
                return redirect(url_for('main.get_moderator_dashboard'))
            elif session.get('role') == Roles.TRAVELLER and is_premium:
                return redirect(url_for('departure_board.get_departure_list'))
            else:
                return redirect(url_for('main.get_user_dashboard'))
        else:
            flash(message, 'danger')
            # Keep username for convenience
            return render_template('auth/login.html', username=username, request_types=request_types)

    return render_template('auth/login.html', request_types=request_types)

@bp.route('/logout')
@login_required
def logout():
    """Logout a user"""
    clear_user_session()
    flash('You have been logged out.', 'info')
    return redirect(url_for('main.get_landing_page'))
