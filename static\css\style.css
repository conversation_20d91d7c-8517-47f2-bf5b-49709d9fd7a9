/* Custom CSS for Footprints */
/* Custom primary color */
:root {
  --primary-color: #4e6bff;
  --primary-rgb: 78, 107, 255;
  --primary-bg-subtle: rgba(var(--primary-rgb), 0.1);
  --primary-darker: #3f56cc;

  --dark-color: #1a1a1a;
  --light-color: #ffffff;
  --white-opacity-50: rgba(255, 255, 255, 0.5);
  --white-opacity-95: rgba(255, 255, 255, 0.95);

  --secondary-color: #6c757d;
  --bs-secondary-color: rgba(33, 37, 41, 0.75);

  --success-rgb: 25, 135, 84;
  --success-bg-subtle: rgba(var(--success-rgb), 0.1);

  --journey-card-height: 350px;
  --journey-image-height: 160px;
  --announcement-line-height: 1.5em;
  --announcement-max-lines: 3;
  --announcement-content-height: calc(
    var(--announcement-line-height) * var(--announcement-max-lines)
  );
}

/* Main content area */
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

textarea {
  resize: none;
}

/* Journey cards */
.journey-card {
  transition: transform 0.2s;
}

.journey-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Event timeline */
.event-timeline {
  position: relative;
  padding-left: 30px;
}

.event-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 15px;
  width: 2px;
  background-color: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item::before {
  content: "";
  position: absolute;
  left: -30px;
  top: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #007bff;
}

/* Image display */
.event-image {
  width: 100%;
  max-width: 400px;
  height: 250px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/* Location autocomplete */
.ui-autocomplete {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* login-form-label */
.login-form-label {
  margin-top: 60px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* login-form-label-username */
.login-form-label-username {
  margin-top: 20px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* login-form-label-password */
.login-form-label-password {
  margin-top: 20px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* navaigation bar */
.navbar {
  background-color: rgb(34, 34, 34);
}

/* login-button */
.login-button {
  margin-top: 40px;
}

/* signup-button  */
.signup-button {
  margin-top: 10px;
}

/* login-container */
.login-container {
  display: flex;
  align-items: center;
  min-height: calc(100vh - 240px);
  padding-top: 0;
  padding-bottom: 0;
}

.full-height {
  width: 100%;
}

.login-wrapper {
  overflow: hidden;
  border-radius: 0.5rem;
}

.login-form-section {
  background-color: white;
}

.login-image-wrapper {
  height: 500px;
  overflow: hidden;
}

.register-image-wrapper {
  height: 650px;
  overflow: hidden;
}

.login-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 0.5rem;
}

.register-image {
  width: 96%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 0.5rem;
}

.form-control {
  border: 1px solid #e9ecef;
}

.form-control:focus {
  background-color: #fff;
}

.btn-dark {
  background-color: #212529;
}

.btn-dark:hover {
  background-color: #000;
}

@media (max-width: 767.98px) {
  .login-container {
    padding-top: 2rem;
    padding-bottom: 2rem;
    min-height: auto;
  }
}

/* Pagination styling */
.pagination .page-link:focus {
  box-shadow: none;
}

.invalid-feedback {
  font-size: 0.8em !important;
  margin-top: 0.1rem !important;
}
