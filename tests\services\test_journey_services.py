from services.journey_service import (
    create_journey,
    get_journey,
    update_journey,
    delete_journey,
    update_journey_visibility,
    update_journey_hidden_status,
    get_private_journeys,
    get_public_journeys
)


def test_create_journey_success(mock_journey_data, mock_user_data):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False}
    mock_journey_data.create_journey.return_value = 1  # journey_id

    success, message, journey_id = create_journey(1, "Test Journey", "Description", "2025-01-01", False)

    assert success
    assert message == "Journey created successfully"
    assert journey_id == 1


def test_create_journey_blocked_user(mock_journey_data, mock_user_data):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': True}

    success, message, journey_id = create_journey(1, "Test Journey", "Description", "2025-01-01", True)

    assert not success
    assert message == "You have been blocked from sharing journeys"
    assert journey_id is None


def test_get_journey_success(mock_journey_data, mock_user_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'user_id': 1,
        'is_public': True,
        'is_hidden': False
    }

    success, message, journey = get_journey(1, 1)

    assert success
    assert message == "Journey retrieved successfully"
    assert journey['journey_id'] == 1


def test_get_journey_not_found(mock_journey_data):
    mock_journey_data.get_journey.return_value = None

    success, message, journey = get_journey(1, 1)

    assert not success
    assert message == "Journey not found"
    assert journey is None


def test_update_journey_success(mock_journey_data, mock_user_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False
    }

    mock_journey_data.update_journey.return_value = None

    success, message = update_journey(1, 1, title="Updated Journey")

    assert success
    assert message == "Journey updated successfully"



def test_update_journey_permission_error(mock_journey_data, mock_user_data, mocker):
    journey_data = {
        'journey_id': 1,
        'user_id': 2,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False
    }

    mock_get_journey = mocker.patch('services.journey_service.get_journey',
                                    return_value=(True, "Journey found", journey_data))

    success, message = update_journey(1, 1, title="Updated Journey")

    assert not success
    assert message == "You do not have permission to update this journey"

def test_delete_journey_success(mock_journey_data, mock_user_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False
    }

    mock_journey_data.delete_journey.return_value = None

    success, message = delete_journey(1, 1)

    assert success
    assert message == "Journey deleted successfully"


def test_delete_journey_permission_error(mock_journey_data, mock_user_data, mocker):
    journey_data = {
        'journey_id': 1,
        'user_id': 2,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False
    }

    mock_get_journey = mocker.patch('services.journey_service.get_journey',
                                    return_value=(True, "Journey found", journey_data))

    success, message = delete_journey(1, 1)

    assert not success
    assert message == "Only the owner can delete a journey"


def test_update_journey_visibility_success(mock_journey_data, mock_user_data, mocker):
    journey_data = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'visibility': 'private',
        'is_hidden': False
    }

    mock_get_journey = mocker.patch('services.journey_service.get_journey',
                                    return_value=(True, "Journey found", journey_data))
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'role': 'traveller'}
    mock_journey_data.update_journey_visibility.return_value = 1

    success, message = update_journey_visibility(1, 1, 'public')

    assert success
    assert message == "Journey visibility set to public successfully"


def test_update_journey_visibility_blocked_user(mock_journey_data, mock_user_data, mocker):
    journey_data = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'visibility': 'private',
        'is_hidden': False
    }

    mock_get_journey = mocker.patch('services.journey_service.get_journey',
                                    return_value=(True, "Journey found", journey_data))
    mock_user_data.get_user_by_id.return_value = {'is_blocked': True, 'role': 'traveller'}

    success, message = update_journey_visibility(1, 1, 'public')

    assert not success
    assert message == "You have been blocked from sharing journeys"


def test_update_journey_hidden_status_success(mock_journey_data, mock_user_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False
    }

    mock_user_data.get_user_by_id.return_value = {'role': 'admin'}
    mock_journey_data.update_journey_hidden_status.return_value = 1

    success, message = update_journey_hidden_status(1, 1, True)

    assert success
    assert message == "Journey set to hidden successfully"


def test_update_journey_hidden_status_with_notification(mock_journey_data, mock_user_data, mocker):
    """Test that notification is sent when staff hides another user's journey"""
    # Mock journey data - different user_id (2) than the staff member (1)
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Journey owner
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False  # Currently visible
    }

    # Mock staff user (admin)
    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}
    mock_journey_data.update_journey_hidden_status.return_value = 1

    # Mock notification service - patch the import inside the function
    mock_notification_service = mocker.patch('services.notification_service')
    mock_notification_service.create_notification.return_value = (True, "Notification sent", 123)

    success, message = update_journey_hidden_status(1, 1, True)  # Staff user 1 hiding journey owned by user 2

    assert success
    assert message == "Journey set to hidden successfully"

    # Verify notification was sent
    mock_notification_service.create_notification.assert_called_once_with(
        user_id=2,  # Journey owner
        notification_type='edit',
        content="Your journey 'Test Journey' has been hidden by admin_user. It is no longer visible to other users.",
        related_id=1
    )


def test_update_journey_hidden_status_no_notification_for_owner(mock_journey_data, mock_user_data, mocker):
    """Test that no notification is sent when owner hides their own journey"""
    # Mock journey data - same user_id as the person hiding it
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,  # Same as the user making the change
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': False
    }

    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}
    mock_journey_data.update_journey_hidden_status.return_value = 1

    # Mock notification service
    mock_notification_service = mocker.patch('services.notification_service')

    success, message = update_journey_hidden_status(1, 1, True)  # User 1 hiding their own journey

    assert success
    assert message == "Journey set to hidden successfully"

    # Verify no notification was sent
    mock_notification_service.create_notification.assert_not_called()


def test_update_journey_hidden_status_no_notification_for_unhiding(mock_journey_data, mock_user_data, mocker):
    """Test that no notification is sent when unhiding a journey"""
    # Mock journey data - different user_id, but journey is already hidden
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Journey owner
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': '2025-01-01',
        'is_public': False,
        'is_hidden': True  # Already hidden
    }

    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}
    mock_journey_data.update_journey_hidden_status.return_value = 1

    # Mock notification service
    mock_notification_service = mocker.patch('services.notification_service')

    success, message = update_journey_hidden_status(1, 1, False)  # Staff user 1 unhiding journey

    assert success
    assert message == "Journey set to visible successfully"

    # Verify no notification was sent (we only notify on hiding, not unhiding)
    mock_notification_service.create_notification.assert_not_called()


def test_get_private_journeys(mock_journey_data):
    mock_journey_data.get_private_journeys.return_value = [
        {'journey_id': 1, 'title': 'Test Journey', 'description': 'Description', 'user_id': 1}
    ]

    journeys = get_private_journeys(1, limit=10, offset=0)

    assert len(journeys) == 1
    assert journeys[0]['journey_id'] == 1


def test_get_public_journeys(mock_journey_data):
    mock_journey_data.get_public_journeys.return_value = [
        {'journey_id': 1, 'title': 'Test Journey', 'description': 'Description', 'user_id': 1}
    ]

    journeys = get_public_journeys(limit=10, offset=0)

    assert len(journeys) == 1
    assert journeys[0]['journey_id'] == 1
