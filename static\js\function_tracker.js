/**
 * Function Usage Tracker
 * 
 * This script tracks JavaScript function usage in real-time
 * to help identify unused functions and code.
 */

window.FunctionTracker = (function() {
    'use strict';
    
    let isTracking = false;
    let calledFunctions = new Set();
    let originalFunctions = new Map();
    let startTime = null;
    let interactions = [];
    
    // List of functions to track (can be expanded)
    const FUNCTIONS_TO_TRACK = [
        // Event detail page functions
        'smartBack', 'confirmDeleteEvent', 'confirmDeleteComment', 
        'showProtectedEventMessage', 'likeEvent', 'addComment', 
        'deleteComment', 'reportComment',
        
        // Image management functions
        'manageImages', 'uploadImages', 'deleteImage', 'showImageModal',
        'addFirstImage', 'staffManageImages',
        
        // Map and location functions
        'initMap', 'updateMap', 'searchLocation', 'selectLocation',
        'changeLocation', 'editLocationName', 'cancelLocationEdit',
        'confirmLocationChange', 'backToSearch', 'editMapLocation',
        
        // Edit modal functions
        'editEvent', 'validateForm', 'submitForm', 'showModal', 
        'hideModal', 'closeModal', 'openEditModal',
        
        // Form validation functions
        'validateEventForm', 'validateLocationName', 'checkFormChanges',
        'resetForm', 'populateForm',
        
        // Bootstrap and jQuery functions
        'modal', 'dropdown', 'tooltip', 'popover', 'collapse',
        
        // Custom utility functions
        'formatDate', 'formatTime', 'showAlert', 'showToast',
        'debounce', 'throttle', 'sanitizeInput'
    ];
    
    function wrapFunction(obj, funcName, context = 'global') {
        if (typeof obj[funcName] === 'function' && !originalFunctions.has(`${context}.${funcName}`)) {
            const originalFunc = obj[funcName];
            originalFunctions.set(`${context}.${funcName}`, originalFunc);
            
            obj[funcName] = function(...args) {
                if (isTracking) {
                    calledFunctions.add(`${context}.${funcName}`);
                    logInteraction('function_call', `${context}.${funcName}`, {
                        timestamp: Date.now(),
                        args: args.length,
                        context: context
                    });
                }
                return originalFunc.apply(this, args);
            };
        }
    }
    
    function wrapEventHandlers() {
        // Wrap common event handlers
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (isTracking && typeof listener === 'function') {
                const wrappedListener = function(event) {
                    calledFunctions.add(`event.${type}`);
                    logInteraction('event_handler', type, {
                        timestamp: Date.now(),
                        target: event.target.tagName,
                        id: event.target.id,
                        className: event.target.className
                    });
                    return listener.call(this, event);
                };
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            return originalAddEventListener.call(this, type, listener, options);
        };
    }
    
    function wrapJQueryFunctions() {
        if (typeof $ !== 'undefined') {
            const jqueryMethods = ['click', 'submit', 'change', 'load', 'ready', 'on', 'off'];
            jqueryMethods.forEach(method => {
                if ($.fn[method]) {
                    wrapFunction($.fn, method, 'jquery');
                }
            });
        }
    }
    
    function logInteraction(type, name, details) {
        interactions.push({
            type: type,
            name: name,
            details: details,
            timestamp: Date.now() - startTime
        });
    }
    
    function trackElementInteractions() {
        // Track clicks on specific elements
        document.addEventListener('click', function(event) {
            if (!isTracking) return;
            
            const element = event.target;
            const elementInfo = {
                tag: element.tagName,
                id: element.id,
                className: element.className,
                text: element.textContent?.substring(0, 50)
            };
            
            logInteraction('element_click', 'click', elementInfo);
            
            // Track specific button clicks
            if (element.matches('.editEventBtn')) {
                calledFunctions.add('editEventBtn.click');
            }
            if (element.matches('.like-btn')) {
                calledFunctions.add('likeBtn.click');
            }
            if (element.matches('.menu-btn')) {
                calledFunctions.add('menuBtn.click');
            }
        });
    }
    
    return {
        start: function() {
            if (isTracking) return;
            
            console.log('🔍 Starting function usage tracking...');
            isTracking = true;
            startTime = Date.now();
            calledFunctions.clear();
            interactions = [];
            
            // Wrap global functions
            FUNCTIONS_TO_TRACK.forEach(funcName => {
                if (window[funcName]) {
                    wrapFunction(window, funcName, 'window');
                }
            });
            
            // Wrap jQuery functions
            wrapJQueryFunctions();
            
            // Wrap event handlers
            wrapEventHandlers();
            
            // Track element interactions
            trackElementInteractions();
            
            console.log(`✓ Tracking ${FUNCTIONS_TO_TRACK.length} functions`);
        },
        
        stop: function() {
            if (!isTracking) return;
            
            isTracking = false;
            console.log('⏹️ Stopped function usage tracking');
            return this.getReport();
        },
        
        getReport: function() {
            const duration = startTime ? Date.now() - startTime : 0;
            
            return {
                timestamp: new Date().toISOString(),
                duration: duration,
                functionsTracked: FUNCTIONS_TO_TRACK.length,
                functionsCalled: Array.from(calledFunctions),
                functionsNotCalled: FUNCTIONS_TO_TRACK.filter(f => 
                    !calledFunctions.has(`window.${f}`) && 
                    !calledFunctions.has(`jquery.${f}`)
                ),
                interactions: interactions,
                summary: {
                    totalFunctions: FUNCTIONS_TO_TRACK.length,
                    usedFunctions: calledFunctions.size,
                    unusedFunctions: FUNCTIONS_TO_TRACK.length - calledFunctions.size,
                    usagePercentage: (calledFunctions.size / FUNCTIONS_TO_TRACK.length * 100).toFixed(1)
                }
            };
        },
        
        downloadReport: function() {
            const report = this.getReport();
            const blob = new Blob([JSON.stringify(report, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `function_usage_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        },
        
        showReport: function() {
            const report = this.getReport();
            console.group('📊 Function Usage Report');
            console.log('Duration:', report.duration + 'ms');
            console.log('Functions tracked:', report.functionsTracked);
            console.log('Functions called:', report.functionsCalled.length);
            console.log('Usage percentage:', report.summary.usagePercentage + '%');
            console.log('Called functions:', report.functionsCalled);
            console.log('Unused functions:', report.functionsNotCalled);
            console.groupEnd();
            
            return report;
        },
        
        // Utility methods
        isTracking: function() {
            return isTracking;
        },
        
        addFunction: function(funcName) {
            if (!FUNCTIONS_TO_TRACK.includes(funcName)) {
                FUNCTIONS_TO_TRACK.push(funcName);
                if (window[funcName] && isTracking) {
                    wrapFunction(window, funcName, 'window');
                }
            }
        }
    };
})();

// Auto-start tracking when page loads (optional)
document.addEventListener('DOMContentLoaded', function() {
    // Uncomment to auto-start tracking
    // window.FunctionTracker.start();
    
    // Add tracking controls to page (for manual testing)
    if (window.location.search.includes('debug=true')) {
        const controls = document.createElement('div');
        controls.style.cssText = `
            position: fixed; top: 10px; right: 10px; z-index: 9999;
            background: #333; color: white; padding: 10px; border-radius: 5px;
            font-family: monospace; font-size: 12px;
        `;
        controls.innerHTML = `
            <div>Function Tracker</div>
            <button onclick="window.FunctionTracker.start()" style="margin: 2px;">Start</button>
            <button onclick="window.FunctionTracker.stop()" style="margin: 2px;">Stop</button>
            <button onclick="window.FunctionTracker.showReport()" style="margin: 2px;">Report</button>
            <button onclick="window.FunctionTracker.downloadReport()" style="margin: 2px;">Download</button>
        `;
        document.body.appendChild(controls);
    }
});
